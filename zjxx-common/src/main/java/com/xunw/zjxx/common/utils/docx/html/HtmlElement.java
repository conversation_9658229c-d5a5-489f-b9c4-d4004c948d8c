package com.xunw.zjxx.common.utils.docx.html;


public abstract class HtmlElement{
	/**
	 * 优先级，优先级值越大，该标签越靠前
	 */
	protected int priority = 1;
//	protected List<HtmlElement> children;

	public int getPriority() {
		return priority;
	}
//	public List<HtmlElement> getChildren() {
//		return children;
//	}
	public abstract String getStyleStr();
	public abstract String head();
	public abstract String tail();
}
