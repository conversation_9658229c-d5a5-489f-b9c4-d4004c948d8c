package com.xunw.zjxx.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class SystemConfig {

	private static String appId;

	@Value("${system.app-id}")
	private String appIdTemp;

	@Value("${system.openapi-secret-key}")
	private String openApiSecretKey;

	@PostConstruct
	public void init(){
		appId = appIdTemp;
	}

	public String getAppIdTemp() {
		return appIdTemp;
	}

	public void setAppIdTemp(String appIdTemp) {
		this.appIdTemp = appIdTemp;
	}

	public static String getAppId() {
		return appId;
	}

	public static void setAppId(String appId) {
		SystemConfig.appId = appId;
	}

	public String getOpenApiSecretKey() {
		return openApiSecretKey;
	}

	public void setOpenApiSecretKey(String openApiSecretKey) {
		this.openApiSecretKey = openApiSecretKey;
	}
}