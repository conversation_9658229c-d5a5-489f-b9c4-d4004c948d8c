package com.xunw.zjxx.common.utils;

import com.alibaba.fastjson.JSONObject;
import jxl.*;
import jxl.format.*;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.read.biff.BiffException;
import jxl.write.*;
import jxl.write.biff.RowsExceededException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.Boolean;
import java.lang.Number;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;

/**
 * Excel导出工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtil {

    /**
     * 单元格样式枚举
     */
    public enum CellStyle {
        /** 标题样式 */
        TITLE,
        /** 普通单元格样式 */
        NORMAL,
        /** 数值单元格样式 */
        NUMBER,
        /** 日期单元格样式 */
        DATE,
        /** 错误单元格样式 */
        ERROR
    }

    // 默认的日期格式
    private static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    // 默认的数值格式
    private static final String DEFAULT_NUMBER_FORMAT = "0.00";

    // 全局单元格样式缓存
    private final Map<CellStyle, WritableCellFormat> cellStyleCache = new HashMap<>();

    // 工作簿
    private WritableWorkbook workbook;
    // 当前工作表
    private WritableSheet currentSheet;
    // 日期格式化工具
    private SimpleDateFormat dateFormat;
    // 数值格式化工具
    private DecimalFormat numberFormat;

    /**
     * 构造函数
     *
     * @param outputStream 输出流
     * @throws Exception 异常
     */
    public ExcelUtil(OutputStream outputStream) throws Exception {
        this.workbook = Workbook.createWorkbook(outputStream);
        this.dateFormat = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
        this.numberFormat = new DecimalFormat(DEFAULT_NUMBER_FORMAT);
        initCellStyles();
    }

    /**
     * 初始化单元格样式
     *
     * @throws Exception 异常
     */
    private void initCellStyles() throws Exception {
        // 创建标题样式
        WritableFont titleFont = new WritableFont(WritableFont.ARIAL, 11, WritableFont.BOLD);
        WritableCellFormat titleFormat = new WritableCellFormat(titleFont);
        titleFormat.setBackground(Colour.GRAY_25);
        titleFormat.setAlignment(Alignment.CENTRE);
        titleFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        titleFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
        titleFormat.setWrap(true);
        cellStyleCache.put(CellStyle.TITLE, titleFormat);

        // 创建普通单元格样式
        WritableFont normalFont = new WritableFont(WritableFont.ARIAL, 10);
        WritableCellFormat normalFormat = new WritableCellFormat(normalFont);
        normalFormat.setAlignment(Alignment.LEFT);
        normalFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        normalFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
        normalFormat.setWrap(true);
        cellStyleCache.put(CellStyle.NORMAL, normalFormat);

        // 创建数值单元格样式
        WritableCellFormat numberFormat = new WritableCellFormat(normalFont, new NumberFormat(DEFAULT_NUMBER_FORMAT));
        numberFormat.setAlignment(Alignment.RIGHT);
        numberFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        numberFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
        cellStyleCache.put(CellStyle.NUMBER, numberFormat);

        // 创建日期单元格样式
        WritableCellFormat dateFormat = new WritableCellFormat(normalFont, new DateFormat(DEFAULT_DATE_FORMAT));
        dateFormat.setAlignment(Alignment.CENTRE);
        dateFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        dateFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
        cellStyleCache.put(CellStyle.DATE, dateFormat);

        // 创建错误单元格样式
        WritableFont errorFont = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.RED);
        WritableCellFormat errorFormat = new WritableCellFormat(errorFont);
        errorFormat.setAlignment(Alignment.LEFT);
        errorFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
        errorFormat.setBorder(Border.ALL, BorderLineStyle.THIN);
        errorFormat.setWrap(true);
        cellStyleCache.put(CellStyle.ERROR, errorFormat);
    }

    /**
     * 创建新的工作表
     *
     * @param sheetName 工作表名称
     * @param sheetIndex 工作表索引
     * @return 当前工具实例
     */
    public ExcelUtil createSheet(String sheetName, int sheetIndex) {
        this.currentSheet = workbook.createSheet(sheetName, sheetIndex);
        return this;
    }

    /**
     * 设置列宽
     *
     * @param columnIndex 列索引
     * @param width 宽度
     * @return 当前工具实例
     */
    public ExcelUtil setColumnWidth(int columnIndex, int width) {
        currentSheet.setColumnView(columnIndex, width);
        return this;
    }

    /**
     * 设置行高
     *
     * @param rowIndex 行索引
     * @param height 高度
     * @return 当前工具实例
     */
    public ExcelUtil setRowHeight(int rowIndex, int height) throws RowsExceededException {
        currentSheet.setRowView(rowIndex, height * 20);  // 20为单位换算
        return this;
    }

    /**
     * 添加标题行
     *
     * @param rowIndex 行索引
     * @param titles 标题数组
     * @param columnWidths 列宽数组(可选)
     * @return 当前工具实例
     * @throws Exception 写入异常
     */
    public ExcelUtil addTitleRow(int rowIndex, String[] titles, int... columnWidths) throws Exception {
        for (int i = 0; i < titles.length; i++) {
            addCell(i, rowIndex, titles[i], CellStyle.TITLE);

            // 如果提供了列宽，则设置列宽
            if (columnWidths != null && i < columnWidths.length) {
                setColumnWidth(i, columnWidths[i]);
            } else {
                // 默认列宽
                setColumnWidth(i, 15);
            }
        }
        return this;
    }

    /**
     * 添加单元格
     *
     * @param columnIndex 列索引
     * @param rowIndex 行索引
     * @param value 单元格值
     * @param style 样式
     * @return 当前工具实例
     * @throws Exception 写入异常
     */
    public ExcelUtil addCell(int columnIndex, int rowIndex, Object value, CellStyle style) throws Exception {
        if (value == null) {
            currentSheet.addCell(new Label(columnIndex, rowIndex, "", cellStyleCache.get(style)));
            return this;
        }

        if (value instanceof Number) {
            if (style == CellStyle.NUMBER) {
                currentSheet.addCell(new jxl.write.Number(columnIndex, rowIndex, ((Number) value).doubleValue(), cellStyleCache.get(style)));
            } else {
                currentSheet.addCell(new Label(columnIndex, rowIndex, numberFormat.format(value), cellStyleCache.get(style)));
            }
        } else if (value instanceof Date) {
            if (style == CellStyle.DATE) {
                currentSheet.addCell(new DateTime(columnIndex, rowIndex, (Date) value, cellStyleCache.get(style)));
            } else {
                currentSheet.addCell(new Label(columnIndex, rowIndex, dateFormat.format(value), cellStyleCache.get(style)));
            }
        } else if (value instanceof Boolean) {
            currentSheet.addCell(new jxl.write.Boolean(columnIndex, rowIndex, (Boolean) value, cellStyleCache.get(style)));
        } else {
            currentSheet.addCell(new Label(columnIndex, rowIndex, value.toString(), cellStyleCache.get(style)));
        }

        return this;
    }

    /**
     * 添加单元格(使用默认样式)
     *
     * @param columnIndex 列索引
     * @param rowIndex 行索引
     * @param value 单元格值
     * @return 当前工具实例
     * @throws Exception 写入异常
     */
    public ExcelUtil addCell(int columnIndex, int rowIndex, Object value) throws Exception {
        if (value instanceof Number) {
            return addCell(columnIndex, rowIndex, value, CellStyle.NUMBER);
        } else if (value instanceof Date) {
            return addCell(columnIndex, rowIndex, value, CellStyle.DATE);
        } else {
            return addCell(columnIndex, rowIndex, value, CellStyle.NORMAL);
        }
    }

    /**
     * 添加数据行
     *
     * @param rowIndex 行索引
     * @param rowData 行数据(数组)
     * @return 当前工具实例
     * @throws Exception 写入异常
     */
    public ExcelUtil addDataRow(int rowIndex, Object[] rowData) throws Exception {
        for (int i = 0; i < rowData.length; i++) {
            addCell(i, rowIndex, rowData[i]);
        }
        return this;
    }

    /**
     * 添加数据行
     *
     * @param rowIndex 行索引
     * @param rowData 行数据(Map)
     * @param columnMapping 列映射函数
     * @return 当前工具实例
     * @throws Exception 写入异常
     */
    public ExcelUtil addDataRow(int rowIndex, Map<String, Object> rowData, Function<String, Integer> columnMapping) throws Exception {
        for (Map.Entry<String, Object> entry : rowData.entrySet()) {
            Integer columnIndex = columnMapping.apply(entry.getKey());
            if (columnIndex != null) {
                addCell(columnIndex, rowIndex, entry.getValue());
            }
        }
        return this;
    }

    /**
     * 添加错误信息行
     *
     * @param rowIndex 行索引
     * @param errorMessage 错误信息
     * @return 当前工具实例
     * @throws Exception 写入异常
     */
    public ExcelUtil addErrorRow(int rowIndex, String errorMessage) throws Exception {
        currentSheet.mergeCells(0, rowIndex, 5, rowIndex); // 合并前6列
        addCell(0, rowIndex, errorMessage, CellStyle.ERROR);
        return this;
    }

    /**
     * 批量导出数据
     *
     * @param dataList 数据列表
     * @param headers 表头(键名,显示名称)
     * @param columnWidths 列宽
     * @return 当前工具实例
     * @throws Exception 异常
     */
    public ExcelUtil exportData(List<Map<String, Object>> dataList, LinkedHashMap<String, String> headers, int... columnWidths) throws Exception {
        if (currentSheet == null) {
            createSheet("Sheet1", 0);
        }

        // 添加表头
        String[] titles = headers.values().toArray(new String[0]);
        addTitleRow(0, titles, columnWidths);

        // 构建列映射
        final String[] keys = headers.keySet().toArray(new String[0]);
        Function<String, Integer> columnMapping = key -> {
            for (int i = 0; i < keys.length; i++) {
                if (keys[i].equals(key)) {
                    return i;
                }
            }
            return null;
        };

        // 添加数据行
        if (dataList != null && !dataList.isEmpty()) {
            for (int i = 0; i < dataList.size(); i++) {
                addDataRow(i + 1, dataList.get(i), columnMapping);
            }
        } else {
            // 无数据时添加提示
            currentSheet.mergeCells(0, 1, titles.length - 1, 1);
            addCell(0, 1, "没有数据", CellStyle.ERROR);
        }

        return this;
    }

    /**
     * 批量导出JSON数据
     *
     * @param jsonList JSON数据列表
     * @param headers 表头(键名,显示名称)
     * @param columnWidths 列宽
     * @return 当前工具实例
     * @throws Exception 异常
     */
    public ExcelUtil exportJsonData(List<JSONObject> jsonList, LinkedHashMap<String, String> headers, int... columnWidths) throws Exception {
        List<Map<String, Object>> dataList = new ArrayList<>(jsonList.size());
        for (JSONObject json : jsonList) {
            dataList.add(new HashMap<>(json));
        }
        return exportData(dataList, headers, columnWidths);
    }

    /**
     * 创建空工作表(添加提示信息)
     *
     * @param message 提示信息
     * @return 当前工具实例
     * @throws Exception 异常
     */
    public ExcelUtil createEmptySheet(String message) throws Exception {
        if (currentSheet == null) {
            createSheet("Sheet1", 0);
        }

        currentSheet.mergeCells(0, 0, 5, 0);
        addCell(0, 0, StringUtils.isNotEmpty(message) ? message : "未找到符合条件的数据", CellStyle.ERROR);

        return this;
    }

    /**
     * 设置日期格式
     *
     * @param format 日期格式
     * @return 当前工具实例
     */
    public ExcelUtil setDateFormat(String format) {
        this.dateFormat = new SimpleDateFormat(format);
        try {
            // 创建新的格式对象
            WritableFont normalFont = new WritableFont(WritableFont.ARIAL, 10);
            WritableCellFormat newDateFormat = new WritableCellFormat(normalFont, new DateFormat(format));
            newDateFormat.setAlignment(Alignment.CENTRE);
            newDateFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
            newDateFormat.setBorder(Border.ALL, BorderLineStyle.THIN);

            // 替换缓存中的格式
            cellStyleCache.put(CellStyle.DATE, newDateFormat);
        } catch (Exception e) {
            log.error("设置日期格式失败", e);
        }
        return this;
    }

    /**
     * 设置数值格式
     *
     * @param format 数值格式
     * @return 当前工具实例
     */
    public ExcelUtil setNumberFormat(String format) {
        this.numberFormat = new DecimalFormat(format);
        try {
            // 创建新的格式对象而不是修改现有的
            WritableFont normalFont = new WritableFont(WritableFont.ARIAL, 10);
            WritableCellFormat newNumberFormat = new WritableCellFormat(normalFont, new NumberFormat(format));
            newNumberFormat.setAlignment(Alignment.RIGHT);
            newNumberFormat.setVerticalAlignment(VerticalAlignment.CENTRE);
            newNumberFormat.setBorder(Border.ALL, BorderLineStyle.THIN);

            // 替换缓存中的格式
            cellStyleCache.put(CellStyle.NUMBER, newNumberFormat);
        } catch (Exception e) {
            log.error("设置数值格式失败", e);
        }
        return this;
    }

    /**
     * 写入并关闭工作簿
     *
     * @throws Exception 异常
     */
    public void write() throws Exception {
        try {
            workbook.write();
        } finally {
            close();
        }
    }

    /**
     * 关闭工作簿
     *
     * @throws Exception 异常
     */
    public void close() throws Exception {
        if (workbook != null) {
            workbook.close();
            workbook = null;
        }
    }

    /**
     * 设置HTTP响应头，用于文件下载
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     * @throws Exception 异常
     */
    public static void setExcelResponseHeader(HttpServletResponse response, String fileName) throws Exception {
        String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"; filename*=utf-8''" + encodedFileName);
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
    }

    /**
     * 读取Excel文件内容
     *
     * @param file Excel文件
     * @return 读取的数据列表，每行一个Map
     * @throws Exception 异常
     */
    public static List<Map<String, Object>> readExcel(File file) throws Exception {
        return readExcel(file, 0, 1);
    }

    /**
     * 读取Excel文件内容
     *
     * @param file Excel文件
     * @param sheetIndex 工作表索引
     * @param headerRowIndex 表头行索引
     * @return 读取的数据列表，每行一个Map
     * @throws Exception 异常
     */
    public static List<Map<String, Object>> readExcel(File file, int sheetIndex, int headerRowIndex) throws Exception {
        Workbook workbook = null;
        try {
            workbook = Workbook.getWorkbook(file);
            return readExcelData(workbook, sheetIndex, headerRowIndex);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 读取Excel输入流内容
     *
     * @param inputStream Excel输入流
     * @return 读取的数据列表，每行一个Map
     * @throws Exception 异常
     */
    public static List<Map<String, Object>> readExcel(InputStream inputStream) throws Exception {
        return readExcel(inputStream, 0, 1);
    }

    /**
     * 读取Excel输入流内容
     *
     * @param inputStream Excel输入流
     * @param sheetIndex 工作表索引
     * @param headerRowIndex 表头行索引
     * @return 读取的数据列表，每行一个Map
     * @throws Exception 异常
     */
    public static List<Map<String, Object>> readExcel(InputStream inputStream, int sheetIndex, int headerRowIndex) throws Exception {
        Workbook workbook = null;
        try {
            workbook = Workbook.getWorkbook(inputStream);
            return readExcelData(workbook, sheetIndex, headerRowIndex);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 从工作簿读取数据
     *
     * @param workbook 工作簿
     * @param sheetIndex 工作表索引
     * @param headerRowIndex 表头行索引
     * @return 读取的数据列表
     */
    private static List<Map<String, Object>> readExcelData(Workbook workbook, int sheetIndex, int headerRowIndex) {
        Sheet sheet = workbook.getSheet(sheetIndex);
        if (sheet == null) {
            log.warn("Excel文件中不存在索引为{}的工作表", sheetIndex);
            return Collections.emptyList();
        }

        // 获取表头
        int columns = sheet.getColumns();
        String[] headers = new String[columns];
        for (int col = 0; col < columns; col++) {
            Cell headerCell = sheet.getCell(col, headerRowIndex);
            headers[col] = headerCell.getContents();
        }

        // 读取数据行
        List<Map<String, Object>> dataList = new ArrayList<>();
        int rows = sheet.getRows();

        for (int row = headerRowIndex + 1; row < rows; row++) {
            Map<String, Object> rowData = new HashMap<>();
            boolean hasData = false;

            for (int col = 0; col < columns; col++) {
                if (col >= headers.length || headers[col] == null) {
                    continue;
                }

                Cell cell = sheet.getCell(col, row);
                if (cell == null || StringUtils.isEmpty(cell.getContents())) {
                    continue;
                }

                Object cellValue = getCellValue(cell);
                if (cellValue != null) {
                    rowData.put(headers[col], cellValue);
                    hasData = true;
                }
            }

            if (hasData) {
                dataList.add(rowData);
            }
        }

        return dataList;
    }

    /**
     * 三参数函数接口
     */
    @FunctionalInterface
    public interface TriFunction<A, B, C, R> {
        R apply(A a, B b, C c);
    }

    /**
     * 读取Excel文件并自定义单元格处理逻辑
     *
     * @param file Excel文件
     * @param processor 单元格处理器，参数为(行索引,列索引,单元格值)，返回处理后的结果
     * @return 处理后的数据列表
     * @throws Exception 异常
     */
    public static <T> List<T> readExcelWithProcessor(File file, TriFunction<Integer, Integer, Cell, T> processor) throws Exception {
        Workbook workbook = null;
        try {
            workbook = Workbook.getWorkbook(file);
            Sheet sheet = workbook.getSheet(0);
            if (sheet == null) {
                return Collections.emptyList();
            }

            int rows = sheet.getRows();
            int columns = sheet.getColumns();
            List<T> resultList = new ArrayList<>();

            for (int row = 0; row < rows; row++) {
                for (int col = 0; col < columns; col++) {
                    Cell cell = sheet.getCell(col, row);
                    if (cell != null) {
                        T result = processor.apply(row, col, cell);
                        if (result != null) {
                            resultList.add(result);
                        }
                    }
                }
            }
            return resultList;
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 获取单元格的值，根据单元格类型返回不同的Java类型
     *
     * @param cell 单元格
     * @return 单元格值
     */
    private static Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell instanceof NumberCell) {
            return ((NumberCell) cell).getValue();
        } else if (cell instanceof DateCell) {
            return ((DateCell) cell).getDate();
        } else {
            String content = cell.getContents();
            if (StringUtils.isEmpty(content)) {
                return null;
            }
            return content;
        }
    }

    /**
     * 从Excel文件读取指定工作表的所有单元格数据
     *
     * @param file Excel文件
     * @param sheetIndex 工作表索引
     * @return 单元格数据二维数组，第一维是行，第二维是列
     * @throws BiffException Excel格式异常
     * @throws IOException IO异常
     */
    public static Object[][] readExcelToArray(File file, int sheetIndex) throws BiffException, IOException {
        Workbook workbook = null;
        try {
            workbook = Workbook.getWorkbook(file);
            Sheet sheet = workbook.getSheet(sheetIndex);
            if (sheet == null) {
                return new Object[0][0];
            }

            int rows = sheet.getRows();
            int columns = sheet.getColumns();
            Object[][] data = new Object[rows][columns];

            for (int row = 0; row < rows; row++) {
                for (int col = 0; col < columns; col++) {
                    Cell cell = sheet.getCell(col, row);
                    data[row][col] = getCellValue(cell);
                }
            }

            return data;
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 从Excel读取数据并转换为指定类型的对象列表
     *
     * @param file Excel文件
     * @param mapper 将行数据映射到对象的函数
     * @param <T> 目标对象类型
     * @return 对象列表
     * @throws Exception 异常
     */
    public static <T> List<T> readExcelToObjects(File file, Function<Map<String, Object>, T> mapper) throws Exception {
        List<Map<String, Object>> dataList = readExcel(file);
        List<T> resultList = new ArrayList<>(dataList.size());

        for (Map<String, Object> rowData : dataList) {
            T obj = mapper.apply(rowData);
            if (obj != null) {
                resultList.add(obj);
            }
        }

        return resultList;
    }

    /**
     * 验证Excel文件的表头是否符合预期
     *
     * @param file Excel文件
     * @param expectedHeaders 预期的表头
     * @return 如果表头符合预期，返回true；否则返回false
     * @throws Exception 异常
     */
    public static boolean validateExcelHeaders(File file, String[] expectedHeaders) throws Exception {
        Workbook workbook = null;
        try {
            workbook = Workbook.getWorkbook(file);
            Sheet sheet = workbook.getSheet(0);
            if (sheet == null) {
                return false;
            }

            // 检查列数
            if (sheet.getColumns() < expectedHeaders.length) {
                return false;
            }

            // 检查表头内容
            for (int i = 0; i < expectedHeaders.length; i++) {
                Cell cell = sheet.getCell(i, 0);
                if (cell == null || !expectedHeaders[i].equals(cell.getContents())) {
                    return false;
                }
            }

            return true;
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 获取Excel文件中的所有工作表名称
     *
     * @param file Excel文件
     * @return 工作表名称列表
     * @throws Exception 异常
     */
    public static List<String> getSheetNames(File file) throws Exception {
        Workbook workbook = null;
        try {
            workbook = Workbook.getWorkbook(file);
            String[] names = workbook.getSheetNames();
            return Arrays.asList(names);
        } finally {
            if (workbook != null) {
                workbook.close();
            }
        }
    }

    /**
     * 一站式导出Excel到HTTP响应
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     * @param dataList 数据列表
     * @param headers 表头(键名,显示名称)
     * @param columnWidths 列宽
     * @throws Exception 异常
     */
    public static void exportToResponse(
            HttpServletResponse response,
            String fileName,
            List<Map<String, Object>> dataList,
            LinkedHashMap<String, String> headers,
            int... columnWidths) throws Exception {

        setExcelResponseHeader(response, fileName);

        ExcelUtil excelUtil = null;
        try {
            excelUtil = new ExcelUtil(response.getOutputStream());
            excelUtil.createSheet("Sheet1", 0)
                    .exportData(dataList, headers, columnWidths)
                    .write();
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            // 如果已经开始写响应，则不能再修改响应头
            if (excelUtil != null) {
                excelUtil.createEmptySheet("导出失败: " + e.getMessage())
                        .write();
            } else {
                throw e;
            }
        }
    }

    /**
     * 一站式导出JSON数据到HTTP响应
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     * @param jsonList JSON数据列表
     * @param headers 表头(键名,显示名称)
     * @param columnWidths 列宽
     * @throws Exception 异常
     */
    public static void exportJsonToResponse(
            HttpServletResponse response,
            String fileName,
            List<JSONObject> jsonList,
            LinkedHashMap<String, String> headers,
            int... columnWidths) throws Exception {

        List<Map<String, Object>> dataList = new ArrayList<>(jsonList.size());
        for (JSONObject json : jsonList) {
            dataList.add(new HashMap<>(json));
        }

        exportToResponse(response, fileName, dataList, headers, columnWidths);
    }
}
