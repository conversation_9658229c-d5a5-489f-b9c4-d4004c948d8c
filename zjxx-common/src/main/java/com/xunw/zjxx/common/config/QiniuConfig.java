package com.xunw.zjxx.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "qiniu", ignoreUnknownFields = false)
public class QiniuConfig {

    private String accessKey;
    private String secretKey;
    private String bucket;
    private String urlprefix;
    private String zbhfprefix;
    private String imageMogr2Rotate;


    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getUrlprefix() {
        return urlprefix;
    }

    public void setUrlprefix(String urlprefix) {
        this.urlprefix = urlprefix;
    }

    public String getZbhfprefix() {
        return zbhfprefix;
    }

    public void setZbhfprefix(String zbhfprefix) {
        this.zbhfprefix = zbhfprefix;
    }

    public String getImageMogr2Rotate() {
        return imageMogr2Rotate;
    }

    public void setImageMogr2Rotate(String imageMogr2Rotate) {
        this.imageMogr2Rotate = imageMogr2Rotate;
    }
}