package com.xunw.zjxx.common.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.enums.UserTypeEnum;

import java.io.UnsupportedEncodingException;
import java.util.Date;

public class JWTUtils {
	
	 /**
     * 校验token是否正确
     * @param token Token
     * @return boolean 是否正确
     * <AUTHOR>
     * @date 2018/8/31 9:05
     */
    public static boolean verify(String token) {
        try {
            String secret = getSecret(token);
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm).build();
            DecodedJWT jwt = verifier.verify(token);
            return jwt != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * token过期校验，校验token是否过期
     * @param token Token
     * <AUTHOR>
     * @return true 过期   false 未过期
     * @date 2023/07/31 09:53
     */
    public static boolean isExpired(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt().before(new Date());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获得Token中的信息
     * @param token
     * @param claim
     * @return java.lang.String
     * <AUTHOR>
     * @date 2018/9/7 16:54
     */
    public static String getClaim(String token, String claim) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getClaim(claim).asString();
        } catch (Exception e) {
        	return null;
        }
    }

    /**
     * 生成签名
     * @param account 帐号
     * @return java.lang.String 返回加密的Token
     * <AUTHOR>
     * @date 2018/8/31 9:07
     */
    public static String sign(String userId, String username, UserTypeEnum userType, String appId) {
        try {
            // 帐号加JWT私钥加密
            String secret = getSecret(userId, username, userType, appId);
            // 此处过期时间是以毫秒为单位，所以乘以1000
            Date date = new Date(System.currentTimeMillis() + Constants.JWT_TOKEN_EXPIRED_TIME_IN_HOUR * 3600*1000);
            Algorithm algorithm = Algorithm.HMAC256(secret);
            // 附带account帐号信息
            return JWT.create()
                    .withClaim(Constants.AUTH.USER_ID, userId)
                    .withClaim(Constants.AUTH.USERNAME, username)
                    .withClaim(Constants.AUTH.USER_TYPE, userType.name())
                    .withClaim(Constants.AUTH.APP_ID, appId)
                    .withClaim(Constants.AUTH.TIMESTAMP, String.valueOf(System.currentTimeMillis()))
                    .withExpiresAt(date)
                    .sign(algorithm);
        } catch (UnsupportedEncodingException e) {
            throw new BizException("JWTToken加密异常:"+e);
        }
    }
    
    /**
     * 获取token加密秘钥,根据加密规则生成
     */
    protected static String getSecret(String userId, String username, UserTypeEnum userType, String appId) {
        String secret = userId + username + userType + appId + Constants.JWT_TOKEN_ENCRYPTJWTKEY;
        return secret;
    }
    /**
     * 获取token加密秘钥,根据加密规则生成
     */
    protected static String getSecret(String token) {
        return getSecret(getClaim(token, "userId"), getClaim(token, "username"), UserTypeEnum.valueOf(getClaim(token, "userType")), getClaim(token, "appId"));
    }

}
