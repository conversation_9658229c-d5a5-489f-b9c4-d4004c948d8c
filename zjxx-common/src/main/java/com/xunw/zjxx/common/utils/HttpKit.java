package com.xunw.zjxx.common.utils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.omg.PortableInterceptor.SYSTEM_EXCEPTION;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.config.MicroConfig;
import com.xunw.zjxx.common.config.SystemConfig;
import com.xunw.zjxx.common.exception.BizException;

import cn.hutool.core.lang.UUID;

/**
 * https 请求工具类
 * 
 * <AUTHOR>
 * @date 2019-08-19 下午2:40:19
 */
public class HttpKit {

	private static Logger LOGGER = LoggerFactory.getLogger(HttpKit.class);

	private static final String DEFAULT_CHARSET = "UTF-8";

	/**
	 * 请求参数用Map包装
	 */
	public static String get(String url) {
		return get(url, new HashMap<String, String>());
	}

	/**
	 * 请求参数用Map包装
	 */
	public static String get(String url, Map<String, String> params) {
		return requst(url, params, "GET");
	}

	/**
	 * 请求参数是key1=value1&key2=value2
	 */
	public static String get(String url, String params) {
		return requst(url, params, "GET");
	}

	/**
	 * 请求参数用Map包装
	 */
	public static String post(String url) {
		return post(url, new HashMap<String, String>());
	}

	/**
	 * 请求参数用Map包装
	 */
	public static String post(String url, Map<String, String> params) {
		return requst(url, params, "POST");
	}

	/**
	 * 请求参数是key1=value1&key2=value2
	 */
	public static String post(String url, String params) {
		return requst(url, params, "POST");
	}

	private static String requst(String url, Map<String, String> paramters, String method) {
		String params = "";
		if (paramters != null && paramters.size() > 0) {
			for (String key : paramters.keySet()) {
				params += (key + "=" + paramters.get(key) + "&");
			}
			params = params.substring(0, params.length() - 1);
		}
		return requst(url, params, method);
	}

	private static String requst(String url, String params, String method) {
		System.out.println("------request params:" + params);
		String result = null;
		InputStream is = null;
		try {
			if ("GET".equals(method) && StringUtils.isNotEmpty(params)) {
				if (url.indexOf("?") > -1) {
					url += ("&" + params);
				} else {
					url += ("?" + params);
				}
			}
			URL realUrl = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			conn.setRequestProperty("Accept-Charset", DEFAULT_CHARSET);
			conn.setConnectTimeout(180000);
			conn.setReadTimeout(180000);
			if ("GET".equals(method)) {
				conn.setRequestMethod("GET");
				conn.setDoInput(true);
			} else if ("POST".equals(method)) {
				conn.setRequestMethod("POST");
				conn.setDoInput(true);
				conn.setDoOutput(true);
				if (StringUtils.isNotEmpty(params)) {
					byte[] postDataBytes = params.getBytes(DEFAULT_CHARSET);
					conn.setRequestProperty("Content-Length", String.valueOf(postDataBytes.length));
					conn.getOutputStream().write(postDataBytes);
				}
			}

			// 根据responseCode来获取输入流，此处错误响应码的响应体内容也要获取（看服务端的返回结果形式决定）
			if (HttpURLConnection.HTTP_OK == conn.getResponseCode()) {
				is = conn.getInputStream();
			} else {
				is = conn.getErrorStream();
			}
			BufferedReader in = new BufferedReader(new InputStreamReader(is, DEFAULT_CHARSET));
			StringBuffer buffer = new StringBuffer();
			String line = "";
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
			result = buffer.toString();
			conn.disconnect();
		} catch (Exception e) {
			LOGGER.error("requst error:", e);
		} finally {
			if (is != null) {
				IOUtils.closeQuietly(is);
			}
		}
		return result;
	}

	public static String post(String requestUrl, String contentType, String params) throws Exception {
		String encoding = "UTF-8";
		if (requestUrl.contains("nlp")) {
			encoding = "GBK";
		}
		return HttpKit.postGeneralUrl(requestUrl, contentType, params, encoding);
	}

	public static String postGeneralUrl(String generalUrl, String contentType, String params, String encoding)
			throws Exception {
		URL url = new URL(generalUrl);
		// 打开和URL之间的连接
		HttpURLConnection connection = (HttpURLConnection) url.openConnection();
		connection.setRequestMethod("POST");
		// 设置通用的请求属性
		connection.setRequestProperty("Content-Type", contentType);
		connection.setRequestProperty("Connection", "Keep-Alive");
		connection.setUseCaches(false);
		connection.setDoOutput(true);
		connection.setDoInput(true);

		// 得到请求的输出流对象
		DataOutputStream out = new DataOutputStream(connection.getOutputStream());
		out.write(params.getBytes(encoding));
		out.flush();
		out.close();

		// 建立实际的连接
		connection.connect();
		// 获取所有响应头字段
		Map<String, List<String>> headers = connection.getHeaderFields();
		// 遍历所有的响应头字段
		for (String key : headers.keySet()) {
			System.err.println(key + "--->" + headers.get(key));
		}
		// 定义 BufferedReader输入流来读取URL的响应
		BufferedReader in = null;
		in = new BufferedReader(new InputStreamReader(connection.getInputStream(), encoding));
		String result = "";
		String getLine;
		while ((getLine = in.readLine()) != null) {
			result += getLine;
		}
		in.close();
		System.err.println("result:" + result);
		return result;
	}

	/**
	 * 用于微服务调用 不带token
	 */
	public static String microPost(String generalUrl, Map<String, Object> param) {
		return microPost(generalUrl, param, null);
	}

	/**
	 * 用于微服务调用 form表单请求
	 */
	public static String microPost(String generalUrl, Map<String, Object> param, String token) {
		JSONObject object = null;
		InputStream is = null;
		try {
			MicroConfig microConfig = SpringBeanUtils.getBean(MicroConfig.class);
			String gatewayUrl = microConfig.getGatewayUrl().endsWith("/") ? microConfig.getGatewayUrl()
					: microConfig.getGatewayUrl() + "/";
			generalUrl = generalUrl.startsWith("/") ? generalUrl.substring(1) : generalUrl;
			String apiURL = gatewayUrl + generalUrl;
			URL realUrl = new URL(apiURL);
			HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
			conn.setRequestProperty("Accept-Charset", DEFAULT_CHARSET);
			if (StringUtils.isNotEmpty(token)) {
				conn.setRequestProperty(Constants.AUTH.AUTHORIZATION, token);
			}
			conn.setRequestProperty(Constants.AUTH.APP_ID, SystemConfig.getAppId());
			conn.setConnectTimeout(180000);
			conn.setReadTimeout(180000);
			conn.setRequestMethod("POST");
			conn.setDoInput(true);
			conn.setDoOutput(true);
			if (param != null && !param.isEmpty()) {
				StringBuffer paramStr = new StringBuffer();
				param.forEach((k, v) -> {
					paramStr.append(k).append("=");
					if (v != null) {
						paramStr.append(v);
					}
					paramStr.append("&");
				});
				String p = paramStr.substring(0, paramStr.length() - 1);
				byte[] postDataBytes = p.getBytes(DEFAULT_CHARSET);
				conn.setRequestProperty("Content-Length", String.valueOf(postDataBytes.length));
				conn.getOutputStream().write(postDataBytes);
			}
			// 根据responseCode来获取输入流，此处错误响应码的响应体内容也要获取（看服务端的返回结果形式决定）
			if (HttpURLConnection.HTTP_OK == conn.getResponseCode()) {
				is = conn.getInputStream();
			} else {
				is = conn.getErrorStream();
			}
			BufferedReader in = new BufferedReader(new InputStreamReader(is, DEFAULT_CHARSET));
			StringBuffer buffer = new StringBuffer();
			String line = "";
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
			String result = buffer.toString();
			conn.disconnect();
			object = JSONObject.parseObject(result);
			if (object != null && object.getInteger("code").intValue() == 0) {
				return object.toJSONString();
			}
		} catch (Exception e) {
			LOGGER.error("requst error:", e);
		} finally {
			if (is != null) {
				IOUtils.closeQuietly(is);
			}
		}
		throw BizException.withMessage(
				object != null ? object.getInteger("code") : BizException.SYSTEM_EXCEPTION.getCode(),
				object != null && StringUtils.isNotEmpty(object.getString("message")) ? object.getString("message") : "调用微服务异常！");
	}

	/**
	 * 用于微服务调用 JSON请求协议
	 */
	public static String microJSONPost(String generalUrl, String param, String token) {
		JSONObject object = null;
		InputStream is = null;
		try {
			MicroConfig microConfig = SpringBeanUtils.getBean(MicroConfig.class);
			String gatewayUrl = microConfig.getGatewayUrl().endsWith("/") ? microConfig.getGatewayUrl()
					: microConfig.getGatewayUrl() + "/";
			generalUrl = generalUrl.startsWith("/") ? generalUrl.substring(1) : generalUrl;
			String apiURL = gatewayUrl + generalUrl;
			URL realUrl = new URL(apiURL);
			HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
			conn.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
			conn.setRequestProperty("Accept-Charset", DEFAULT_CHARSET);
			if (StringUtils.isNotEmpty(token)) {
				conn.setRequestProperty(Constants.AUTH.AUTHORIZATION, token);
			}
			conn.setRequestProperty(Constants.AUTH.APP_ID, SystemConfig.getAppId());
			conn.setConnectTimeout(180000);
			conn.setReadTimeout(180000);
			conn.setRequestMethod("POST");
			conn.setDoInput(true);
			conn.setDoOutput(true);
			if (StringUtils.isNotEmpty(param)) {
				conn.getOutputStream().write(param.getBytes(StandardCharsets.UTF_8));
			}
			// 根据responseCode来获取输入流，此处错误响应码的响应体内容也要获取（看服务端的返回结果形式决定）
			if (HttpURLConnection.HTTP_OK == conn.getResponseCode()) {
				is = conn.getInputStream();
			} else {
				is = conn.getErrorStream();
			}
			BufferedReader in = new BufferedReader(new InputStreamReader(is, DEFAULT_CHARSET));
			StringBuffer buffer = new StringBuffer();
			String line = "";
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
			String result = buffer.toString();
			conn.disconnect();
			object = JSONObject.parseObject(result);
			if (object != null && object.getInteger("code").intValue() == 0) {
				return object.toJSONString();
			}
		} catch (Exception e) {
			LOGGER.error("requst error:", e);
		} finally {
			if (is != null) {
				IOUtils.closeQuietly(is);
			}
		}
		throw BizException.withMessage(
				object != null ? object.getInteger("code") : BizException.SYSTEM_EXCEPTION.getCode(),
				object != null && StringUtils.isNotEmpty(object.getString("message")) ? object.getString("message") : "调用微服务异常！");
	}

	/**
	 * 用于微服务调用 JSON请求协议 不带token
	 */
	public static String microJSONPost(String generalUrl, String param) {
		return microJSONPost(generalUrl, param, null);
	}

	/**
	 * 微服务文件上传
	 */
	public static String microRequestFile(String generalUrl, Map<String, String> textMap, Map<String, String> fileMap,
			String token) {
		HttpURLConnection conn = null;
		String BOUNDARY = "-----------------12345654321-----------";
		JSONObject object = null;
		InputStream is = null;
		try {
			MicroConfig microConfig = SpringBeanUtils.getBean(MicroConfig.class);
			String gatewayUrl = microConfig.getGatewayUrl().endsWith("/") ? microConfig.getGatewayUrl()
					: microConfig.getGatewayUrl() + "/";
			generalUrl = generalUrl.startsWith("/") ? generalUrl.substring(1) : generalUrl;
			String apiURL = gatewayUrl + generalUrl;
			URL url = new URL(apiURL);
			conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(5000);
			conn.setReadTimeout(30000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			if (StringUtils.isNotEmpty(token)) {
				conn.setRequestProperty(Constants.AUTH.AUTHORIZATION, token);
			}
			conn.setRequestProperty(Constants.APPID, Constants.AUTH.APP_ID);
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
			conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
			conn.setRequestProperty("Charset", "UTF-8");
			OutputStream out = new DataOutputStream(conn.getOutputStream());
			if (textMap != null) {
				StringBuffer strBuf = new StringBuffer();
				Iterator iter = textMap.entrySet().iterator();
				while (iter.hasNext()) {
					Map.Entry entry = (Map.Entry) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"").append(inputName).append("\"\r\n\r\n");
					strBuf.append(inputValue);
				}
				out.write(strBuf.toString().getBytes());
			}
			if (fileMap != null) {
				String contentType = "multipart/form-data";
				Iterator iter = fileMap.entrySet().iterator();
				while (iter.hasNext()) {
					Map.Entry entry = (Map.Entry) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					File file = new File(inputValue);
					String filename = file.getName();
					StringBuffer strBuf = new StringBuffer();
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"").append(inputName).append("\"; filename=\"")
							.append(filename).append("\"\r\n");
					strBuf.append("Content-Type:").append(contentType).append("\r\n\r\n");
					out.write(strBuf.toString().getBytes());
					DataInputStream in = new DataInputStream(Files.newInputStream(file.toPath()));
					int bytes = 0;
					byte[] bufferOut = new byte[1024];
					while ((bytes = in.read(bufferOut)) != -1) {
						out.write(bufferOut, 0, bytes);
					}
					in.close();
				}
			}
			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();
			// 根据responseCode来获取输入流，此处错误响应码的响应体内容也要获取（看服务端的返回结果形式决定）
			if (HttpURLConnection.HTTP_OK == conn.getResponseCode()) {
				is = conn.getInputStream();
			} else {
				is = conn.getErrorStream();
			}
			BufferedReader in = new BufferedReader(new InputStreamReader(is, DEFAULT_CHARSET));
			StringBuffer buffer = new StringBuffer();
			String line = "";
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
			String result = buffer.toString();
			conn.disconnect();
			object = JSONObject.parseObject(result);
			if (object !=null && object.getInteger("code").intValue() == 0) {
				return object.toJSONString();
			}
		} catch (Exception e) {
			LOGGER.error("requst error:", e);
		} finally {
			if (is != null) {
				IOUtils.closeQuietly(is);
			}
		}
		throw BizException.withMessage(
				object != null ? object.getInteger("code") : BizException.SYSTEM_EXCEPTION.getCode(),
				object != null && StringUtils.isNotEmpty(object.getString("message")) ? object.getString("message") : "调用微服务异常！");
	}

	/**
	 * 微服务文件上传 注意textMap必须是Map<String, String>类型  田军 2023-09-14备注
	 */
	public static String microRequestStream(String generalUrl, Map<String, String> textMap,
			Map<String, InputStream> fileMap, String token) {
		HttpURLConnection conn = null;
		String BOUNDARY = "-----------------12345654321-----------";
		JSONObject object = null;
		InputStream is = null;
		try {
			MicroConfig microConfig = SpringBeanUtils.getBean(MicroConfig.class);
			String gatewayUrl = microConfig.getGatewayUrl().endsWith("/") ? microConfig.getGatewayUrl()
					: microConfig.getGatewayUrl() + "/";
			generalUrl = generalUrl.startsWith("/") ? generalUrl.substring(1) : generalUrl;
			String apiURL = gatewayUrl + generalUrl;
			URL url = new URL(apiURL);
			conn = (HttpURLConnection) url.openConnection();
			conn.setConnectTimeout(5000);
			conn.setReadTimeout(30000);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setRequestMethod("POST");
			if (StringUtils.isNotEmpty(token)) {
				conn.setRequestProperty(Constants.AUTH.AUTHORIZATION, token);
			}
			conn.setRequestProperty(Constants.APPID, Constants.AUTH.APP_ID);
			conn.setRequestProperty("Connection", "Keep-Alive");
			conn.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
			conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + BOUNDARY);
			conn.setRequestProperty("Charset", "UTF-8");
			OutputStream out = new DataOutputStream(conn.getOutputStream());
			if (textMap != null) {
				StringBuffer strBuf = new StringBuffer();
				Iterator iter = textMap.entrySet().iterator();
				while (iter.hasNext()) {
					Map.Entry entry = (Map.Entry) iter.next();
					String inputName = (String) entry.getKey();
					String inputValue = (String) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"").append(inputName).append("\"\r\n\r\n");
					strBuf.append(inputValue);
				}
				out.write(strBuf.toString().getBytes());
			}
			if (fileMap != null) {
				String contentType = "multipart/form-data";
				Iterator iter = fileMap.entrySet().iterator();
				while (iter.hasNext()) {
					Map.Entry entry = (Map.Entry) iter.next();
					String inputName = (String) entry.getKey();
					InputStream inputValue = (InputStream) entry.getValue();
					if (inputValue == null) {
						continue;
					}
					String filename = UUID.randomUUID().toString();// 使用随机文件名
					StringBuffer strBuf = new StringBuffer();
					strBuf.append("\r\n").append("--").append(BOUNDARY).append("\r\n");
					strBuf.append("Content-Disposition: form-data; name=\"").append(inputName).append("\"; filename=\"")
							.append(filename).append("\"\r\n");
					strBuf.append("Content-Type:").append(contentType).append("\r\n\r\n");
					out.write(strBuf.toString().getBytes());
					DataInputStream in = new DataInputStream(inputValue);
					int bytes = 0;
					byte[] bufferOut = new byte[1024];
					while ((bytes = in.read(bufferOut)) != -1) {
						out.write(bufferOut, 0, bytes);
					}
					in.close();
				}
			}
			byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
			out.write(endData);
			out.flush();
			out.close();
			// 根据responseCode来获取输入流，此处错误响应码的响应体内容也要获取（看服务端的返回结果形式决定）
			if (HttpURLConnection.HTTP_OK == conn.getResponseCode()) {
				is = conn.getInputStream();
			} else {
				is = conn.getErrorStream();
			}
			BufferedReader in = new BufferedReader(new InputStreamReader(is, DEFAULT_CHARSET));
			StringBuffer buffer = new StringBuffer();
			String line = "";
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
			String result = buffer.toString();
			conn.disconnect();
			object = JSONObject.parseObject(result);
			if (object != null && object.getInteger("code").intValue() == 0) {
				return object.toJSONString();
			}
		} catch (Exception e) {
			LOGGER.error("requst error:", e);
		} finally {
			if (is != null) {
				IOUtils.closeQuietly(is);
			}
		}
		throw BizException.withMessage(
				object != null ? object.getInteger("code") : BizException.SYSTEM_EXCEPTION.getCode(),
				object != null && StringUtils.isNotEmpty(object.getString("message")) ? object.getString("message") : "调用微服务异常！");
	}
}
