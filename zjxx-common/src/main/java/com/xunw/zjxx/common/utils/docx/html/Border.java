package com.xunw.zjxx.common.utils.docx.html;


public class Border extends HtmlElement{

	private String style;
	private String width;
	private String color;

	public Border(String style, String width, String color) {
		this.style = style;
		this.width = width;
		this.color = color;
		priority = 10;
	}

	@Override
	public String head() {
		return "<span style=\"border-style:" + style + ";border-width:" + width + ";" + (color.equals("") ? "" : ("border-color:" + color + ";")) + "\">";
	}

	@Override
	public String tail() {
		return "</span>";
	}

	@Override
	public String getStyleStr() {
		return "";
//		return "border-style:" + style + ";border-width:" + width + ";" + (color.equals("") ? "" : ("border-color:" + color + ";"));
	}

}
