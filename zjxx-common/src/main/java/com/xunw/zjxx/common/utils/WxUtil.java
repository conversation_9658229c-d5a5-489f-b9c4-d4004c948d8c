package com.xunw.zjxx.common.utils;

import com.alibaba.fastjson.JSONObject;

/**
 * 微信获取openId帮助
 */

public class WxUtil {
    /**
     * 微信根据code获取openId
     */
    public static JSONObject getOpenIdByCode(String appId, String secret, String code) {
        String url = String.format("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code", appId, secret, code);
        String reslutStr = HttpKit.get(url);
        return JSONObject.parseObject(reslutStr);
    }

}
