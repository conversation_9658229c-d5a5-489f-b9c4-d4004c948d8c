package com.xunw.zjxx.common.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;
import java.util.Objects;

/**
 * 投诉类型
 */
public enum TypeEnum implements IEnum {

	TS("投诉", "0"),
	JY("建议", "1"),
	BY("表扬", "2");

    private String name;

    private String id;

    private TypeEnum(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static TypeEnum findById(String id) {
        for (TypeEnum status : TypeEnum.values()) {
            if (Objects.equals(status.id, id)) {
                return status;
            }
        }
        return null;
    }

	public static TypeEnum findByEnumName(String name) {
		for (TypeEnum status : TypeEnum.values()) {
			if (status.name().equals(name)) {
				return status;
			}
		}
		return null;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	@Override
	public Serializable getValue() {
		return this.name();
	}
    
}
