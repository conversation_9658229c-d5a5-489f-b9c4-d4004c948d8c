package com.xunw.zjxx.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
@PropertySource(value = "classpath:micro-service.properties", ignoreResourceNotFound = true)
public class MicroConfig {

    @Value("${gateway.url}")
    private String gatewayUrl;
    
    @Bean
    @ConfigurationProperties(prefix = "login-service", ignoreUnknownFields = false)
    public LoginServiceConfig loginServiceConfig() {
        return new LoginServiceConfig();
    }
    
    @Bean
    @ConfigurationProperties(prefix = "user-auth-service", ignoreUnknownFields = false)
    public UserAuthServiceConfig userAuthServiceConfig() {
    	return new UserAuthServiceConfig();
    }
    
    @Bean
    @ConfigurationProperties(prefix = "learn-service", ignoreUnknownFields = false)
    public LearnServiceConfig learnServiceConfig() {
    	return new LearnServiceConfig();
    }
    
    @Bean
    @ConfigurationProperties(prefix = "exam-service", ignoreUnknownFields = false)
    public ExamServiceConfig examServiceConfig() {
    	return new ExamServiceConfig();
    }
    
    @Bean
    @ConfigurationProperties(prefix = "face-service", ignoreUnknownFields = false)
    public FaceServiceConfig faceServiceConfig() {
    	return new FaceServiceConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "basic-service", ignoreUnknownFields = false)
    public BasicServiceConfig basicServiceConfig() {
        return new BasicServiceConfig();
    }

	public String getGatewayUrl() {
		return gatewayUrl;
	}

	public void setGatewayUrl(String gatewayUrl) {
		this.gatewayUrl = gatewayUrl;
	}
}
