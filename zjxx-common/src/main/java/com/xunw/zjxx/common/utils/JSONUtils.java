package com.xunw.zjxx.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cglib.beans.BeanMap;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

/**
 * json 工具类
 * 
 * <AUTHOR>
 *
 */
public class JSONUtils {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(JSONUtils.class);
	
	public static String toString(Object object) throws JsonProcessingException {
		ObjectMapper mapper = new ObjectMapper();
		mapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
		mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
		mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));  
		String value = mapper.writeValueAsString(object);
		return value;
	}

	public static <T> T toBean(String content, Class<T> cls)
			throws JsonParseException, JsonMappingException, IOException {
		ObjectMapper mapper = new ObjectMapper();
		mapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
		mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
		mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
		mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));  
		return mapper.readValue(content, cls);
	}
	
	/**
	 * 将对象转换成map
	 */
	public static <T> Map<String, Object> convertBeanToMap(T object){
		try {
			Map<String, Object> map = new HashMap<>();
			BeanMap beanMap = BeanMap.create(object);
            for (Object key : beanMap.keySet()) {
                map.put(key+"", beanMap.get(key));
            }
			return map;
		} catch (Exception e) {
			LOGGER.error("convertObjectToMap Error",e);
			return null;
		}
	}
	
	/**
	 * 将对象转换成map 并将所有的key转成小写
	 */
	public static Map<String, Object> convertBeanToMapWithLowerKey(Object object){
		try {
			Map<String, Object> map = new HashMap<>();
			BeanMap beanMap = BeanMap.create(object);
            for (Object key : beanMap.keySet()) {
                map.put((key+"").toLowerCase(), beanMap.get(key));
            }
			return map;
		} catch (Exception e) {
			LOGGER.error("convertObjectToMapWithLowerKey Error",e);
			return null;
		}
	}
	
	/**
	 * 将对象数组转换成 map 数组 并将所有的key转成小写
	 */
	public static java.util.List<Map<String, Object>> convertBeanListToMapListWithLowerKey(java.util.List list){
		try {
			java.util.List<Map<String, Object>> resultList = new ArrayList<>();
			for (Object object : list) {
				Map<String, Object> map = new HashMap<>();
				
				BeanMap beanMap = BeanMap.create(object);
	            for (Object key : beanMap.keySet()) {
	                map.put((key+"").toLowerCase(), beanMap.get(key));
	            }
				resultList.add(map);
			}
			return resultList;
		} catch (Exception e) {
			LOGGER.error("convertListToMapWithLowerKey Error",e);
			return null;
		}
	}
	
	
	
	

}
