package com.xunw.zjxx.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

/**
 * spring bean 工具类
 *
 * <AUTHOR>
 */
@Component
public class SpringBeanUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }

    public static void loadApplicationContext(String[] xmlConfig) throws Exception {
        applicationContext = new ClassPathXmlApplicationContext(xmlConfig);
    }

    public static void loadApplicationContext(ServletContext servletContext) throws Exception {
        applicationContext = WebApplicationContextUtils.getWebApplicationContext(servletContext);
    }

    public static ApplicationContext getApplicationContext() {
        if (applicationContext == null) {
            throw new IllegalStateException(
                    "'applicationContext' property is null,ApplicationContextHolder not yet init.");
        }
        return applicationContext;
    }

    @SuppressWarnings("unchecked")
    public static <T> T getBean(String beanName) {
        return (T) getApplicationContext().getBean(beanName);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    public static <T> T getBean(Class clazz) {
        return (T) getApplicationContext().getBean(clazz);
    }
}
