package com.xunw.zjxx.common.utils.docx.html;


/**
 * 背景，现只处理背景色
 * <AUTHOR>
 *
 */
public class Background extends HtmlElement{
	private String color;

	public Background(String color) {
		this.color = color;
		this.priority = 4;
	}

	@Override
	public String head() {
		return "<span" + (color.equals("") ? "" : (" style=\"background-color:" + color + ";\"")) + ">";
	}

	@Override
	public String tail() {
		return "</span>";
	}

	@Override
	public String getStyleStr() {
		return color.equals("") ? "" : ("background-color:" + color + ";");
	}
}
