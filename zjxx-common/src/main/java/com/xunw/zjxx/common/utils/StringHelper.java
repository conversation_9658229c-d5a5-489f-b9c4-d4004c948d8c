package com.xunw.zjxx.common.utils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/********
 * 
 * <b>String工具类</b>
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date May 12, 2014 8:53:38 AM
 */
public class StringHelper {

	/******************
	 * 判断字符串是否为空,空白字符串认为为空
	 */
	public static boolean isEmpty(Object obj) {
		return obj == null ? true : (obj.toString().trim().length() == 0 ? true : false);
	}

	/*****************
	 * 判断字符串是否不为空,空白字符串认为为空
	 */
	public static boolean isNotEmpty(Object obj) {
		return !isEmpty(obj);
	}

	/*******************
	 * 数据库字段名称转换为java类属性 CUST_NAME => CustName;
	 */
	public static String toClassName(String columnName) {
		if (isEmpty(columnName)) {
			return "";
		}
		StringBuilder sb = new StringBuilder();
		for (String s : columnName.split("_")) {
			if (s.length() > 0) {
				sb.append(s.substring(0, 1).toUpperCase());
			}
			if (s.length() > 1) {
				sb.append(s.substring(1).toLowerCase());
			}
		}
		return sb.toString();
	}

	/**********************************
	 * 数据库字段名称转换为java类属性 CUST_NAME => custName;
	 */
	public static String toFieldName(String columnName) {
		return lowerFirstChar(toClassName(columnName));

	}

	/**********************************************
	 * java类属性名称转换成数据库名称 custName=>CUST_NAME
	 */
	public static String toColumnName(String fieldName) {
		if (isEmpty(fieldName)) {
			return "";
		}
		StringBuilder sb = new StringBuilder(Character.toUpperCase(fieldName.charAt(0)));
		for (int i = 1; i < fieldName.length(); i++) {
			if (Character.isUpperCase(fieldName.charAt(i))) {
				sb.append("_");
			}
			sb.append(Character.toUpperCase(fieldName.charAt(i)));
		}
		return sb.toString().toUpperCase();
	}

	/***************************************
	 * 首字母大写
	 */
	public static String upperFirstChar(String source) {
		if (isEmpty(source)) {
			return "";
		}
		if (source.length() == 1) {
			return source.toUpperCase();
		}
		return source.substring(0, 1).toUpperCase() + source.substring(1);
	}

	/*****************************************
	 * 首字母小写
	 */
	public static String lowerFirstChar(String source) {
		if (isEmpty(source)) {
			return "";
		}
		if (source.length() == 1) {
			return source.toUpperCase();
		}
		return source.substring(0, 1).toUpperCase() + source.substring(1);
	}

	/*****************************************
	 * 分割不定长度的空白字符
	 */
	public static String[] splitWithSpace(String source) {
		return source.split("\\s+");
	}

	/***************************************************
	 * 左边补齐
	 */

	public static String leftPadding(String source, int length, char padding) {
		if (source == null) {
			source = "";
		}
		String value = "";
		for (int i = 0; i < length - source.length(); i++) {
			value += padding;
		}
		return value + source;
	}

	/***************************************************
	 * 左边补齐
	 */

	public static String rightPadding(String source, int length, String padding) {
		if (source == null) {
			source = "";
		}
		String value = "";
		for (int i = 0; i < length - source.getBytes().length; i++) {
			value += padding;
		}
		return source + value;
	}

	/*************************
	 * 拼接字符串
	 */
	public static String join(String[] array, String joinString) {
		if (array == null || array.length == 0) {
			return "";
		}
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < array.length - 1; i++) {
			sb.append(array[i] + joinString);
		}
		sb.append(array[array.length - 1]);
		return sb.toString();
	}

	/*************************
	 * 拼接字符串
	 */
	public static String join(List<?> list, String joinString) {
		if (list == null || list.size() == 0) {
			return "";
		}
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < list.size() - 1; i++) {
			sb.append(list.get(i) + joinString);
		}
		sb.append(list.get(list.size() - 1));
		return sb.toString();
	}

	public static Map<String, String> stringToMap(String source, String recordSplit, String attrSplit) {
		Map<String, String> map = new HashMap<String, String>();
		if (isEmpty(source)) {
			return map;
		}
		String[] array = source.split(recordSplit);
		for (String s : array) {
			String[] arr = s.split(attrSplit);
			if (arr.length == 1) {
				map.put(arr[0], "");
			} else {
				map.put(arr[0], s.substring(arr[0].length()));
			}
		}
		return map;
	}
	
	public static String delStartChar(String src ,int len){
		if(null != src && src.length() > 0){
			src = src.substring( len );
		}
		return src;
	}

	public static String generateOnlyCode(int std_id){
		return String.format("%08d", std_id);
	}
	
	public static final String trim2Str(Object str,String defaultValue){
		if(str == null){
			return defaultValue;
		}
		String str2 = null;
		if(str instanceof String){
			str2 = ((String)str).trim();
		}else{
			str2 = str.toString().trim();
		}
		return str2.length() == 0 ? defaultValue:str2;
	}
	
	public static final int trim2Int(Object obj,int defaultValue){
		if(obj == null){
			return defaultValue;
		}
		if(obj instanceof Integer){
			return (Integer)obj;
		}
		try{
			return Integer.parseInt(obj.toString());
		}catch (Exception e) {
			return defaultValue;
		}
	}
	
	public static final double trim2Double(Object obj,double defaultValue){
		if(obj == null){
			return defaultValue;
		}
		if(obj instanceof Integer){
			return (Integer)obj;
		}
		if(obj instanceof Long){
			return (Long)obj;
		}
		if(obj instanceof Float){
			return (Float)obj;
		}
		if(obj instanceof Double){
			return (Double)obj;
		}
		try{
			return Double.parseDouble(obj.toString());
		}catch (Exception e) {
			return defaultValue;
		}
	}
	public static boolean isEmpty1(String str) {
		if (str==null || "".equals(str)) {
			return true;
		}
		return false;
	}
	public static boolean isNotEmpty1(String str) {
		if (str!=null && !"".equals(str)) {
			return true;
		}
		return false;
	}
}
