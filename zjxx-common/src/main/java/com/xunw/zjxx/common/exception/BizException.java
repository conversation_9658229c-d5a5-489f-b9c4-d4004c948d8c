package com.xunw.zjxx.common.exception;

/**
 * 系统异常信息
 * <AUTHOR>
 *
 */
public class BizException extends RuntimeException {

    private static final long serialVersionUID = -8007657202243426183L;

    public static final BizException SYSTEM_EXCEPTION = new BizException(-500, "系统错误");
    public static final BizException LOGIN_REQUIRED = new BizException(-100, "用户未登录");
    public static final BizException ACCOUNT_HAS_BEEN_LOGINED = new BizException(-99, "账号已被登录");
    public static final BizException PARAMS_ERROR = new BizException(-101, "参数缺失或者参数错误");
    public static final BizException UN_AUTHORIZATION = new BizException(-102, "当前用户没有权限进行此操作");
    public static final BizException USERNAME_OR_PASSWORD_ERROR = new BizException(-103, "账号或者密码错误");
    public static final BizException SNED_CODE_TOO_MUCH = new BizException(-105, "发送验证码过于频繁，请60秒之后再试");

    public static final BizException OPEN_API_MISSING_PARAM = new BizException(-901,"open api 参数缺失");
    public static final BizException OPEN_API_SIGN_ERROR = new BizException(-903,"签名错误");
    public static final BizException REQUIRED_OPERATION_NNOTATION = new BizException(-106, "方法定义错误(无operation注解)");
    
    //订单已经支付
    public static final BizException ORDER_HAS_PAYED = new BizException(-904,"订单已经支付）");
    //完善信息
    public static final BizException INVOICE_PERFECT_MESSAGE = new BizException(-801,"完善发票信息）");

    public static final BizException BAD_REQUEST = new BizException(-301, "非法请求");

    private int code;

    private String message;

    public BizException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    public BizException(String message) {
    	this(SYSTEM_EXCEPTION.getCode(), message);
    }
    
    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static BizException withMessage(String message) {
        return new BizException(SYSTEM_EXCEPTION.getCode(), message);
    }
    
    public static BizException withMessage(Integer code,  String message) {
        return new BizException(code != null ? code : SYSTEM_EXCEPTION.getCode(), message);
    }
}
