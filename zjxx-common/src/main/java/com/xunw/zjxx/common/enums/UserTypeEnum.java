package com.xunw.zjxx.common.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;
import java.util.Objects;

/**
 * 用户类型
 */
public enum UserTypeEnum implements IEnum {

	ADMIN("管理用户", "0"),
	STUDENT("学员用户", "1");

    private String name;

    private String id;

    private UserTypeEnum(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static UserTypeEnum findById(String id) {
        for (UserTypeEnum status : UserTypeEnum.values()) {
            if (Objects.equals(status.id, id)) {
                return status;
            }
        }
        return null;
    }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	@Override
	public Serializable getValue() {
		return this.name();
	}

}
