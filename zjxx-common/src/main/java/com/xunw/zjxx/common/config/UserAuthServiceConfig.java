package com.xunw.zjxx.common.config;

/**
 * 用户权限微服务
 * <AUTHOR>
 *
 */
public class UserAuthServiceConfig {
	
	private String findPrivilegeByRoleId;
	private String orgList;
	private String orgAdd;
	private String orgEdit;
	private String orgDelete;
	private String orgTree;
	private String orgSelect;
	private String orgGetById;
	private String orgGetHostOrgByDomain;
	private String userList;
	private String userDelete;
	private String userAdd;
	private String resetPassword;
	private String userSetRole;
	private String userGetById;
	private String userUpdate;
	private String deleteUserRole;
	private String findRoleByUserId;
	private String roleList;
	private String setPrivilege;
	private String setPrivilegeByCode;
	private String getTopOrg;
	private String relatedOrgSelect;
	private String addOrgRelation;
	private String relatedOrgList;
	private String relatedOrgTee;

	public String getSetPrivilegeByCode() {
		return setPrivilegeByCode;
	}

	public void setSetPrivilegeByCode(String setPrivilegeByCode) {
		this.setPrivilegeByCode = setPrivilegeByCode;
	}

	public String getSetPrivilege() {
		return setPrivilege;
	}

	public void setSetPrivilege(String setPrivilege) {
		this.setPrivilege = setPrivilege;
	}

	public String getFindPrivilegeByRoleId() {
		return findPrivilegeByRoleId;
	}
	public void setFindPrivilegeByRoleId(String findPrivilegeByRoleId) {
		this.findPrivilegeByRoleId = findPrivilegeByRoleId;
	}
	public String getOrgList() {
		return orgList;
	}
	public void setOrgList(String orgList) {
		this.orgList = orgList;
	}
	public String getOrgAdd() {
		return orgAdd;
	}
	public void setOrgAdd(String orgAdd) {
		this.orgAdd = orgAdd;
	}
	public String getOrgEdit() {
		return orgEdit;
	}
	public void setOrgEdit(String orgEdit) {
		this.orgEdit = orgEdit;
	}
	public String getOrgDelete() {
		return orgDelete;
	}
	public void setOrgDelete(String orgDelete) {
		this.orgDelete = orgDelete;
	}
	public String getOrgTree() {
		return orgTree;
	}
	public void setOrgTree(String orgTree) {
		this.orgTree = orgTree;
	}
	public String getOrgSelect() {
		return orgSelect;
	}
	public void setOrgSelect(String orgSelect) {
		this.orgSelect = orgSelect;
	}
	public String getOrgGetById() {
		return orgGetById;
	}
	public void setOrgGetById(String orgGetById) {
		this.orgGetById = orgGetById;
	}
	public String getOrgGetHostOrgByDomain() {
		return orgGetHostOrgByDomain;
	}
	public void setOrgGetHostOrgByDomain(String orgGetHostOrgByDomain) {
		this.orgGetHostOrgByDomain = orgGetHostOrgByDomain;
	}
	public String getUserAdd() {
		return userAdd;
	}
	public void setUserAdd(String userAdd) {
		this.userAdd = userAdd;
	}
	public String getResetPassword() {
		return resetPassword;
	}
	public void setResetPassword(String resetPassword) {
		this.resetPassword = resetPassword;
	}

	public String getUserSetRole() {
		return userSetRole;
	}

	public void setUserSetRole(String userSetRole) {
		this.userSetRole = userSetRole;
	}

	public String getUserGetById() {
		return userGetById;
	}

	public void setUserGetById(String userGetById) {
		this.userGetById = userGetById;
	}

	public String getUserUpdate() {
		return userUpdate;
	}

	public void setUserUpdate(String userUpdate) {
		this.userUpdate = userUpdate;
	}

	public String getDeleteUserRole() {
		return deleteUserRole;
	}

	public void setDeleteUserRole(String deleteUserRole) {
		this.deleteUserRole = deleteUserRole;
	}

	public String getFindRoleByUserId() {
		return findRoleByUserId;
	}

	public void setFindRoleByUserId(String findRoleByUserId) {
		this.findRoleByUserId = findRoleByUserId;
	}

	public String getUserList() {
		return userList;
	}

	public void setUserList(String userList) {
		this.userList = userList;
	}

	public String getGetTopOrg() {
		return getTopOrg;
	}

	public void setGetTopOrg(String getTopOrg) {
		this.getTopOrg = getTopOrg;
	}
	public String getRelatedOrgSelect() {
		return relatedOrgSelect;
	}
	public void setRelatedOrgSelect(String relatedOrgSelect) {
		this.relatedOrgSelect = relatedOrgSelect;
	}
	public String getAddOrgRelation() {
		return addOrgRelation;
	}
	public void setAddOrgRelation(String addOrgRelation) {
		this.addOrgRelation = addOrgRelation;
	}
	public String getRelatedOrgList() {
		return relatedOrgList;
	}
	public void setRelatedOrgList(String relatedOrgList) {
		this.relatedOrgList = relatedOrgList;
	}
	public String getRelatedOrgTee() {
		return relatedOrgTee;
	}
	public void setRelatedOrgTee(String relatedOrgTee) {
		this.relatedOrgTee = relatedOrgTee;
	}

	public String getRoleList() {
		return roleList;
	}

	public void setRoleList(String roleList) {
		this.roleList = roleList;
	}

	public String getUserDelete() {
		return userDelete;
	}

	public void setUserDelete(String userDelete) {
		this.userDelete = userDelete;
	}
}
