package com.xunw.zjxx.common.base;


import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * service层基类,提供基础的CRUD操作
 * <AUTHOR>
 */
public abstract class BaseCRUDService<T extends BaseMapper<K>, K> {
	
	@Autowired
	protected T mapper;
	
	/**
	 * ***********基本的增删改查方法************
	 */
	@Transactional(rollbackFor = Exception.class)
	public void insert(K entity) {
		mapper.insert(entity);
	}
	
	@Transactional(rollbackFor = Exception.class)
	public void updateById(K entity) {
		mapper.updateById(entity);
	}
	
	@Transactional(rollbackFor = Exception.class)
	public void deleteById(String id) {
		mapper.deleteById(id);
	}
	
	@Transactional(rollbackFor = Exception.class)
	public void deleteList(EntityWrapper<K> wrapper) {
		mapper.delete(wrapper);
	}
	
	@Transactional(rollbackFor = Exception.class)
	public void deleteBatchIds(Collection<String> idList) {
		mapper.deleteBatchIds(idList);
	}
	
	public K selectById(String id) {
		return mapper.selectById(id);
	}
	
	public Integer selectCount(EntityWrapper<K> wrapper) {
		return mapper.selectCount(wrapper);
	}
	
	public List<K> selectList(EntityWrapper<K> wrapper){
		return mapper.selectList(wrapper);
	}
	
	public Page<K> selectPage(Page<K> page, EntityWrapper<K> wrapper){
		page.setRecords(mapper.selectPage(page, wrapper));
		return page;
	}
	
	public List<K> selectBatchIds(Collection<String> ids) {
		return mapper.selectBatchIds(ids);
	}
	
	@Transactional(rollbackFor = Exception.class)
	public void updateAllColumnById(K entity) {
		mapper.updateAllColumnById(entity);
	}
	
	@Transactional(rollbackFor = Exception.class)
	public void update(K entity, EntityWrapper<K> wrapper) {
		mapper.update(entity, wrapper);
	}
	
	
	
	/**
	 * ***********基本的增删改查方法-----结束************
	 */
}
