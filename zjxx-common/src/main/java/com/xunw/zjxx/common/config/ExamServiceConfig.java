package com.xunw.zjxx.common.config;

public class ExamServiceConfig {
	
	private String questionDbList;
	private String questionDbAdd;
	private String questionDbDelete;
	private String questionDbEdit;
	private String questionDbViewQues;
	private String questionDbQuestionPreview;
	private String questionDbSelect;
	private String questionCount;
	private String questionList;
	private String questionAdd;
	private String questionEdit;
	private String questionSelectRealType;
	private String questionGetQuesDetailsById;
	private String questionDeleteById;
	private String questionEditStatus;
	private String questionImport;
	private String questionDbGetRandomObjectiveQuestion;
	private String questionGetChildByPid;
	private String paperList;
    private String paperUpdate;
    private String paperDelete;
    private String paperGetById;
    private String paperAdd;
    private String paperAuto;
    private String paperAddAndAuto;
	private String paperRepoList;
	private String paperPackage;
	private String paperRepoGetPaperDetailById;
	private String paperRepoEditPaper;
	private String paperRepoDeletePaper;
	private String paperRepoAutoPackage;
	private String paperRepoPackage;
	private String studentExamList;
	private String revisereset;
	private String revise;
	private String examList;
	private String examStart;
	private String examDetail;
	private String examUploadCard;
	private String examSave;
	private String examGetById;
	private String examDelete;
	private String examNext;
	private String examSubmit;
	private String autoSubmit;
	private String redoExam;

	public String getPaperPackage() {
		return paperPackage;
	}

	public void setPaperPackage(String paperPackage) {
		this.paperPackage = paperPackage;
	}

	public String getPaperAuto() {
        return paperAuto;
    }

    public void setPaperAuto(String paperAuto) {
        this.paperAuto = paperAuto;
    }

    public String getPaperAddAndAuto() {
        return paperAddAndAuto;
    }

    public void setPaperAddAndAuto(String paperAddAndAuto) {
        this.paperAddAndAuto = paperAddAndAuto;
    }

    public String getPaperAdd() {
        return paperAdd;
    }

    public void setPaperAdd(String paperAdd) {
        this.paperAdd = paperAdd;
    }

    public String getPaperList() {
        return paperList;
    }

    public void setPaperList(String paperList) {
        this.paperList = paperList;
    }

    public String getPaperUpdate() {
        return paperUpdate;
    }

    public void setPaperUpdate(String paperUpdate) {
        this.paperUpdate = paperUpdate;
    }

    public String getPaperDelete() {
        return paperDelete;
    }

    public void setPaperDelete(String paperDelete) {
        this.paperDelete = paperDelete;
    }

    public String getPaperGetById() {
        return paperGetById;
    }

    public void setPaperGetById(String paperGetById) {
        this.paperGetById = paperGetById;
    }

    public String getQuestionSelectRealType() {
		return questionSelectRealType;
	}

	public void setQuestionSelectRealType(String questionSelectRealType) {
		this.questionSelectRealType = questionSelectRealType;
	}

	public String getQuestionGetChildByPid() {
        return questionGetChildByPid;
    }

    public void setQuestionGetChildByPid(String questionGetChildByPid) {
        this.questionGetChildByPid = questionGetChildByPid;
    }

    public String getPaperRepoPackage() {
        return paperRepoPackage;
    }

    public void setPaperRepoPackage(String paperRepoPackage) {
        this.paperRepoPackage = paperRepoPackage;
    }

    public String getPaperRepoAutoPackage() {
        return paperRepoAutoPackage;
    }

    public void setPaperRepoAutoPackage(String paperRepoAutoPackage) {
        this.paperRepoAutoPackage = paperRepoAutoPackage;
    }

    public String getQuestionDbQuestionPreview() {
		return questionDbQuestionPreview;
	}

	public void setQuestionDbQuestionPreview(String questionDbQuestionPreview) {
		this.questionDbQuestionPreview = questionDbQuestionPreview;
	}

	public String getQuestionDbSelect() {
		return questionDbSelect;
	}

	public void setQuestionDbSelect(String questionDbSelect) {
		this.questionDbSelect = questionDbSelect;
	}

	public String getQuestionDbList() {
		return questionDbList;
	}
	public void setQuestionDbList(String questionDbList) {
		this.questionDbList = questionDbList;
	}
	public String getQuestionDbAdd() {
		return questionDbAdd;
	}
	public void setQuestionDbAdd(String questionDbAdd) {
		this.questionDbAdd = questionDbAdd;
	}
	public String getQuestionDbDelete() {
		return questionDbDelete;
	}
	public void setQuestionDbDelete(String questionDbDelete) {
		this.questionDbDelete = questionDbDelete;
	}
	public String getQuestionDbEdit() {
		return questionDbEdit;
	}
	public void setQuestionDbEdit(String questionDbEdit) {
		this.questionDbEdit = questionDbEdit;
	}
	public String getQuestionDbViewQues() {
		return questionDbViewQues;
	}
	public void setQuestionDbViewQues(String questionDbViewQues) {
		this.questionDbViewQues = questionDbViewQues;
	}
	public String getQuestionCount() {
		return questionCount;
	}
	public void setQuestionCount(String questionCount) {
		this.questionCount = questionCount;
	}
	public String getQuestionList() {
		return questionList;
	}
	public void setQuestionList(String questionList) {
		this.questionList = questionList;
	}
	public String getQuestionAdd() {
		return questionAdd;
	}
	public void setQuestionAdd(String questionAdd) {
		this.questionAdd = questionAdd;
	}
	public String getQuestionEdit() {
		return questionEdit;
	}
	public void setQuestionEdit(String questionEdit) {
		this.questionEdit = questionEdit;
	}
	public String getQuestionGetQuesDetailsById() {
		return questionGetQuesDetailsById;
	}
	public void setQuestionGetQuesDetailsById(String questionGetQuesDetailsById) {
		this.questionGetQuesDetailsById = questionGetQuesDetailsById;
	}
	public String getQuestionDeleteById() {
		return questionDeleteById;
	}
	public void setQuestionDeleteById(String questionDeleteById) {
		this.questionDeleteById = questionDeleteById;
	}
	public String getQuestionEditStatus() {
		return questionEditStatus;
	}
	public void setQuestionEditStatus(String questionEditStatus) {
		this.questionEditStatus = questionEditStatus;
	}
	public String getQuestionImport() {
		return questionImport;
	}
	public void setQuestionImport(String questionImport) {
		this.questionImport = questionImport;
	}
	public String getPaperRepoList() {
		return paperRepoList;
	}
	public void setPaperRepoList(String paperRepoList) {
		this.paperRepoList = paperRepoList;
	}
	public String getPaperRepoGetPaperDetailById() {
		return paperRepoGetPaperDetailById;
	}
	public void setPaperRepoGetPaperDetailById(String paperRepoGetPaperDetailById) {
		this.paperRepoGetPaperDetailById = paperRepoGetPaperDetailById;
	}
	public String getPaperRepoEditPaper() {
		return paperRepoEditPaper;
	}
	public void setPaperRepoEditPaper(String paperRepoEditPaper) {
		this.paperRepoEditPaper = paperRepoEditPaper;
	}
	public String getPaperRepoDeletePaper() {
		return paperRepoDeletePaper;
	}
	public void setPaperRepoDeletePaper(String paperRepoDeletePaper) {
		this.paperRepoDeletePaper = paperRepoDeletePaper;
	}
	public String getStudentExamList() {
		return studentExamList;
	}
	public void setStudentExamList(String studentExamList) {
		this.studentExamList = studentExamList;
	}
	public String getRevisereset() {
		return revisereset;
	}
	public void setRevisereset(String revisereset) {
		this.revisereset = revisereset;
	}
	public String getExamList() {
		return examList;
	}
	public void setExamList(String examList) {
		this.examList = examList;
	}
	public String getExamStart() {
		return examStart;
	}
	public void setExamStart(String examStart) {
		this.examStart = examStart;
	}
	public String getExamDetail() {
		return examDetail;
	}
	public void setExamDetail(String examDetail) {
		this.examDetail = examDetail;
	}
	public String getExamUploadCard() {
		return examUploadCard;
	}
	public void setExamUploadCard(String examUploadCard) {
		this.examUploadCard = examUploadCard;
	}
	public String getExamSave() {
		return examSave;
	}
	public void setExamSave(String examSave) {
		this.examSave = examSave;
	}
	public String getExamGetById() {
		return examGetById;
	}
	public void setExamGetById(String examGetById) {
		this.examGetById = examGetById;
	}
	public String getExamDelete() {
		return examDelete;
	}
	public void setExamDelete(String examDelete) {
		this.examDelete = examDelete;
	}
	public String getExamNext() {
		return examNext;
	}
	public void setExamNext(String examNext) {
		this.examNext = examNext;
	}

	public String getRevise() {
		return revise;
	}

	public void setRevise(String revise) {
		this.revise = revise;
	}

	public String getExamSubmit() {
		return examSubmit;
	}

	public void setExamSubmit(String examSubmit) {
		this.examSubmit = examSubmit;
	}

	public String getRedoExam() {
		return redoExam;
	}

	public void setRedoExam(String redoExam) {
		this.redoExam = redoExam;
	}

	public String getQuestionDbGetRandomObjectiveQuestion() {
		return questionDbGetRandomObjectiveQuestion;
	}

	public void setQuestionDbGetRandomObjectiveQuestion(String questionDbGetRandomObjectiveQuestion) {
		this.questionDbGetRandomObjectiveQuestion = questionDbGetRandomObjectiveQuestion;
	}

	public String getAutoSubmit() {
		return autoSubmit;
	}

	public void setAutoSubmit(String autoSubmit) {
		this.autoSubmit = autoSubmit;
	}
}
