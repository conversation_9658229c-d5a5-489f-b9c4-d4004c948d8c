package com.xunw.zjxx.common.utils;

import com.baomidou.mybatisplus.enums.SqlMethod;
import com.baomidou.mybatisplus.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.mapper.SqlHelper;
import com.baomidou.mybatisplus.toolkit.CollectionUtils;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.session.SqlSession;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public class DBUtils {

    /**
     * 批量新增
     *
     * @param entityList
     * 					实体对象数组
     * @param classz
     * 					实体类
     */
    @Transactional(rollbackFor = { Exception.class })
    public static <T>  boolean insertBatch(List<T> entityList, Class<T> classz) {
        return insertBatch(entityList, 30, classz);
    }

    /**
     * 批量新增
     *
     * @param entityList
     * 					实体对象数组
     * @param batchSize
     * 					每多少条数据flush一次
     * @param classz
     * 					实体类
     */
    @Transactional(rollbackFor = { Exception.class })
    public static <T> boolean insertBatch(List<T> entityList, int batchSize, Class<T> classz) {
        if (CollectionUtils.isEmpty(entityList)) {
            return true;
        } else {
            try {
                SqlSession batchSqlSession = sqlSessionBatch(classz);
                Throwable var4 = null;

                try {
                    int size = entityList.size();
                    String sqlStatement = sqlStatement(SqlMethod.INSERT_ONE, classz);

                    for (int i = 0; i < size; ++i) {
                        batchSqlSession.insert(sqlStatement, entityList.get(i));
                        if (i >= 1 && i % batchSize == 0) {
                            batchSqlSession.flushStatements();
                        }
                    }

                    batchSqlSession.flushStatements();
                } catch (Throwable var16) {
                    var4 = var16;
                    throw var16;
                } finally {
                    if (batchSqlSession != null) {
                        if (var4 != null) {
                            try {
                                batchSqlSession.close();
                            } catch (Throwable var15) {
                                var4.addSuppressed(var15);
                            }
                        } else {
                            batchSqlSession.close();
                        }
                    }

                }

                return true;
            } catch (Throwable var18) {
                throw new MybatisPlusException("Error: Cannot execute insertBatch Method. Cause", var18);
            }
        }
    }

    /**
     * 批量修改
     * @param entityList
     * 					实体对象数组
     * @param classz
     * 					实体类
     */
    public static <T> boolean updateBatchById(List<T> entityList, Class<T> classz) {
        return updateBatchById(entityList, 30, classz);
    }

    public static <T> boolean updateAllColumnBatchById(List<T> entityList, Class<T> classz) {
        return updateBatchById(entityList, 30, false, classz);
    }

    /**
     * 批量修改
     * @param entityList
     * 					实体对象数组
     * @param batchSize
     * 					每多少条flush一次
     * @param classz
     * 					实体类
     */
    @Transactional(rollbackFor = { Exception.class })
    public static <T> boolean updateBatchById(List<T> entityList, int batchSize, Class<T> classz) {
        return updateBatchById(entityList, batchSize, true, classz);
    }

    private static <T> boolean updateBatchById(List<T> entityList, int batchSize, boolean selective, Class<T> classz) {
        if (CollectionUtils.isEmpty(entityList)) {
            return true;
        } else {
            try {
                SqlSession batchSqlSession = sqlSessionBatch(classz);
                Throwable var5 = null;
                try {
                    int size = entityList.size();
                    SqlMethod sqlMethod = selective ? SqlMethod.UPDATE_BY_ID : SqlMethod.UPDATE_ALL_COLUMN_BY_ID;
                    String sqlStatement = sqlStatement(sqlMethod, classz);
                    for (int i = 0; i < size; ++i) {
                        MapperMethod.ParamMap<T> param = new MapperMethod.ParamMap();
                        param.put("et", entityList.get(i));
                        batchSqlSession.update(sqlStatement, param);
                        if (i >= 1 && i % batchSize == 0) {
                            batchSqlSession.flushStatements();
                        }
                    }
                    batchSqlSession.flushStatements();
                    return true;
                } catch (Throwable var19) {
                    var5 = var19;
                    throw var19;
                } finally {
                    if (batchSqlSession != null) {
                        if (var5 != null) {
                            try {
                                batchSqlSession.close();
                            } catch (Throwable var18) {
                                var5.addSuppressed(var18);
                            }
                        } else {
                            batchSqlSession.close();
                        }
                    }
                }
            } catch (Throwable var21) {
                throw new MybatisPlusException("Error: Cannot execute updateBatchById Method. Cause", var21);
            }
        }
    }

    protected static <T> SqlSession sqlSessionBatch(Class<T> classz) {
        return SqlHelper.sqlSessionBatch(classz);
    }

    protected static <T> String sqlStatement(SqlMethod sqlMethod, Class<T> classz) {
        return SqlHelper.table(classz).getSqlStatement(sqlMethod.getMethod());
    }
}
