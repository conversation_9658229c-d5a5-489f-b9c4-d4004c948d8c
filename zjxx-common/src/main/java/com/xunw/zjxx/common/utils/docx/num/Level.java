package com.xunw.zjxx.common.utils.docx.num;



import com.xunw.zjxx.common.utils.docx.html.HtmlElement;
import com.xunw.zjxx.common.utils.BaseUtil;

import java.util.ArrayList;
import java.util.List;

public class Level {
	private String id;
	private int start = 1;//起始值
	private int curIndex;//当前的序号值
	private String numFmt;//bullet、 decimal、lowerLetter、upperLetter、lowerRoman、upperRoman、chineseCountingThousand（中文数字小写）、chineseLegalSimplified（中文数字大写）...
	private String lvlText;//序号字符串 或者 序号格式
	private String lvlPicBulletId = null;//图片序号Id，如果序号使用图片才会有//TODO 暂不处理
	private String hanging;//序号宽度
	private String left;//margin-left
	private List<HtmlElement> fonts;//序号的样式

	public Level(String id, int start, String numFmt, String lvlText,
			String lvlPicBulletId, String hanging, String left,
			List<HtmlElement> fonts) {
		this.id = id;
		this.start = start;
		this.curIndex = start;
		this.numFmt = numFmt;
		this.lvlText = lvlText;
		this.lvlPicBulletId = lvlPicBulletId;
		this.hanging = hanging;
		this.left = left;
		this.fonts = fonts;
	}

	public String getId() {
		return id;
	}
	public int getStart() {
		return start;
	}
	public String getNumFmt() {
		return numFmt;
	}
	public String getLvlText() {
		return lvlText;
	}
	public String getLvlUnicodeText(){
		return BaseUtil.toUnicode(lvlText);
	}
	public String getLvlPicBulletId() {
		return lvlPicBulletId;
	}
	public String getHanging() {
		return hanging;
	}
	public String getLeft() {
		return left;
	}
	public List<HtmlElement> getFonts() {
		return fonts;
	}
	public int getCurIndex() {
		return curIndex++;
	}
	public void restCurIndex() {
		curIndex = start;
	}
	public int getCurIndexFromSub() {
		if(curIndex -1 < start){
			curIndex++;
			return start;
		}
		return curIndex -1;
	}

	public Level clone(){
		Level newLevel = new Level(id, start, numFmt, lvlText, lvlPicBulletId, hanging, left, new ArrayList<HtmlElement>(fonts));
		return newLevel;
	}

}
