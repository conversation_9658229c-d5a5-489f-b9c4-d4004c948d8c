/*
 * *************************************************
 * Copyright (c) 2018 SunData. All Rights Reserved.
 * Created by <PERSON><PERSON><PERSON> on 2018-04-24 13:54:56.
 * *************************************************
 */

package com.xunw.zjxx.common.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class NumUtils {
    public static final String[] UP_CHINESE_NUMBER = {"零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"};
    public static final String[] UP_UNIT = {"", "拾", "佰", "仟", "萬", "拾", "佰", "仟", "亿", "拾"};
    public static final String[] CHINESE_NUMBER = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    public static final String[] UNIT = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十"};
    public static final String[] CHAR = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"};

    /**
     * 将数字转换成中文数字
     */
    public static String toCnNumber(int number) {
        String result = "";
        if (number < 0 || number > 999999999) {
            //限制支持的数字范围，小于10亿
            return result;
        }
        StringBuffer numStr = new StringBuffer(String.valueOf(number)).reverse();
        int cur, pre = 0;
        if (number >= 10 && number < 20) {
            cur = Integer.valueOf(numStr.substring(0, 1));
            if (cur == 0) {
                return UNIT[1];
            }
            return UNIT[1] + CHINESE_NUMBER[cur];
        }
        for (int i = 0; i < numStr.length(); i++) {
            //当前数字
            cur = Integer.valueOf(numStr.substring(i, i + 1));
            if (i != 0) {
                //上一个数字
                pre = Integer.valueOf(numStr.substring(i - 1, i));
            }
            if (i == 0) {
                if (cur != 0 || numStr.length() == 1) {
                    result = CHINESE_NUMBER[cur];
                }
                continue;
            }
            if (i == 1 || i == 2 || i == 3 || i == 5 || i == 6 || i == 7 || i == 9) {
                if (cur != 0) {
                    result = CHINESE_NUMBER[cur] + UNIT[i] + result;
                } else if (pre != 0) {
                    result = CHINESE_NUMBER[cur] + result;
                }
                continue;
            }
            if (i == 4 || i == 8) {
                result = UNIT[i] + result;
                if ((pre != 0 && cur == 0) || cur != 0) {
                    result = CHINESE_NUMBER[cur] + result;
                }
                continue;
            }
        }
        return result;
    }

    /**
     * 将(1-26)区间的数字转换成字母
     */
    public static String toEnNumber(int number) {
        String result = "";
        if (number < 1 || number > 26) {
            //限制支持的数字范围
            return result;
        }
        return CHAR[number - 1];
    }

    /**
     * 百分比 相除 保留两位小数 乘以 100
     * @return
     */
    public static String toPercent(int a, int b) {
        DecimalFormat format = new DecimalFormat("0.00");
        return format.format(div(a, b).multiply(new BigDecimal(100)));
    }

    public static BigDecimal div(int value1, int value2) {
        if (value1 == 0 || value2 == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal b1 = new BigDecimal(value1);
        BigDecimal b2 = new BigDecimal(value2);
        return b1.divide(b2,6, BigDecimal.ROUND_HALF_UP);
    }
}