package com.xunw.zjxx.common.utils;

import com.alibaba.fastjson.JSON;
import com.qiniu.pili.Client;
import com.qiniu.pili.Hub;
import com.qiniu.pili.PiliException;
import com.qiniu.pili.Stream;
import com.qiniu.util.Auth;
import com.qiniu.util.Base64;
import com.qiniu.util.UrlSafeBase64;
import com.xunw.zjxx.common.config.QiniuConfig;
import net.sf.json.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.SignatureException;
import java.util.HashMap;
import java.util.Map;

public class QiniuZhiboUtils {

    private static Logger log = LoggerFactory.getLogger(QiniuZhiboUtils.class);

    //初始化client
    private static Client cli = new Client(
            ((QiniuConfig)SpringBeanUtils.getBean(QiniuConfig.class)).getAccessKey(),
            ((QiniuConfig)SpringBeanUtils.getBean(QiniuConfig.class)).getSecretKey()
    );

    //初始化Hub
    private static Hub hub = cli.newHub(
            QiniuZbConstants.QINIU_HUBNAME
    );

    /**
     * 创建直播流
     * @param streamName
     * @return
     * @throws PiliException
     */
    public static Stream createStream(String streamName) throws PiliException{
        Stream stream = hub.create(streamName);
        return stream;
    }

    /**
     * 获取直播流
     * @param streamName
     * @return
     * @throws PiliException
     */
    public static Stream getStream(String streamName) throws PiliException{
        return hub.get(streamName);
    }

    /**
     * 是否直播已禁用
     * @param streamName
     * @return
     * @throws PiliException
     */
    public static boolean isDisabled(String streamName) throws PiliException{
        Stream stream = getStream(streamName);
        return stream.getDisabledTill() != 0;
    }

    /**
     * 启用直播
     * @param streamName
     * @throws PiliException
     */
    public static void enable(String streamName) throws PiliException{
        getStream(streamName).enable();
    }

    /**
     * 禁用直播
     * @param streamName
     * @throws PiliException
     */
    public static void disable(String streamName) throws PiliException{
        getStream(streamName).disable();
    }

    /**
     * 是否直播推流中
     * @param streamName
     * @return
     * @throws PiliException
     */
    public static boolean isLiving(String streamName) throws PiliException{
        Stream stream = getStream(streamName);
        try {
            Stream.LiveStatus status = stream.liveStatus();
            return status.startAt != 0;
        } catch (PiliException e) {
            if("stream isn't in live".equals(e.getMessage())){
                return false;
            }else{
                throw e;
            }
        }
    }

    /**
     * 获取推流地址
     * @param streamName
     * @return
     */
    public static String getRTMPPublishURL(String streamName){
        String url = cli.RTMPPublishURL(QiniuZbConstants.QINIU_TUILIU_DOMAIN, QiniuZbConstants.QINIU_HUBNAME, streamName, QiniuZbConstants.QINIU_TUILIU_GQSC);
        return url;
    }

    /**
     * 获取RTMP直播地址
     * @param streamName
     * @return
     */
    public static String getRTMPPlayURL(String streamName){
        String url = cli.RTMPPlayURL(QiniuZbConstants.QINIU_RTMP_DOMAIN, QiniuZbConstants.QINIU_HUBNAME, streamName);
        return url;
    }

    /**
     * 获取HLS直播地址
     * @param streamName
     * @return
     */
    public static String getHLSPlayURL(String streamName){
        String url = cli.HLSPlayURL(QiniuZbConstants.QINIU_HLS_DOMAIN, QiniuZbConstants.QINIU_HUBNAME, streamName);
        return url;
    }

    /**
     * 获取FLV（HDL）直播地址
     * @param streamName
     * @return
     */
    public static String getFLVPlayURL(String streamName){
        String url = cli.HDLPlayURL(QiniuZbConstants.QINIU_FLV_DOMAIN, QiniuZbConstants.QINIU_HUBNAME, streamName);
        return url;
    }

    /**
     * 录制，生成一个可以点播回看的视频文件
     * @param streamName
     * @notifyUrl 保存成功回调通知地址
     * @param start 直播开始的秒时间戳，整数，Unix 时间戳，要保存的直播的起始时间，0 表示从第一次直播开始
     * @param end 直播线束的秒时间戳，整数，Unix 时间戳，要保存的直播的结束时间，0 表示当前时间
     * @return replay_url、replay_sessionid，如果出现异常：replay_url=error
     */
    public static Map<String,Object> save(String streamName, String notifyUrl, long start, long end){
        Map<String,Object> resultMap = new HashMap<String, Object>();
        HttpURLConnection conn = null;
        try {
            QiniuConfig qiniuConfig = SpringBeanUtils.getBean(QiniuConfig.class);
            String fname = qiniuConfig.getZbhfprefix() + "_"+ streamName + "_" + DateUtils.getCurrentDate("yyyyMMddHHmmss") +".mp4";
            String encodedStreamTitle = UrlSafeBase64.encodeToString(streamName);
            System.out.println("encodedStreamTitle=" + encodedStreamTitle);
            String host = "http://pili.qiniuapi.com";
            String url = host + "/v2/hubs/" + QiniuZbConstants.QINIU_HUBNAME + "/streams/" + encodedStreamTitle + "/saveas";
            System.out.println("url=" + url);
            // 发送请求参数
            JSONObject data = new JSONObject();
            data.put("fname", fname);
            data.put("format", "mp4");
            data.put("notify", notifyUrl);
            data.put("start", start);
            data.put("end", end);
            data.put("pipeline", "zb_huifang");
            String bodyStr = data.toString();
            System.out.println("bodyStr=" + bodyStr);
            byte[] body = bodyStr.getBytes("UTF-8");

            String authorization = (String) Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey()).authorizationV2(url,"POST", body, "application/json").get("Authorization");
            System.out.println("authorization=" + authorization);
            URL u = new URL(url);
            conn = (HttpURLConnection) u.openConnection();
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("Authorization", authorization);
            conn.setConnectTimeout(50000);
            conn.setReadTimeout(50000);
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");

            OutputStream os = conn.getOutputStream();
            IOUtils.write(body, os);
            os.flush();

            InputStream is = conn.getInputStream();
            String returnStr = IOUtils.toString(is, "UTF-8");
            System.out.println("returnStr=" + returnStr);
            BaseUtil.close(is);

            JSONObject returnJson = JSONObject.fromObject(returnStr);
            String replay_url = QiniuZbConstants.QINIU_REVIEW_URLPREFIX + fname;
            System.out.println("回放文件URL=" + replay_url);
            resultMap.put("replay_url", replay_url);
            resultMap.put("replay_sessionid", returnJson.get("persistentID"));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成直播[" + streamName + "]的回放文件失败，原因：" + e.getMessage(), e);
            try {
                InputStream is = conn.getErrorStream();
                String returnStr = IOUtils.toString(is, "UTF-8");
                System.out.println("returnStr=" + returnStr);
                log.error("生成直播[" + streamName + "]的回放文件失败，从七牛云返回的信息：" + returnStr);
                BaseUtil.close(is);
            } catch (Exception e2) {
                e2.printStackTrace();
                log.error("获取七牛云返回的信息失败，原因：" + e2.getMessage(),e);
            }
            resultMap.put("replay_url", "error");
        }
        return resultMap;
    }

    /**
     * 把直播的回放文件转为低频存储
     */
    public static void chtypeForHuifang(String huifangKey){
        String entry = "zkexam-zhibo:" + huifangKey;
        HttpURLConnection conn = null;
        try {//设置文件为低频存储
            QiniuConfig qiniuConfig = SpringBeanUtils.getBean(QiniuConfig.class);
            String encodedEntryURI = UrlSafeBase64.encodeToString(entry);
            String host = "http://rs.qiniu.com";
            String url = "/chtype/" + encodedEntryURI + "/type/1";
            String authorization = (String) Auth.create(qiniuConfig.getAccessKey(), qiniuConfig.getSecretKey()).authorization(url, null, "application/x-www-form-urlencoded").get("Authorization");
            URL u = new URL(host + url);
            conn = (HttpURLConnection) u.openConnection();
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("Authorization", authorization);
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setDoInput(true);
            // 设置请求方式，默认为GET
            conn.setRequestMethod("GET");

            InputStream is = conn.getInputStream();
            String returnStr = IOUtils.toString(is, "UTF-8");
            if(BaseUtil.isNotEmpty(returnStr)){
                System.out.println("returnStr=" + returnStr);
            }
            BaseUtil.close(is);
        } catch (Exception e) {
            System.out.println("把[" + entry + "]设为低频存储失败，原因：" + e.getMessage());
            if(conn != null){
                try {
                    InputStream is = conn.getErrorStream();
                    String returnStr = IOUtils.toString(is, "UTF-8");
                    System.out.println("returnStr=" + returnStr);
                    BaseUtil.close(is);
                } catch (Exception e2) {
                    e2.printStackTrace();
                }
            }
            e.printStackTrace();
        }
    }

    //通过接口查询直播的时长秒
    public static int getZhiBoDuration(String url) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        // 创建Get请求
        url = url + QiniuZbConstants.QINIU_REVIEW_URLSUFFIX;
        HttpPost httpPost = new HttpPost(url);

        // 响应模型
        CloseableHttpResponse response = null;

        try {
            // 由客户端执行(发送)Post请求
            response = httpClient.execute(httpPost);

            // 配置信息
            RequestConfig requestConfig = RequestConfig.custom()
                    // 设置连接超时时间(单位毫秒)
                    .setConnectTimeout(5000)
                    // 设置请求超时时间(单位毫秒)
                    .setConnectionRequestTimeout(5000)
                    // socket读写超时时间(单位毫秒)
                    .setSocketTimeout(5000)
                    // 设置是否允许重定向(默认为true)
                    .setRedirectsEnabled(true).build();

            // 将上面的配置信息 运用到这个Post请求里
            httpPost.setConfig(requestConfig);

            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();
            System.out.println("响应状态为:" + response.getStatusLine());
            if (responseEntity != null) {
                String str = EntityUtils.toString(responseEntity);
                long len = responseEntity.getContentLength();
                System.out.println("响应内容长度为:" + responseEntity.getContentLength());
                if(len <= 30 && len != -1) {
                    System.out.println("响应内容为:" + str);
                }
                com.alibaba.fastjson.JSONObject parseObject = JSON.parseObject(str);
                com.alibaba.fastjson.JSONObject format = (com.alibaba.fastjson.JSONObject) parseObject.get("format");
                if(BaseUtil.isNotEmpty(format)) {
                    return BaseUtil.getInt(format.get("duration"));
                }else {
                    return 0 ;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    /**
     * 获取直播流的实时在线观看人数
     */
    public static Integer getQiNiuStreamLiveCountNum(String streamName){
        try {
            String hub=QiniuZbConstants.QINIU_HUBNAME;
            String url = "http://pili.qiniuapi.com/v2/hubs/"+ hub +"/stat/play";
            QiniuConfig qiniuConfig = SpringBeanUtils.getBean(QiniuConfig.class);
            String header = "GET /v2/hubs/"+hub+"/stat/play"+
                    "\nHost: pili.qiniuapi.com" +
                    "\nContent-Type: application/json" +
                    "\n\n";
            byte[] sign = new byte[0];
            try {
                sign = HmacSha1Util.hmacSHA1(header,qiniuConfig.getSecretKey());
            } catch (SignatureException e) {
                e.printStackTrace();
            }
            String encodedSign = Base64.encodeToString(sign,0).replace('+','-').replace('/','_');
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json");
            httpHeaders.add("Host","pili.qiniuapi.com");
            String authorization = ("Qiniu "+qiniuConfig.getAccessKey()+":" + encodedSign).replaceAll("\n","");
            httpHeaders.add("Authorization",authorization);
            RestTemplate restTemplate = new RestTemplate();
            org.springframework.http.HttpEntity<String> requestEntity = new org.springframework.http.HttpEntity<String>(null, httpHeaders);
            ResponseEntity<String> results = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
            if (results.getStatusCode() == HttpStatus.OK) {
                String responseBody = results.getBody();
                JSONObject respObject = JSONObject.fromObject(responseBody);
                if (respObject.containsKey("streams")) {
                    respObject = respObject.getJSONObject("streams");
                    if (respObject.containsKey(streamName)) {
                        respObject = respObject.getJSONObject(streamName);
                        return BaseUtil.getIntegerValueFromJson(respObject, "count", 0);
                    }
                }
            }
        } catch (RestClientException e) {
            e.printStackTrace();
            log.error("获取七牛云返回的在线观看人数信息失败，原因：" + e.getMessage(),e);
        }
        return 0;
    }
}
