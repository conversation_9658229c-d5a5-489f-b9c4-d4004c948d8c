package com.xunw.zjxx.common.base;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.baomidou.mybatisplus.enums.IEnum;
import com.baomidou.mybatisplus.plugins.Page;

/**
 * 自定义查询参数对象的基类
 * <AUTHOR>
 *
 */
public abstract class BaseQueryParams extends Page {

	private static final Logger LOGGER = LoggerFactory.getLogger(BaseQueryParams.class);
	
	private static final String PAGE_PARAM_NAME_PAGE_NUMBER = "current";
	private static final String PAGE_PARAM_NAME_PAGE_SIZE = "size";
	
	/**
	 * 将所有的属性放入HashMap作为condition
	 */
	@Override
	public Map<String, Object> getCondition() {
		Map<String, Object> condition = new HashMap<>();
		try {
			BeanInfo beanInfo = Introspector.getBeanInfo(this.getClass(), BaseQueryParams.class);
			PropertyDescriptor[] descriptors = beanInfo.getPropertyDescriptors();
			for (PropertyDescriptor descriptor : descriptors) {
				Method getter = descriptor.getReadMethod();
				Object val = getter.invoke(this);
				if (val != null && val instanceof IEnum) {
					condition.put(descriptor.getName(), ((IEnum)val).getValue());
				} else {
					condition.put(descriptor.getName(), val);
				}
			}
			condition.put(PAGE_PARAM_NAME_PAGE_NUMBER, this.getCurrent());
			condition.put(PAGE_PARAM_NAME_PAGE_SIZE, this.getSize());
		} catch (Exception e) {
			LOGGER.error("BaseQueryParams get condition Error:", e);
		}
		return condition;
	}

}
