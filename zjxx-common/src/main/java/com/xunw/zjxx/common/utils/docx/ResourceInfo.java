package com.xunw.zjxx.common.utils.docx;

import com.xunw.zjxx.common.utils.BaseUtil;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;


public class ResourceInfo {
	protected File file;
	protected String id;
	protected String pathInZip;
	protected String target;
	protected String type;

	public ResourceInfo(String id, File file, String pathInZip, String target, String type) {
		this.id = id;
		this.file = file;
		this.pathInZip = pathInZip;
		this.target = target;
		this.type = type;
	}

	public File getFile() {
		return file;
	}
	public String getId() {
		return id;
	}
	public String getPathInZip() {
		return pathInZip;
	}
	public String getType() {
		return type;
	}

	public String getTarget() {
		return target;
	}

	/**
	 * 复制出一个新的资源对象，保证id和文件名的唯一
	 * @param info 原资源对象
	 * @param dictDir 新资源对象里面的文件要保存的文件夹
	 * @return
	 * @throws IOException
	 */
	public static ResourceInfo copyResourceInfo(ResourceInfo info,File dictDir) throws IOException{
		String suffix = info.getFile().getName();
		if(suffix.contains(".")){
			suffix = suffix.substring(suffix.lastIndexOf('.'));
		}else{
			suffix = "";
		}
		String newId= "MT" + BaseUtil.generateId2();
		String newFileName = newId + suffix;
		if(!dictDir.exists()){
			dictDir.mkdirs();
		}
		String resInDirName = new File(info.getPathInZip()).getParentFile().getName();
		File newFile = new File(new File(dictDir,resInDirName),newFileName);
		String newPathInZip = info.getPathInZip();
		if(newPathInZip.indexOf('/') > -1){
			newPathInZip = newPathInZip.substring(0, newPathInZip.lastIndexOf('/') + 1) + newFileName;
		}else{
			newPathInZip = newFileName;
		}
		FileUtils.copyFile(info.getFile(), newFile);
		String newTarget = resInDirName + "/" + newFileName;
//		System.out.println("newTarget=" + newTarget);
		ResourceInfo newInfo = new ResourceInfo(newId,newFile,newPathInZip,newTarget,info.getType());

		return newInfo;
	}
}
