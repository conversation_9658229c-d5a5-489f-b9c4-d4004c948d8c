package com.xunw.zjxx.common.utils;

import org.apache.commons.codec.binary.Base64;

import java.io.IOException;
import java.io.UnsupportedEncodingException;

/**
 * Base64工具
 * <AUTHOR>
 * @date 2018/8/21 15:14
 */
public class Base64ConvertUtil {

    /**
     * base64编码
     */
    public static String encode(String str) throws UnsupportedEncodingException  {
        return Base64.encodeBase64String(str.getBytes("utf-8"));
    }
    
    /**
     * base64编码
     */
    public static String encode(byte[] bytes) throws UnsupportedEncodingException  {
        return Base64.encodeBase64String(bytes);
    }
    
    /**
     * base64解码
     */
    public static String decode(String str) throws IOException  {
        byte[] decodeBytes = Base64.decodeBase64(str);
        return new String(decodeBytes,"utf-8");
    }
    
    
//    public static void main(String[] args) throws IOException {
//		String aString = "-----f2222<PERSON><PERSON>继续教育管理平台测试？#@￥";
//		String encode = encode(aString);
//		System.out.println("---encode:"+encode);
//		String decode = decode(encode);
//		System.out.println("--decode:"+decode);
//	}

}
