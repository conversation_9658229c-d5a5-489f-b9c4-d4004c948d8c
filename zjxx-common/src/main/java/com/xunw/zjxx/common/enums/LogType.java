package com.xunw.zjxx.common.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;

/**
 * 系统日志类别
 * <AUTHOR>
 *
 */
public enum LogType implements IEnum {

    NORMAL("普通日志", "1");

    private String name;

    private String id;

    private LogType(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static LogType findById(String id) {
        for (LogType status : LogType.values()) {
            if (status.id == id) {
                return status;
            }
        }
        return null;
    }

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	@Override
	public Serializable getValue() {
		return this.name();
	}
    
}
