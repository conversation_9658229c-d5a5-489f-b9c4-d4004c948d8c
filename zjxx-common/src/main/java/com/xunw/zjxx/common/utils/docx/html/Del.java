package com.xunw.zjxx.common.utils.docx.html;


/**
 * ɾ����
 * <AUTHOR>
 *
 */
public class Del extends HtmlElement{

	private boolean none = false;//�Ƿ�����ɾ����

	public Del(boolean none) {
		this.none = none;
	}

	@Override
	public String head() {
		return none?"<span style=\"text-decoration:none;\">":"<del>";
	}

	@Override
	public String tail() {
		return none?"</span>":"</del>";
	}

	@Override
	public String getStyleStr() {
		return "text-decoration:" + (none?"none":"line-through") + ";";
	}
}
