package com.xunw.zjxx.common.config;

/**
 * 学习微服务配置
 * <AUTHOR>
 *
 */
public class LearnServiceConfig {
	
	private String coursewareList;
	private String studyGetLearn;
	private String getById;
	private String getEnableCoursewareByCourseId;
	private String addStudyInfo;
	private String edit;
	private String add;
	private String remove;
	private String addLearn;
	private String addNotes;
	private String getLearnNotes;
	private String deleteLearnNote;
	private String addLearnComment;
	private String getLearnComments;
	private String getStudyMaterials;
	private String removeStudyMaterial;
	private String addExtConfig;
	private String removeExtConfig;
	private String addPlayQues;
	private String CoursewareProgress;
	private String CoursewareImport;

	public String getAdd() {
		return add;
	}

	public void setAdd(String add) {
		this.add = add;
	}

	public String getCoursewareList() {
		return coursewareList;
	}
	public void setCoursewareList(String coursewareList) {
		this.coursewareList = coursewareList;
	}
	public String getStudyGetLearn() {
		return studyGetLearn;
	}
	public void setStudyGetLearn(String studyGetLearn) {
		this.studyGetLearn = studyGetLearn;
	}
	public String getGetById() {
		return getById;
	}
	public void setGetById(String getById) {
		this.getById = getById;
	}
	public String getAddStudyInfo() {
		return addStudyInfo;
	}
	public void setAddStudyInfo(String addStudyInfo) {
		this.addStudyInfo = addStudyInfo;
	}
	public String getEdit() {
		return edit;
	}
	public void setEdit(String edit) {
		this.edit = edit;
	}
	public String getRemove() {
		return remove;
	}
	public void setRemove(String remove) {
		this.remove = remove;
	}
	public String getAddLearn() {
		return addLearn;
	}
	public void setAddLearn(String addLearn) {
		this.addLearn = addLearn;
	}
	public String getAddNotes() {
		return addNotes;
	}
	public void setAddNotes(String addNotes) {
		this.addNotes = addNotes;
	}
	public String getGetLearnNotes() {
		return getLearnNotes;
	}
	public void setGetLearnNotes(String getLearnNotes) {
		this.getLearnNotes = getLearnNotes;
	}
	public String getDeleteLearnNote() {
		return deleteLearnNote;
	}
	public void setDeleteLearnNote(String deleteLearnNote) {
		this.deleteLearnNote = deleteLearnNote;
	}
	public String getAddLearnComment() {
		return addLearnComment;
	}
	public void setAddLearnComment(String addLearnComment) {
		this.addLearnComment = addLearnComment;
	}
	public String getGetLearnComments() {
		return getLearnComments;
	}
	public void setGetLearnComments(String getLearnComments) {
		this.getLearnComments = getLearnComments;
	}

	public String getGetStudyMaterials() {
		return getStudyMaterials;
	}

	public void setGetStudyMaterials(String getStudyMaterials) {
		this.getStudyMaterials = getStudyMaterials;
	}

	public String getRemoveStudyMaterial() {
		return removeStudyMaterial;
	}

	public void setRemoveStudyMaterial(String removeStudyMaterial) {
		this.removeStudyMaterial = removeStudyMaterial;
	}

	public String getAddExtConfig() {
		return addExtConfig;
	}

	public void setAddExtConfig(String addExtConfig) {
		this.addExtConfig = addExtConfig;
	}

	public String getRemoveExtConfig() {
		return removeExtConfig;
	}

	public void setRemoveExtConfig(String removeExtConfig) {
		this.removeExtConfig = removeExtConfig;
	}

	public String getAddPlayQues() {
		return addPlayQues;
	}

	public void setAddPlayQues(String addPlayQues) {
		this.addPlayQues = addPlayQues;
	}

	public String getCoursewareProgress() {
		return CoursewareProgress;
	}

	public void setCoursewareProgress(String coursewareProgress) {
		CoursewareProgress = coursewareProgress;
	}

	public String getCoursewareImport() {
		return CoursewareImport;
	}

	public void setCoursewareImport(String coursewareImport) {
		CoursewareImport = coursewareImport;
	}

	public String getGetEnableCoursewareByCourseId() {
		return getEnableCoursewareByCourseId;
	}

	public void setGetEnableCoursewareByCourseId(String getEnableCoursewareByCourseId) {
		this.getEnableCoursewareByCourseId = getEnableCoursewareByCourseId;
	}
}
