package com.xunw.zjxx.common.utils;

import com.xunw.zjxx.common.config.RedisConfig;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.io.*;
import java.util.*;

public class CacheHelper {
	
	private static final Logger logger = LoggerFactory.getLogger(CacheHelper.class);
	
	private static String SYSTEM_FLAG = "ZJXX-";//分隔符标记
	
	private static JedisPool pool = null;
	
	static{
		try {
			RedisConfig redisConfig = SpringBeanUtils.getBean(RedisConfig.class);
		    JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
		    JedisPoolConfig poolConfig = new JedisPoolConfig();
		    poolConfig.setMaxTotal(redisConfig.getMaxActive());
	        poolConfig.setMaxIdle(redisConfig.getMaxIdle());
	        poolConfig.setMinIdle(redisConfig.getMinIdle());
	        poolConfig.setTestOnCreate(true);
	        poolConfig.setTestOnBorrow(false);
	        poolConfig.setTestOnReturn(true);
	        poolConfig.setTestWhileIdle(true);
	        String host = redisConfig.getHost();
	        Integer port = redisConfig.getPort();
	        String password = redisConfig.getPassword();
	        Integer database = redisConfig.getDatabase();
	        Integer timeout = redisConfig.getTimeout();
		    if(StringUtils.isEmpty(redisConfig.getPassword())){
		    	pool = new JedisPool(jedisPoolConfig,host,port,timeout,null,database);
		    }else{
		    	pool = new JedisPool(jedisPoolConfig,host,port,timeout,password,database);
		    }
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private static Jedis getResource(){
		return pool.getResource();
	}
	
	/**
     * 释放资源
     * @param jedis
     */
	private static void returnResource(Jedis jedis) {
        if (jedis != null) {
        	jedis.close();
        }
    }
	
	/**
     * 获取字符串
     * @param key 键
     * @return 值
     */
	public static <T> T getCache(String cacheName, String key) {
		T value = null;
		Jedis jedis = null;
		try {
            String newKey = SYSTEM_FLAG + cacheName + "-" + key;
			jedis = getResource();
			if (jedis !=null && jedis.exists(newKey)) {
				Object valueObj = unserizlize(jedis.get(getKeyBytes(newKey)));
				value = (T)valueObj;
			}
		} catch (Exception e) {
			logger.warn("获取缓存(cacheName = " + cacheName + "，key=" + key + ")失败，原因：" + e.getMessage(), e);
		} finally {
			returnResource(jedis);
		}
		return value;
	}
	
	/**
	 * 获取缓存
	 * 
	 * @param key
	 * @return
	 */
	public static Set<String> getCacheKeys(String cacheName) {
		Jedis jedis = null;
        try {
        	String newKey = SYSTEM_FLAG + cacheName + "-*";
            jedis = getResource();
            Set<String> keys = jedis.keys(newKey);
            Set<String> newKeys = new HashSet<String>(keys.size());
            int strLen = newKey.length() - 1;
            for(String key:keys){
            	newKeys.add(key.substring(strLen));
            }
            return newKeys;
        } catch (Exception e) {
            logger.warn("获取缓存[" + cacheName + "]的key列表失败，原因：" + e.getMessage(), e);
        } finally {
            returnResource(jedis);
        }
        return null;
	}
	
	/**
	 * 获取缓存
	 * 
	 * @param key
	 * @return
	 */
	public static Set<String> getAllCacheKeys(String cacheKeyPattern) {
		Jedis jedis = null;
        try {
            jedis = getResource();
            Set<String> keys = jedis.keys(cacheKeyPattern);
            return keys;
        } catch (Exception e) {
            logger.warn("获取缓存[" + cacheKeyPattern + "]的key列表失败，原因：" + e.getMessage(), e);
        } finally {
            returnResource(jedis);
        }
        return null;
	}
	
    /**
     * 设置永久对象缓存。需要执行removeCache之后才能清掉此缓存
     * @param key 键
     * @param value 值 
     * @return
     */
    public static void setCache(String cacheName, String key, Object value) {
        Jedis jedis = null;
        try {
            String newKey = SYSTEM_FLAG + cacheName + "-" + key;
            jedis = getResource();
            jedis.set(getKeyBytes(newKey), serialize(value));
        } catch (Exception e) {
        	e.printStackTrace();
            logger.warn("缓存(cacheName = " + cacheName + "，key=" + key + "，value=" + value + ")失败，原因：" + e.getMessage(), e);
        } finally {
            returnResource(jedis);
        }
    }
	
    /**
     * 设置有时间范围的对象缓存
     * @param key 键
     * @param value 值
     * @param milliseconds 过期时间，单位：毫秒，从设置时间起，过milliseconds之后该缓存就自动过期了
     * @return
     */
    public static void setCache(String cacheName, String key, Object value,long milliseconds) {
        Jedis jedis = null;
        try {
            String newKey = SYSTEM_FLAG + cacheName + "-" + key;
            jedis = getResource();
            jedis.set(getKeyBytes(newKey), serialize(value));
            jedis.pexpire(newKey, milliseconds);
        } catch (Exception e) {
        	e.printStackTrace();
            logger.warn("缓存(cacheName = " + cacheName + "，key=" + key + "，value=" + value + ")失败，原因：" + e.getMessage(), e);
        } finally {
            returnResource(jedis);
        }
    }
	
    /**
     * 设置有时间范围的对象缓存
     * @param key 键
     * @param value 值
     * @param expiredTime 过期时间
     * @return
     */
    public static void setCache(String cacheName, String key, Object value,Date expiredTime) {
        Jedis jedis = null;
        try {
            String newKey = SYSTEM_FLAG + cacheName + "-" + key;
            jedis = getResource();
            jedis.set(getKeyBytes(newKey), serialize(value));
            jedis.expireAt(newKey, expiredTime.getTime());
        } catch (Exception e) {
        	e.printStackTrace();
            logger.warn("缓存(cacheName = " + cacheName + "，key=" + key + "，value=" + value + ")失败，原因：" + e.getMessage(), e);
        } finally {
            returnResource(jedis);
        }
    }

	/**
	 * 删除缓存
	 * 
	 * @param key
	 * @return
	 */
	public static void removeCache(String cacheName, String key) {
		logger.info("删除缓存[" + cacheName + "][" + key + "]");
		Jedis jedis = null;
		try {
			String newKey = SYSTEM_FLAG + cacheName + "-" + key;
			jedis = getResource();
			if (jedis.exists(newKey)) {
				jedis.del(newKey);
			}
		} catch (Exception e) {
			logger.warn("删除缓存失败(cacheName = " + cacheName + "，key=" + key
					+ ")失败，原因：" + e.getMessage(), e);
		} finally {
			returnResource(jedis);
		}
	}

	/**
	 * 清除缓存
	 * 
	 * @param cacheName
	 */
	public static void removeCache(String cacheName) {
		logger.info("删除缓存[" + cacheName + "][*]");
		Jedis jedis = null;
        try {
        	String newKey = SYSTEM_FLAG + cacheName + "-*";
            jedis = getResource();
            Set<String> keys = jedis.keys(newKey);
            if(keys != null){
	            for(String key : keys){
	            	jedis.del(key);
	            }
            }
        } catch (Exception e) {
            logger.warn("删除缓存[" + cacheName + "][*]失败，原因：" + e.getMessage(), e);
        } finally {
            returnResource(jedis);
        }
	}

	/**
	 * 清空本项目全部所使用的Redis缓存，慎用，会导致服务无法正常运行。
	 * 
	 */
	public static void removeAllCache() {
		logger.info("清空本项目全部所使用的Redis缓存");
		Jedis jedis = null;
        try {
            jedis = getResource();
            Set<String> keys = jedis.keys(SYSTEM_FLAG + "*");
            if(keys != null){
	            for(String key : keys){
	            	jedis.del(key);
	            }
            }
        } catch (Exception e) {
            logger.warn("清空本项目全部所使用的Redis缓存失败，原因：" + e.getMessage(), e);
        } finally {
            returnResource(jedis);
        }
	}

	/**
	 * 清空Redis所有缓存，慎用，会导致相关所有服务无法正常运行。
	 * 
	 */
	public static void removeWholeCache() {
		logger.info("清空Redis所有缓存");
		Jedis jedis = null;
        try {
            jedis = getResource();
            Set<String> keys = jedis.keys("*");
            if(keys != null){
	            for(String key : keys){
	            	jedis.del(key);
	            }
            }
        } catch (Exception e) {
            logger.warn("清空Redis所有缓存失败，原因：" + e.getMessage(), e);
        } finally {
            returnResource(jedis);
        }
	}

	private static byte[] serialize(Object object) {
        ObjectOutputStream objectOutputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            objectOutputStream = new ObjectOutputStream(byteArrayOutputStream);
            objectOutputStream.writeObject(object);
            byte[] getByte = byteArrayOutputStream.toByteArray();
            return getByte;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
	
	private static Object unserizlize(byte[] binaryByte) {
        ObjectInputStream objectInputStream = null;
        ByteArrayInputStream byteArrayInputStream = null;
        byteArrayInputStream = new ByteArrayInputStream(binaryByte);
        try {
            objectInputStream = new ObjectInputStream(byteArrayInputStream);
            Object obj = objectInputStream.readObject();
            return obj;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
	
	private static byte[] getKeyBytes(String key){
		try {
			return key.getBytes("UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public static void main(String[] args) {
		String cacheName = "user";
		{
			String key = "abcd-123456";
	//		String[] value =  new String[]{"马涛","刘敏"};
			Map<String,Object> value = new HashMap<String, Object>();
			value.put("name", "马涛");
			value.put("age", 33);
			setCache(cacheName, key, value);
			Map<String,Object> value2 = getCache(cacheName, key);
			System.out.println(value2);
		}
		{
			String key = "abcd-23456";
	//		String[] value =  new String[]{"马涛","刘敏"};
			Map<String,Object> value = new HashMap<String, Object>();
			value.put("name", "刘敏");
			value.put("age", 32);
			setCache(cacheName, key, value);
			Map<String,Object> value2 = getCache(cacheName, key);
			System.out.println(value2);
		}
		System.out.println(getCacheKeys(cacheName));
		String key = "abcd-23456";
		removeCache(cacheName);
		Map<String,Object> value2 = getCache(cacheName, key);
		System.out.println(value2);
		removeWholeCache();
	}
	
}
