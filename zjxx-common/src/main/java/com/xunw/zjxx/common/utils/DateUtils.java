package com.xunw.zjxx.common.utils;

import com.xunw.zjxx.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 * <AUTHOR>
 */
public class DateUtils {
	
	/**
	 * 向前退或向后加若干分钟
	 * @param date 		指定的日期
	 * @param minute	加减值(传入负数表示向前退)
	 * @return 改变后的时间
	 */
    public static Date addMinute(Date date, int minute) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minute);
        return calendar.getTime();
    }
    
    /**
	 * 向前退或向后加若干年
	 * @param date 		指定的日期
	 * @param minute	加减值(传入负数表示向前退)
	 * @return 改变后的时间
	 */
    public static Date addYear(Date date, int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, year);
        return calendar.getTime();
    }
    
    /**
   	 * 向前退或向后加若干月
   	 * @param date 		指定的日期
   	 * @param minute	加减值(传入负数表示向前退)
   	 * @return 改变后的时间
   	 */
    public static Date addMonth(Date date, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, month);
        return calendar.getTime();
    }
    
    /**
   	 * 向前退或向后加若干天
   	 * @param date 		指定的日期
   	 * @param minute	加减值(传入负数表示向前退)
   	 * @return 改变后的时间
   	 */
    public static Date addDay(Date date, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, day);
        return calendar.getTime();
    }

    /**
     * 计算时间差
     */
    public static String diff(Date begin, Date end) {
        long diff = end.getTime() - begin.getTime();
        long day = diff / (24 * 60 * 60 * 1000);
        long hour = (diff / (60 * 60 * 1000) - day * 24);
        long minute = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long second = (diff / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - minute * 60);
        StringBuffer result = new StringBuffer();
        if (day > 0) {
            result.append(day).append("天");
        }
        if (hour > 0) {
            result.append(hour).append("小时");
        }
        result.append(minute).append("分");
        result.append(second).append("秒");
        return result.toString();
    }

	/**
	 * 计算时间差
	 */
	public static long diffSecond(Date begin, Date end) {
		return (end.getTime() - begin.getTime()) / 1000;
	}
    
    /**
     * 判断指定的时间是否在某一个时间段范围内
     * true 是    false 否
     */
    public static boolean isBetween(Date target, Date begin, Date end) {
       if (target == null || begin == null || end == null) {
    	   throw BizException.withMessage("[target,begin,end]参数均不能够为空");
       }
       else {
    	   if (target.getTime() >= begin.getTime() && target.getTime() <= end.getTime()) {
    		   return true;
    	   }
    	   else {
    		   return false;
    	   }
       }
    }


    /**
     * 格式化日期，使用格式:yyyy-MM-dd HH:mm:ss
     */
    public static String format(Date datetime) {
    	return format(datetime, "yyyy-MM-dd HH:mm:ss");
    }
    
    public static String format(Date datetime, String pattern) {
    	if(datetime == null) {
    		return null;
    	}
    	SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
    	return dateFormat.format(datetime);
    }
    
    public static String getCurrentDate() {
    	return format(new Date(), "yyyy-MM-dd HH:mm:ss");
    }
    
    public static String getCurrentDate(String pattern) {
    	return format(new Date(), pattern);
    }
    
    /**
     * 日期字符串转成对象，使用格式:yyyy-MM-dd HH:mm:ss
     */
    public static Date parse(String datetime) {
    	return parse(datetime, "yyyy-MM-dd HH:mm:ss");
    }
    
    public static Date parse(String datetime, String pattern) {
    	try {
			if(StringUtils.isEmpty(datetime)) {
				return null;
			}
			SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
			return dateFormat.parse(datetime);
		} catch (ParseException e) {
			throw BizException.withMessage("日期转换错误");
		}
    }
    
    public static Date now() {
    	return new Date();
    }

	/**
	 * 得到当前在本周及几天
	 */
	public static String getWeek() {
		int week = Calendar.getInstance().get(Calendar.DAY_OF_WEEK);
		switch (week) {
			case 1:
				return "星期日";
			case 2:
				return "星期一";
			case 3:
				return "星期二";
			case 4:
				return "星期三";
			case 5:
				return "星期四";
			case 6:
				return "星期五";
			case 7:
				return "星期六";
			default:
				return null;
		}
	}
	
	public static void main(String[] args) {
		if(DateUtils.parse("2022-11-01 23:33:23").after(DateUtils.parse("2022-11-01 23:33:23"))) {
			System.out.println("-----1----");
		}
		else {
			System.out.println("-----2----");
		}
	}
}