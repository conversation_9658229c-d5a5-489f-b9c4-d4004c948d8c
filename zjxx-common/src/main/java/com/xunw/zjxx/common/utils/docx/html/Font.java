package com.xunw.zjxx.common.utils.docx.html;


public class Font extends HtmlElement{

	private String color;
	private String size;
	private String family;

	public Font(String color, String size, String family) {
		this.color = color;
		this.size = size;
		this.family = ("宋体".equals(family) || "eastAsia".equals(family)) ? "" : family;//忽略默认字体
	}

	@Override
	public String head() {
		return "<span style=\"" + (color.equals("") ? "" : ("color:" + color + ";"))
				 + (size.equals("") ? "" : ("font-size:" + size + ";"))
				 + (family.equals("") ? "" : ("font-family:" + family + ";")) + "\">";
	}

	@Override
	public String tail() {
		return "</span>";
	}

	@Override
	public String getStyleStr() {
		return (color.equals("") ? "" : ("color:" + color + ";"))
				 + (size.equals("") ? "" : ("font-size:" + size + ";"))
				 + (family.equals("") ? "" : ("font-family:" + family + ";"));
	}

}
