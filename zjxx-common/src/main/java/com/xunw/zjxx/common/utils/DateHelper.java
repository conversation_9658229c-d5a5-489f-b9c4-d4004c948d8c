package com.xunw.zjxx.common.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateHelper {

	private static Calendar					gobalCalendar						= Calendar.getInstance();
	public final static SimpleDateFormat	shortSimpleDateFormat				= new SimpleDateFormat("yyyy-MM-dd");
	public final static SimpleDateFormat	longSimpleDateFormat				= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	public final static SimpleDateFormat	shortSimpleDateFormatWithoutSplit	= new SimpleDateFormat("yyyyMMdd");
	public final static SimpleDateFormat	longSimpleDateFormatWithoutSplit	= new SimpleDateFormat("yyyyMMddHHmmss");
	public final static String[]			WeekNames							= new String[] { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
	public final static String[]			MonthNames							= new String[] { "一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月" };

	public final static int					DaySeconds							= 86400;
	public final static int					HourSeconds							= 3600;

	public static Date parseLongString(String source) {
		try {
			return longSimpleDateFormat.parse(source);
		} catch (Exception e) {
			return null;
		}
	}

	public static Date parseShorString(String source) {
		try {
			return shortSimpleDateFormat.parse(source);
		} catch (Exception e) {
			return null;
		}
	}
	
	public static Date parseString(String source) {
		source = source.trim();
		if(source.contains(" ")){
			return parseLongString(source);
		}else{
			return parseShorString(source);
		}
	}

	/*****
	 * 根据年、月、日设置日期，并返回设置好的日期
	 * 
	 * @param year
	 * @param month
	 * @param date
	 */
	public static Date setDate(int year, int month, int date) {
		gobalCalendar.set(year, month, date);
		return gobalCalendar.getTime();
	}

	/****
	 * 按照yyyy-MM-dd格式返回当前的日期
	 */
	public static String toShortString() {
		return toShortString(new Date());
	}

	/****
	 * 按照yyyy-MM-dd格式返回date日期
	 */
	public static String toShortString(Date date) {
		return shortSimpleDateFormat.format(date);
	}

	/****
	 * 按照yyyy-MM-dd HH:mm:ss格式返回当前的日期
	 * 
	 * @return
	 */
	public static String toLongString() {
		return toLongString(new Date());
	}

	/****
	 * 按照yyyy-MM-dd HH:mm:ss格式返回date日期
	 * 
	 * @return
	 */
	public static String toLongString(Date date) {
		return longSimpleDateFormat.format(date);
	}

	public static Date addValues(int filed, int value) {
		return addValues(new Date(), filed, value);
	}
	
	public static final String formatDate(Date date,String dateFormatStr){
		SimpleDateFormat format	= new SimpleDateFormat(dateFormatStr);
		return format.format(date);
	}

	/********
	 * 为date日期增加的数值
	 */
	public static Date addValues(Date date, int filed, int value) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.add(filed, value);
		return c.getTime();
	}

	/***********
	 * 验证自定义时间表达式是否正确
	 */
	/*public static boolean checkSecondByValue(String source) {
		return checkSecondByValue(source, 0);
	}*/

	/***********
	 * 验证自定义时间表达式是否正确,并判断是否小于最小值
	 */
	/*public static boolean checkSecondByValue(String source, int minSecond) {
		Integer value = null;
		try {
			value = getSecondByValue(source);
		} catch (Exception e) {
			return false;
		}
		if (value < minSecond)
			return false;
		return true;
	}*/

	/****
	 * 对字符串进行时间转换，转换成秒 Source 格式 1d2h3m4s 1:49 d:100 2:50 h:104 3:51 m:109 4:52
	 * s:115
	 */
	/*public static Integer getSecondByValue(String source) {
		if (StringHelper.isEmpty(source))
			return null;
		source = source.trim();
		String value = "时间格式错误：请按照格式 1d2h3m4s(d:天,h小时,m分钟,s秒)";
		String number = "";
		// 倍数
		int seconds = 0;
		boolean d = false, h = false, m = false, s = false;
		for (int i = 0; i < source.length(); i++) {
			char c = source.charAt(i);
			int tmp = (int) c;
			if (tmp == 100) {
				if (d)
					LogHelper.log(value);
				try {
					seconds = seconds + Integer.parseInt(number) * 24 * 60 * 60;
				} catch (Exception e) {
					LogHelper.log(value);
				}
				d = true;
				number = "";
			} else if (tmp == 104) {
				if (h)
					LogHelper.log(value);
				// h
				try {
					seconds = seconds + Integer.parseInt(number) * 60 * 60;
				} catch (Exception e) {
					LogHelper.log(value);
				}
				h = true;
				number = "";
			} else if (tmp == 109) {
				if (m)
					LogHelper.log(value);
				// m
				try {
					seconds = seconds + Integer.parseInt(number) * 60;
				} catch (Exception e) {
					LogHelper.log(value);
				}
				m = true;
				number = "";
			} else if (tmp == 115) {
				if (s)
					LogHelper.log(value);
				try {
					seconds = seconds + Integer.parseInt(number);
				} catch (Exception e) {
					LogHelper.log(value);
				}
				s = true;
				number = "";
				// s
			} else if (tmp >= 48 && tmp <= 57) {
				number = number + c;
			} else {
				LogHelper.log(value);
			}
		}
		return seconds;
	}*/

	/******
	 * 返回中文日期 2010年11月11日 星期二
	 */
	public static String toChineseString() {
		return toChineseString(new Date());
	}

	/******
	 * 返回中文日期 2010年11月11日 星期二
	 */
	public static String toChineseString(Date date) {
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
		return sdf.format(date) + " " + WeekNames[c.get(Calendar.DAY_OF_WEEK) - 1];
	}

	public static String getTicketTimes(Date maxTime, Date minTime) {
		if (maxTime == null || minTime == null) {
			return "";
		}
		long times = maxTime.getTime() - minTime.getTime();
		if (times < 0) {
			times = 0 - times;
		}
		int value = Integer.parseInt(times + "");

		int day = value / (DaySeconds * 1000);
		value = value - day * DaySeconds * 1000;

		int hour = value / (HourSeconds * 1000);
		value = value - hour * HourSeconds * 1000;

		int minute = value / 6000;

		value = value - minute * 6000;

		int second = value / 1000;

		String returnValue = "";
		if (day > 0) {
			returnValue = day + "d";
		}
		if (hour > 0) {
			returnValue = returnValue + hour + "h";
		}
		if (minute > 0) {
			returnValue = returnValue + minute + "m";
		}
		if (second > 0) {
			returnValue = returnValue + second + "s";
		}
		return returnValue;

	}



	/**
	 *
	 * @param stringDate 日期字符串
	 * @param formatType 格式化模式
	 * @return
	 * @throws ParseException
	 */
	public static Date stringTodate(String stringDate,String formatType) throws ParseException {
		SimpleDateFormat sdf = new SimpleDateFormat(formatType);
		Date date = sdf.parse(stringDate);
		return date;
	}



}
