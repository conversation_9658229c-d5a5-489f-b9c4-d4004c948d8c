/*
 * *************************************************
 * Copyright (c) 2018 QMTH. All Rights Reserved.
 * Created by <PERSON><PERSON><PERSON> on 2018-06-08 10:24:34.
 * *************************************************
 */

package com.xunw.zjxx.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.regex.Pattern;

public class StrUtils {
	
	private static Pattern linePattern = Pattern.compile("_(\\w)");
	
    private static Logger log = LoggerFactory.getLogger(StrUtils.class);

    /**
     * (乱码)从MySQL的JSON字段中取出需要转码
     */
    public static String toUTF8(String value) {
        try {
            if (value != null) {
                return new String(value.getBytes("iso-8859-1"), "utf-8");
            }
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }
        return value;
    }

    /**
     * 去掉头尾"p"标签
     */
    public static String pTagReplace(String str) {
        if (str == null) {
            return null;
        }
        if (str.startsWith("<p>")) {
            //去掉头<p >
            str = str.replaceFirst("<p>", "");
            //去掉尾</p>
            int index = str.lastIndexOf("</p>");
            if (index > 0) {
                str = str.substring(0, index);
            }
        }
        return str;
    }
 
}