package com.xunw.zjxx.common.utils;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.read.biff.BiffException;
import jxl.write.*;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

public class OfficeToolExcel {

	/**
	 * 导出EXCEL
	 * 
	 * @param filepath
	 * @param title
	 * @param list
	 * @param cell_length
	 * @throws Exception
	 */
	public static void makeExcel(OutputStream os, String title, List<Map<Object, Object>> list, int[] cell_length)
			throws Exception {
		WritableWorkbook wwb = Workbook.createWorkbook(os);
		WritableSheet ws = wwb.createSheet("Sheet1", 0);

		if ((list == null) || (cell_length == null)) {
			throw new Exception("parameters is null");
		}

		ws.mergeCells(0, 0, cell_length.length - 1, 1);
		Label header = new Label(0, 0, title, getHeader());
		ws.addCell(header);

		int row = 2;
		for (Map<Object, Object> map : list) {
			int col = 0;
			for (Iterator<Object> localIterator2 = map.keySet().iterator(); localIterator2.hasNext();) {
				Object obj = localIterator2.next();
				if (row == 2) {
					Label label = new Label(col, row, String.valueOf(obj), getTitle());
					ws.addCell(label);
				} else {
					ws.addCell(new Label(col, row, String.valueOf(map.get(obj)), getNormolCell()));
				}
				ws.setColumnView(col, cell_length[col]);
				++col;
			}
			++row;
		}

		wwb.write();
		wwb.close();
	}

	public static List<Map<String, String>> readExcel(File filename, String[] alisa) throws BiffException, IOException {
		List<Map<String, String>> list = new ArrayList<Map<String, String>>();
		Workbook wb = Workbook.getWorkbook(filename);
		Sheet s = wb.getSheet(0);
		Cell c = null;
		int row = s.getRows();
		int col = s.getColumns();
		for (int i = 0; i < row; ++i) {
			Map<String, String> map = new HashMap<String, String>();
			for (int j = 0; j < col && j < alisa.length; ++j) {
				c = s.getCell(j, i);
				map.put(alisa[j], BaseUtil.getCellText(c));
			}
			list.add(map);
		}
		return list;
	}

	public static WritableCellFormat getHeader() {
		WritableFont font = new WritableFont(WritableFont.TIMES, 11, WritableFont.BOLD);
		try {
			font.setColour(Colour.BLACK);
		} catch (WriteException e1) {
			e1.printStackTrace();
		}
		WritableCellFormat format = new WritableCellFormat(font);
		try {
			format.setAlignment(Alignment.CENTRE);
			format.setVerticalAlignment(VerticalAlignment.CENTRE);
			format.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
			format.setBackground(Colour.GRAY_50);
		} catch (WriteException e) {
			e.printStackTrace();
		}
		return format;
	}

	public static WritableCellFormat getTitle() {
		WritableFont font = new WritableFont(WritableFont.TIMES, 10, WritableFont.BOLD);
		try {
			font.setColour(Colour.BLACK);
		} catch (WriteException e1) {
			e1.printStackTrace();
		}
		WritableCellFormat format = new WritableCellFormat(font);
		try {
			format.setAlignment(Alignment.CENTRE);
			format.setVerticalAlignment(VerticalAlignment.CENTRE);
			format.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
			format.setBackground(Colour.GRAY_25);
		} catch (WriteException e) {
			e.printStackTrace();
		}
		return format;
	}

	public static WritableCellFormat getNormolCell() {
		WritableFont font = new WritableFont(WritableFont.TIMES, 10);
		WritableCellFormat format = new WritableCellFormat(font);
		try {
			format.setAlignment(Alignment.CENTRE);
			format.setVerticalAlignment(VerticalAlignment.CENTRE);
			format.setBorder(Border.ALL, BorderLineStyle.THIN, Colour.BLACK);
		} catch (WriteException e) {
			e.printStackTrace();
		}
		return format;
	}
}