package com.xunw.zjxx.common.utils;

import org.apache.commons.io.ByteOrderMark;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;

import java.io.*;
import java.text.DecimalFormat;

/**
 * 支持csv xls xlsx 
 * <AUTHOR>
 *
 */
public class FileData {
	
    public static final DecimalFormat NUMBER_FORMATOR = new DecimalFormat("#######.##");

    private BufferedReader reader;

    private String splite;

    private String line;

    private String[] datas;
    
    private int rowIndex;
    
    private int columnCount;
    
    private Sheet sheet;

    public FileData(String filename, String splite) throws IOException, InvalidFormatException {
    	if(getExtension(filename).endsWith("csv")){
    		BOMInputStream bomIn = new BOMInputStream(new FileInputStream(filename), false,ByteOrderMark.UTF_8);
    		String charset = bomIn.hasBOM() ? bomIn.getBOMCharsetName() : "utf-8";
    		this.reader = new BufferedReader(new InputStreamReader(bomIn,charset));
    		this.splite = splite;
    		this.line = null;
    	}
    	if(getExtension(filename).equals("xls") || getExtension(filename).equals("xlsx")){
    	    Workbook wb = WorkbookFactory.create(new File(filename));
            this.sheet = wb.getSheetAt(0);
            rowIndex = 0;
        	this.splite = splite;
    		this.line = null;
    	}
    }
    
    public FileData(File file, String splite) throws IOException, InvalidFormatException {
    	if(getExtension(file.getName()).endsWith("csv")){
    		BOMInputStream bomIn = new BOMInputStream(new FileInputStream(file), false,ByteOrderMark.UTF_8);
    		String charset = bomIn.hasBOM() ? bomIn.getBOMCharsetName() : "utf-8";
    		this.reader = new BufferedReader(new InputStreamReader(bomIn,charset));
    		this.splite = splite;
    		this.line = null;
    	}
    	if(getExtension(file.getName()).equals("xls") || getExtension(file.getName()).equals("xlsx")){
    	    Workbook wb = WorkbookFactory.create(new File(file.getName()));
            this.sheet = wb.getSheetAt(0);
            rowIndex = 0;
        	this.splite = splite;
    		this.line = null;
    	}
    }
    
    public String getData(int column) {
        if (datas != null && datas.length > column) {
            return datas[column];
        } else {
            return null;
        }
    }

    public boolean nextLine() {
        try {
        	if(reader != null){
        		this.line = reader.readLine();
        		//此处不能够去除空白字符串，否则会导致可变的数组长度
        		this.datas = StringUtils.splitByWholeSeparatorPreserveAllTokens(line, splite);
        		return line != null;
        	}
        	if(sheet != null){
        		if(rowIndex > sheet.getLastRowNum()){
        			return false;
        		}
        		else{
        			if(rowIndex == 0){
        				columnCount = this.sheet.getRow(rowIndex).getPhysicalNumberOfCells();
        			}
        			Row row = this.sheet.getRow(rowIndex);
        			StringBuffer stringBuffer = new StringBuffer();
        			for (int i=0; i < columnCount; i++) {
        				Cell cell = row.getCell(i);
        				String content = "";
        				if(cell != null){
        					 if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                 content = NUMBER_FORMATOR.format(cell.getNumericCellValue());
                             } else if (cell.getCellType() == Cell.CELL_TYPE_STRING) {
                                 content = StringUtils.trimToNull(cell.getStringCellValue());
                             }
        				}
        				stringBuffer.append(content).append(splite);
        			}
        			stringBuffer = stringBuffer.deleteCharAt(stringBuffer.length()-1);
        			this.line = stringBuffer.toString();
        			System.out.println("line:" + this.line);
        			this.datas = StringUtils.splitByWholeSeparatorPreserveAllTokens(line, splite);
        			System.out.println("datas:" + this.datas);
        			rowIndex ++;
        			return true;
        		}
        	}
        } catch (Exception e) {
        }
        return false;
    }

    public int getColumnCount() {
        return datas != null ? datas.length : 0;
    }

    public String[] getDatas() {
        return datas;
    }
  
    public String getLine() {
        if (line != null) {
            return line;
        } else {
            return null;
        }
    }

    public void close() {
        try {
        	if(reader!=null){
        		reader.close();
        	}
        } catch (Exception e) {
        }
    }
    
    /**
	 * 获取文件的后缀
	 */
	protected String getExtension(String fileName) {
		int k = fileName.lastIndexOf(".");
		String ext = "";
		if (k > 0) {
			ext = fileName.substring(k + 1, fileName.length());
		}
		return ext.toLowerCase();
	}
    
    
//    public static void main(String[] args) {
//    	String string = "1~核心能力测评~1~021711006180~高飞龙~201701~语文素养与应用~2018-02-01 09:36:27~1~核心能力测评~1~";
//		System.out.println(StringUtils.split(string,"~").length);
//		System.out.println(StringUtils.splitByWholeSeparator(string,"~").length);
//	}
}
