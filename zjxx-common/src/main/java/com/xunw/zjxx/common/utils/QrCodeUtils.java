package com.xunw.zjxx.common.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.xunw.zjxx.common.exception.BizException;
import org.apache.commons.io.IOUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.Hashtable;

/**
 * 二维码工具类
 * 
 * <AUTHOR>
 */
public class QrCodeUtils {

	private static final String CHARSET = "utf-8";

	// LOGO宽度
	private static final int LOGO_WIDTH = 80;

	// LOGO高度
	private static final int LOGO_HEIGHT = 80;

	/**
	 * 生成二维码
	 * 
	 * @param content 二维码内容
	 * @param width   二维码宽度
	 * @param height  二维码高度
	 * @return base64编码的二维码图片
	 * @throws Exception
	 */
	public static String createQrCode(String content, int width, int height) throws Exception {
		return createQrCode(content, width, height, null);
	}

	/**
	 * 生成二维码
	 * 
	 * @param content   二维码内容
	 * @param width     二维码宽度
	 * @param height    二维码高度
	 * @param logoImage LOGO图片
	 * @return base64编码的二维码图片
	 * @throws Exception
	 */
	public static String createQrCode(String content, int width, int height, File logoImage) throws Exception {
		InputStream logoImageIs = null;
		try {
			if (logoImage != null) {
				if (!logoImage.exists()) {
					throw BizException.withMessage("LOGO不存在:" + logoImage.getAbsolutePath());
				} else {
					logoImageIs = new FileInputStream(logoImage);
				}
			}
			BufferedImage image = createQrCode(content, width, height, logoImageIs, true);
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			ImageIO.write(image, "png", out);
			byte[] bytes = out.toByteArray();
			String binary = Base64ConvertUtil.encode(bytes).trim();
			StringBuffer base64Img = new StringBuffer();
			base64Img.append("data:image/png;base64,");
			base64Img.append(binary);
			String qrcodeBase64 = base64Img.toString();
			return qrcodeBase64;
		} finally {
			if (logoImageIs != null) {
				IOUtils.closeQuietly(logoImageIs);
			}
		}
	}

	/**
	 * 生成二维码
	 * 
	 * @param content   二维码内容
	 * @param width     二维码宽度
	 * @param height    二维码高度
	 * @param logoImage LOGO图片
	 * @param destImage 二维码图片存放地址
	 * @throws Exception
	 */
	public static void createQrCode(String content, int width, int height, File logoImage, File destImage)
			throws Exception {
		InputStream logoImageIs = null;
		OutputStream destImageOs = null;
		try {
			if (logoImage != null) {
				if (!logoImage.exists()) {
					throw BizException.withMessage("LOGO不存在:" + logoImage.getAbsolutePath());
				} else {
					logoImageIs = new FileInputStream(logoImage);
				}
			}
			destImageOs = new FileOutputStream(destImage);
			ImageIO.write(createQrCode(content, width, height, logoImageIs, true), "png", destImage);
		} finally {
			if (logoImageIs != null) {
				IOUtils.closeQuietly(logoImageIs);
			}
			if (destImageOs != null) {
				IOUtils.closeQuietly(destImageOs);
			}
		}
	}

	/**
	 * 生成二维码
	 * 
	 * @param content   二维码内容
	 * @param width     二维码宽度
	 * @param height    二维码高度
	 * @param logoImage LOGO图片
	 * @param destImage 二维码图片存放地址
	 * @throws Exception
	 */
	public static void createQrCode(String content, int width, int height, File logoImage, OutputStream destImage)
			throws Exception {
		InputStream logoImageIs = null;
		try {
			if (logoImage != null) {
				if (!logoImage.exists()) {
					throw BizException.withMessage("LOGO不存在:" + logoImage.getAbsolutePath());
				} else {
					logoImageIs = new FileInputStream(logoImage);
				}
			}
			ImageIO.write(createQrCode(content, width, height, logoImageIs, true), "png", destImage);
		} finally {
			if (logoImageIs != null) {
				IOUtils.closeQuietly(logoImageIs);
			}
			if (destImage != null) {
				IOUtils.closeQuietly(destImage);
			}
		}
	}

	/**
	 * 生成二维码(不带LOGO)
	 * 
	 * @param content   二维码内容
	 * @param width     二维码宽度
	 * @param height    二维码高度
	 * @param logoImage LOGO图片
	 * @param destImage 二维码图片存放地址
	 * @throws Exception
	 */
	public static void createQrCodeWithOutLogo(String content, int width, int height, OutputStream destImage)
			throws Exception {
		try {
			ImageIO.write(createQrCode(content, width, height, null, true), "png", destImage);
		} finally {
			if (destImage != null) {
				IOUtils.closeQuietly(destImage);
			}
		}

	}

	/**
	 * 生成二维码
	 * 
	 * @param content   二维码内容
	 * @param width     二维码宽度
	 * @param height    二维码高度
	 * @param logoImage LOGO图片URL
	 * @param destImage 二维码图片存放地址
	 * @throws Exception
	 */
	public static void createQrCode(String content, int width, int height, String logoImageUrl, OutputStream destImage)
			throws Exception {
		URL realUrl = new URL(logoImageUrl);
		// 打开和URL之间的连接
		URLConnection conn = realUrl.openConnection();
		// 设置通用的请求属性
		conn.setRequestProperty("accept", "*/*");
		conn.setRequestProperty("connection", "Keep-Alive");
		conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
		createQrCode(content, width, height, conn.getInputStream(), destImage);
	}

	/**
	 * 生成二维码
	 * 
	 * @param content   二维码内容
	 * @param width     二维码宽度
	 * @param height    二维码高度
	 * @param logoImage LOGO图片
	 * @param destImage 二维码图片存放地址
	 * @throws Exception
	 */
	public static void createQrCode(String content, int width, int height, InputStream logoImage,
			OutputStream destImage) throws Exception {
		try {
			ImageIO.write(createQrCode(content, width, height, logoImage, true), "png", destImage);
		} finally {
			if (logoImage != null) {
				IOUtils.closeQuietly(logoImage);
			}
			if (destImage != null) {
				IOUtils.closeQuietly(destImage);
			}
		}
	}

	/**
	 * 生成二维码
	 * 
	 * @param content   二维码内容
	 * @param width     二维码宽度
	 * @param height    二维码高度
	 * @param logoImage LOGO图片
	 * @param destImage 二维码图片存放地址
	 * @throws Exception
	 */
	public static void createQrCode(String content, int width, int height, InputStream logoImage, String destImagePath)
			throws Exception {
		try {
			ImageIO.write(createQrCode(content, width, height, logoImage, true), "png", new File(destImagePath));
		} finally {
			if (logoImage != null) {
				IOUtils.closeQuietly(logoImage);
			}
		}
	}
	
	/**
	 * 创建无白色边距的二维码
	 * @param content
	 * 				扫码后的跳转地址
	 * @param width
	 * 				宽度
	 * @param height
	 * 				高度
	 * @return
	 * 				二维码图片
	 * @throws WriterException 
	 * @throws Exception
	 */
	public static BufferedImage createQrCodeWithNoWhiteSpace(String content, int width, int height) throws WriterException {
		Hashtable<EncodeHintType, Object> hints = new Hashtable<EncodeHintType, Object>();
		hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
		hints.put(EncodeHintType.CHARACTER_SET, CHARSET);
		hints.put(EncodeHintType.MARGIN, 1);
		BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
		bitMatrix = deleteWhite(bitMatrix);//删除白色边距
		int qrCodeWidth = bitMatrix.getWidth();
		int qrCodeHeight = bitMatrix.getHeight();
		BufferedImage qrCodeimage = new BufferedImage(qrCodeWidth, qrCodeHeight, BufferedImage.TYPE_INT_RGB);
		for (int x = 0; x < qrCodeWidth; x++) {
			for (int y = 0; y < qrCodeHeight; y++) {
				qrCodeimage.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
			}
		}
		return qrCodeimage;
	}

	private static BufferedImage createQrCode(String content, int width, int height, InputStream logoImage,
			boolean needCompress) throws Exception {
		Hashtable<EncodeHintType, Object> hints = new Hashtable<EncodeHintType, Object>();
		hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
		hints.put(EncodeHintType.CHARACTER_SET, CHARSET);
		hints.put(EncodeHintType.MARGIN, 1);
		BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
		int qrCodeWidth = bitMatrix.getWidth();
		int qrCodeHeight = bitMatrix.getHeight();
		BufferedImage qrCodeimage = new BufferedImage(qrCodeWidth, qrCodeHeight, BufferedImage.TYPE_INT_RGB);
		for (int x = 0; x < qrCodeWidth; x++) {
			for (int y = 0; y < qrCodeHeight; y++) {
				qrCodeimage.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
			}
		}
		// 无LOGO，直接返回
		if (logoImage == null) {
			return qrCodeimage;
		}
		// 插入图片
		insertImage(qrCodeimage, logoImage, needCompress);
		return qrCodeimage;
	}

	/**
	 * 二维码插入LOGO图片
	 * 
	 * @param qrCodeImage  二维码原始图片
	 * @param logoImage    LOGO图片
	 * @param needCompress 是否需要缩放LOGO
	 * @throws Exception
	 */
	private static void insertImage(BufferedImage qrCodeImage, InputStream logoImage, boolean needCompress)
			throws Exception {
		Image src = ImageIO.read(logoImage);
		int width = src.getWidth(null);
		int height = src.getHeight(null);
		if (needCompress) { // 缩放LOGO
			if (width > LOGO_WIDTH) {
				width = LOGO_WIDTH;
			}
			if (height > LOGO_HEIGHT) {
				height = LOGO_HEIGHT;
			}
			Image image = src.getScaledInstance(width, height, Image.SCALE_SMOOTH);
			BufferedImage tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
			Graphics g = tag.getGraphics();
			g.drawImage(image, 0, 0, null);
			g.dispose();
			src = image;
		}
		// 插入LOGO
		Graphics2D graph = qrCodeImage.createGraphics();
		int x = (qrCodeImage.getWidth(null) - width) / 2;
		int y = (qrCodeImage.getHeight(null) - height) / 2;
		graph.drawImage(src, x, y, width, height, Color.white, null);
		graph.dispose();
	}

	/**
	 * 删除二维码周边的白色间距
	 */
	private static BitMatrix deleteWhite(BitMatrix matrix) {
		int[] rec = matrix.getEnclosingRectangle();
		int resWidth = rec[2] + 1;
		int resHeight = rec[3] + 1;

		BitMatrix resMatrix = new BitMatrix(resWidth, resHeight);
		resMatrix.clear();
		for (int i = 0; i < resWidth; i++) {
			for (int j = 0; j < resHeight; j++) {
				if (matrix.get(i + rec[0], j + rec[1]))
					resMatrix.set(i, j);
			}
		}
		return resMatrix;
	}

	public static void main(String[] args) throws Exception {
//		String content = "https://st-jxjy.whxunw.com/zyjd_learning";
//		String content = "https://gt-jxjy.whxunw.com/zyjd_learning";
//		String content="http://202.114.251.226:8021";
//		String content = "https://adult-regist.whxunw.com/register?batchId=1";
		String content = "https://0149-xwwybm.whxunw.com/h5/#/project_introduction?xmId=acf720192d4-03a3-4ec2-a2c2-76189nmg39a";
//		String content = "http://jxjy-test.whxunw.com/kaosheng/mobile/zyjd/bm/startPage/58DD93DE8C5E48AA999FD947AEC73869";
//		File logoPath = new File("/Volumes/D/TEMP/hbzk_mobole_logo.png");
//		String qrCode = createQrCode(content, 400, 400, logoPath);
//		System.out.println(qrCode);

//		createQrCode(content, 200, 200, null,
//				new File("/Users/<USER>/Documents/D_SOFT/TEMP/xy-tqc-" + System.currentTimeMillis() + ".png"));
		File file = new File("/Users/<USER>/Documents/D_SOFT/TEMP/0149-wkd-xwwy-bm" + System.currentTimeMillis() + ".png");
		file.createNewFile();
		FileOutputStream outputStream = new FileOutputStream(file);
		createQrCode(content, 250, 250, "https://jxjy-att.whxunw.com/training_sas_att/upload/images/2023030418/5CF2E41F0B86499090BC1C7C74E27ED1.jpeg",
				outputStream);
//		String aString = createQrCode(content,360,360);
//		System.out.println(aString);

//		File qrcode = new File("D:\\TIANJUN\\temp\\online_wx.jpg");

		// 不含Logo
		// QrCodeUtils2.encode(text, null, "/Users/<USER>/Documents/picture",
		// true);
		// 含Logo，不指定二维码图片名
		// QrCodeUtils2.encode(text,
		// "/Users/<USER>/Documents/picture/google-icon.jpg",
		// "/Users/<USER>/Documents/picture/", true);
		// 含Logo，指定二维码图片名
		// QrCodeUtils2.encode(text,
		// "/Users/<USER>/Documents/picture/google-icon.jpg",
		// "/Users/<USER>/Documents/picture", "qrcode", true);

//		System.out.println("https://apps.apple.com/us/app/"+URLEncoder.encode("匠心云学堂","UTF-8")+"/id1536716715");

//		int a = (int) (90 / 40);
		System.out.println(1);
	}

}
