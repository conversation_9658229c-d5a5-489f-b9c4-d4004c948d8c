package com.xunw.zjxx.common.utils.docx.num;

import java.util.HashMap;
import java.util.Map;

public class AbstractNum {
	private String id;
	private String multiLevelType;//hybridMultilevel（项目符号或者编号） 或者  multilevel（多级列表）
	private Map<String,Level> levelMap = new HashMap<String, Level>();

	public AbstractNum(String id, String multiLevelType) {
		this.id = id;
		this.multiLevelType = multiLevelType;
	}
	public String getId() {
		return id;
	}
	public String getMultiLevelType() {
		return multiLevelType;
	}
	public Map<String, Level> getLevelMap() {
		return levelMap;
	}
	public AbstractNum clone(){
		AbstractNum newAbstractNum = new AbstractNum(id, multiLevelType);
		for(Map.Entry<String, Level> entry:levelMap.entrySet()){
			newAbstractNum.getLevelMap().put(entry.getKey(), entry.getValue().clone());
		}
		return newAbstractNum;
	}
}
