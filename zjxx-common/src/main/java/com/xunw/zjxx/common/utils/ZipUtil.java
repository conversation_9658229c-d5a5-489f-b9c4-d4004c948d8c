package com.xunw.zjxx.common.utils;

import com.xunw.zjxx.common.exception.BizException;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.FileHeader;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.CompressionMethod;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

public class ZipUtil {
    /**
     * 使用给定密码压缩指定文件或文件夹到指定的zip文件中
     * @param src         要压缩的文件或文件夹路径
     * @param destFile    压缩文件路径
     * @param passwd      压缩使用的密码，可以为空字符串或者null。如果为空字符串或者null，表示不加密压缩
     * @param fileNameCharset 文件名的字符集
     */
    public static void compress(String src, String destFile, String pwd, String fileNameCharset){
    	try {
            File srcFile = new File(src);
            ZipParameters parameters = new ZipParameters();
            // 压缩方式
            parameters.setCompressionMethod(CompressionMethod.DEFLATE);
            // 压缩级别
            parameters.setCompressionLevel(CompressionLevel.NORMAL);
            ZipFile zipFile = null;
            if(pwd != null && !StringUtils.isEmpty(pwd)) {
                parameters.setEncryptFiles(true);
                // 加密方式
                parameters.setEncryptionMethod(EncryptionMethod.ZIP_STANDARD);
        		zipFile = new ZipFile(destFile, pwd.toCharArray());
            }else{
        		zipFile = new ZipFile(destFile);
        	}
            zipFile.setCharset(Charset.forName(fileNameCharset));
            if (srcFile.isDirectory()) {
                for(File child:srcFile.listFiles()) {
                	if(child.isFile()) {
                        zipFile.addFile(child, parameters);
                	}else {
                        zipFile.addFolder(child, parameters);
                	}
                }
            } else {
                zipFile.addFile(srcFile, parameters);
            }
		} catch (Exception e) {
			throw new BizException(e.getMessage());
		}
    }
    /**
     * 使用给定密码压缩指定文件或文件夹到指定的zip文件中。文件名的字符集默认为UTF-8
     * @param src         要压缩的文件或文件夹路径
     * @param destFile    压缩文件路径
     * @param passwd      压缩使用的密码，可以为空字符串或者null。如果为空字符串或者null，表示不加密压缩
     */
    public static void compress(String src, String destFile, String pwd){
    	compress(src, destFile, pwd, "UTF-8");
    }

    /**
     * 解压zip文件
     * @param source 原始文件路径
     * @param dest 解压路径
     * @param pwd 解压文件密码(可以为空)
     * @param fileNameCharset 文件名的字符集
     */
    public static void uncompress(String source, String dest, String pwd, String fileNameCharset){
    	try {
            File zipFile = new File(source);
            ZipFile zFile = new ZipFile(zipFile);
            zFile.setCharset(Charset.forName(fileNameCharset));
            File destDir = new File(dest);
            if (!destDir.exists()) {
                destDir.mkdirs();
            }
            if (zFile.isEncrypted()) {
                // 设置密码
                zFile.setPassword(pwd.toCharArray());
            }
            // 将文件抽出到解压目录(解压)
            zFile.extractAll(dest);
            List<FileHeader> headerList = zFile.getFileHeaders();
            List<File> extractedFileList = new ArrayList<File>();
            for (FileHeader fileHeader : headerList) {
                if (!fileHeader.isDirectory()) {
                    extractedFileList.add(new File(destDir, fileHeader.getFileName()));
                }
            }
//            File[] extractedFiles = new File[extractedFileList.size()];
//            extractedFileList.toArray(extractedFiles);
//            for (File f : extractedFileList) {
//                System.out.println(f.getAbsolutePath() + "文件解压成功!");
//            }
		} catch (Exception e) {
            throw new BizException(e.getMessage());
		}
    }

    /**
     * 解压zip文件。文件名的字符集默认为UTF-8
     * @param source 原始文件路径
     * @param dest 解压路径
     * @param pwd 解压文件密码(可以为空)
     */
    public static void uncompress(String source, String dest, String pwd){
    	uncompress(source, dest, pwd, "GBK");
    }

    /**
     *test
     */
    public static void main(String[] args) throws Exception {
        File tmpFolder = new File("C:/Users/<USER>/Desktop/20210127");
		File zipFile = new File(tmpFolder,"20210222.zip");
		if(zipFile.exists()) {
			zipFile.delete();
		}
		try {
			compress("C:/Users/<USER>/Desktop/20210127/file", zipFile.getPath(), null);
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			uncompress("C:/Users/<USER>/Desktop/file.zip", "C:/Users/<USER>/Desktop/20210127/123",null);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
