package com.xunw.zjxx.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "att", ignoreUnknownFields = false)
public class AttConfig {

    //文件存储临时目录，临时目录中的文件可以删除
    private String tempdir;

    //文件归档存储路径(本地存储时启用,非本地存储无效)
    private String storeDir;

    //文件存储方式   本地存储  七牛云存储
    private String storeType;
    
    //文件存储在云端的相对路径的根路径，本地存储无效
    private String rootDir;

    //文件访问 路径前缀
    private String urlPrefix;

	public String getTempdir() {
		return tempdir;
	}

	public void setTempdir(String tempdir) {
		this.tempdir = tempdir;
	}

	public String getStoreDir() {
		return storeDir;
	}

	public void setStoreDir(String storeDir) {
		this.storeDir = storeDir;
	}

	public String getStoreType() {
		return storeType;
	}

	public void setStoreType(String storeType) {
		this.storeType = storeType;
	}
	
	public String getRootDir() {
		return rootDir;
	}

	public void setRootDir(String rootDir) {
		this.rootDir = rootDir;
	}

	public String getUrlPrefix() {
		return urlPrefix;
	}

	public void setUrlPrefix(String urlPrefix) {
		this.urlPrefix = urlPrefix;
	}

    
}