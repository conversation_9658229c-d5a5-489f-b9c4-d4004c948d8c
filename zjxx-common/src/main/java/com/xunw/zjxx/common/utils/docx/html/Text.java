package com.xunw.zjxx.common.utils.docx.html;



import com.xunw.zjxx.common.utils.BaseUtil;

import java.util.ArrayList;
import java.util.List;

public class Text extends HtmlElement{

	protected List<HtmlElement> styles = new ArrayList<HtmlElement>();
	protected StringBuffer text;

	public Text(StringBuffer text,List<HtmlElement> styles) {
		this.text = text;
		this.styles = styles;
		this.priority = -1;
	}

	public List<HtmlElement> getStyles() {
		return styles;
	}

	@Override
	public String head() {
		return BaseUtil.HTMLEncode(text.toString(),true);
	}

	@Override
	public String tail() {
		return "";
	}

	@Override
	public String getStyleStr() {
		return "";
	}
}
