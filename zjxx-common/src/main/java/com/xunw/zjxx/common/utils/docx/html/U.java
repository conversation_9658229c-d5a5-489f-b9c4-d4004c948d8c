package com.xunw.zjxx.common.utils.docx.html;


/**
 * 下划线
 * <AUTHOR>
 *
 */
public class U extends HtmlElement{
	private boolean none = false;//是否不允许下划线

//	public U() {
//		none = false;
//	}

	public U(boolean none) {
		this.none = none;
	}

	@Override
	public String head() {
		return none?"<span style=\"text-decoration:none;\">":"<u>";
	}

	@Override
	public String tail() {
		return none?"</span>":"</u>";
	}

	@Override
	public String getStyleStr() {
		return "text-decoration:" + (none?"none":"underline") + ";";
	}
}
