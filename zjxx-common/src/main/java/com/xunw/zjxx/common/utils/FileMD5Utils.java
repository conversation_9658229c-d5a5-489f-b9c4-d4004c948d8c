package com.xunw.zjxx.common.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/*
 * 计算文件的md5
 */
public class FileMD5Utils {
	
	public static String encrypt(InputStream inputStream) throws IOException {
		try {
			return	DigestUtils.md5Hex(inputStream);
		}
		finally {
			if(inputStream!=null){
				IOUtils.closeQuietly(inputStream);
			}
		}
	}

	public static String encrypt(File file) throws IOException {
		FileInputStream inputStream = null;
		try {
			inputStream = new FileInputStream(file);
			return	DigestUtils.md5Hex(inputStream);
		}finally {
			if(inputStream!=null){
				IOUtils.closeQuietly(inputStream);
			}
		}
	}

//	public static void main(String[] args) {
//		String aString = encrypt(new File("E:\\JK_SERVER_DIR\\storage\\NORMAL\\DXVK\\kw\\kc.csv"));
//		System.out.println(aString.length() + ":" + aString);
//	}

}
