package com.xunw.zjxx.common.core.annotation;

import com.xunw.zjxx.common.enums.LogType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Operation {
     String privilegeCode() default "";//权限代码 	如果为空则表示该接口无须校验权限
     String desc();//操作描述 必填项
     LogType logType() default LogType.NORMAL; //日志类别
     boolean loginRequired() default true;//调用该接口是否需要登录 默认 true
}
