--单科考试成绩视图
create
or REPLACE view v_student_exam_score as
select p.`course_id`, d.`student_id`,d.`paper_id`,p.`total_score`, d.`score`
from `biz_exam_paper` p
inner join `biz_exam_data` d on p.id = d.`paper_id`
where p.`category` = 'EXAMINE' and d.is_submit = 1 and d.score is not null;

--单科考试最高成绩视图
create
or REPLACE view v_student_exam_max_score as
select s.student_id,s.course_id,s.score,min(s.total_score) as total_score from (
select esa.student_id,esa.course_id,esa.score,esa.total_score from v_student_exam_score esa,(select esb.`student_id`,esb.`course_id`,max(esb.`score`) as score from v_student_exam_score esb GROUP BY esb.`student_id`,esb.`course_id`) t
where esa.`student_id` = t.student_id and esa.`course_id` = t.`course_id` and esa.`score` = t.score ) s 
group by s.student_id,s.course_id,s.score;


----单科成绩视图
create
or REPLACE view v_student_course_score as
select 
   sbc.`student_id`,
   sbc.`course_id`,
   c.name course_name,
   sbc.create_time bm_time,
   c.`type_id`,
   c.`top_type_id`,
   y.`category`,
   s.`progress`,
   t.`score`,
   t.total_score,
   IF(t.`score` >= t.total_score*0.6, c.`hours`, 0) finish_hours,
   IF(t.`score` >= t.total_score*0.6, 1, 0) status,
   IF(t.`score` >= t.total_score*0.6, '合格', '不合格') status_name,
   c.hours total_hours
from `biz_student_bm_course` sbc
INNER join `inf_course` c on c.id = sbc.`course_id`
left join `inf_type` y on y.id = c.`type_id`
left join `v_student_exam_max_score` t on t.course_id = sbc.`course_id` and t.student_id = sbc.`student_id`
left join `biz_courseware_progress` s on s.`course_id` = sbc.`course_id` and s.`student_id` = sbc.`student_id`
where sbc.`is_payed` = 1;

--学员项目成绩视图
create or REPLACE view v_student_xm_score as
select
    xm.id as xm_id,
    scs.`student_id`,
    sum(case when scs.`category` = 'GXK' then IFNULL(scs.`finish_hours`,0) else 0 end) as gxk_finish_hours,
    sum(case when scs.`category` = 'ZYK' then IFNULL(scs.`finish_hours`,0) else 0 end) as zyk_finish_hours,
    sum(scs.`finish_hours`) total_finish_hours,
    xm.`gxk_hours`,
    xm.`zyk_hours`,
    IFNULL(xm.`gxk_hours`, 0) + IFNULL(xm.`zyk_hours`, 0) as total_need_hours
from 
	`v_student_course_score` scs
inner join `biz_xm_course_scope` xcs on xcs.`type_id` = scs.`top_type_id`
inner join biz_xm xm on xm.id = xcs.`xm_id`
group by xm.id,scs.`student_id`;