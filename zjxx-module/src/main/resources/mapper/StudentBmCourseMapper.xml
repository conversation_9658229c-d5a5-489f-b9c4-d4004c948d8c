<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xunw.zjxx.module.biz.mapper.StudentBmCourseMapper">

    <select id="progress" resultType="java.util.Map">
        select
            c.id course_id,
            c.name course_name,
            si.name student_name,
            si.sfzh,
            si.id as student_id,
            ifnull(cp.progress, 0) progress,
            bc.bm_count count
        from
            biz_student_bm_course sbc
        inner join inf_course c on c.id = sbc.course_id
        inner join sys_student_info si on si.id = sbc.student_id
        left join (select bc.student_id,count(bc.id) bm_count from biz_student_bm_course bc group by bc.student_id) bc on bc.student_id = si.id
        left join biz_courseware_progress cp on cp.course_id = c.id and cp.student_id = sbc.student_id
        <where>
                sbc.is_payed='1'
            <if test="courseId != null and courseId != ''">
                and c.id = #{courseId}
            </if>
            <if test="hostOrgId != null and hostOrgId != ''">
                and c.host_org_id = #{hostOrgId}
            </if>
        </where>
        group by c.id,c.name,si.name,si.sfzh,si.id,cp.progress,bc.bm_count
    </select>

    <select id="study" resultType="java.util.Map">
        select
            distinct
            b.id,
            b.name,
            c.last_modify_time,
            a.bm_time,
            a.progress,
            b.hours,
            a.finish_hours,
            ifnull(e.progress, 0) practice_progress
        from
            v_student_course_score a
        inner join inf_type y on y.id = a.top_type_id
        inner join biz_xm_course_scope s on s.type_id = a.type_id
        inner join biz_xm xm on xm.id = s.xm_id
        inner join inf_course b on a.course_id = b.id
        left join biz_courseware_progress c on c.student_id=a.student_id and c.course_id=a.course_id
        left join (select count(ed.id)/count(ep.id)*100 progress, ep.course_id, ed.student_id from biz_exam_paper ep
                    left join biz_exam_data ed on ed.paper_id = ep.id and ed.student_id = #{studentId}
                    where ep.category='HOMEWORK' group by ep.course_id, ed.student_id) e on e.course_id = b.id and e.student_id = #{studentId}
        <where>
            <if test="studentId != null and studentId != ''">
                a.student_id = #{studentId}
            </if>
            <if test="xmId != null and xmId != ''">
                and xm.id = #{xmId}
            </if>
        </where>
    </select>

    <select id="getStudentBmCourseList" resultType="java.util.Map">
        select
            a.id bm_id,
            b.name course_name,
            s.name student_name,
            s.sfzh,
            s.mobile,
            if(a.is_payed=1,'已缴费','未缴费') is_payed,
            a.create_time bm_time
        from biz_student_bm_course a
        inner join inf_course b on a.course_id=b.id
        inner join sys_student_info s on s.id=a.student_id
        <where>
            <if test="keyword != null and keyword != ''">
                and s.name like concat('%',#{keyword},'%') or s.sfzh like concat('%',#{keyword},'%')
            </if>
            <if test="courseName != null and courseName != ''">
                and b.name like concat('%',#{courseName},'%')
            </if>
            <if test="courseId != null and courseId != ''">
                and b.id = #{courseId}
            </if>
            <if test="isPayed != null and isPayed != ''">
                and a.is_payed = #{isPayed}
            </if>
            <if test="hostOrgId != null and hostOrgId != ''">
                and b.host_org_id = #{hostOrgId}
            </if>
        </where>
    </select>

    <!-- 课程学习统计数据 田军-->
    <select id="statisticCourseStudy" resultType="java.util.Map">
		select
			total_cout,t.finish_count,(t.total_cout-t.finish_count) un_finish_count,t.total_hours,t.total_finish_hours,t.exam_count
		from(
			select
			(SELECT count(1) from v_student_course_score  scs where scs.student_id = #{studentId} and
				exists(select 1 from biz_xm_course_scope xcs where xcs.type_id = scs.type_id and xcs.xm_id=#{xmId})) as total_cout,
			(SELECT count(2) from v_student_course_score  scs where scs.student_id = #{studentId} and scs.`score` >= 60 and
				exists(select 1 from biz_xm_course_scope xcs where xcs.type_id = scs.type_id and xcs.xm_id=#{xmId})) as finish_count,
			(SELECT sum(scs.`total_hours`) from v_student_course_score  scs where scs.student_id = #{studentId} and
				exists(select 1 from biz_xm_course_scope xcs where xcs.type_id = scs.type_id and xcs.xm_id=#{xmId})) as total_hours,
			(SELECT sum(scs.`finish_hours`) from v_student_course_score scs where scs.student_id = #{studentId} and
				exists(select 1 from biz_xm_course_scope xcs where xcs.type_id = scs.type_id and xcs.xm_id=#{xmId})) as total_finish_hours,
            (SELECT count(1) from biz_exam_data ed inner join biz_exam_paper ep on ep.id = ed.paper_id and ep.category = 'EXAMINE'
                where ed.student_id = #{studentId}) as exam_count
		) t
    </select>

    <!-- 公需课课程学习 田军-->
    <select id="statisticGXKCourseStudy" resultType="java.util.Map">
		select
		    distinct
            scs.*
		from
			v_student_course_score  scs
		left join biz_xm_course_scope xcs on xcs.type_id = scs.type_id
		where
			scs.student_id=#{studentId} and exists(select 1 from biz_xm_course_scope xcs where xcs.type_id = scs.type_id and xcs.xm_id=#{xmId})
			and scs.category = 'GXK'
    </select>

      <!-- 专业课课课程学习 田军-->
    <select id="statisticZYKCourseStudy" resultType="java.util.Map">
		select
		    distinct
            scs.*
		from
			v_student_course_score  scs
		left join biz_xm_course_scope xcs on xcs.type_id = scs.type_id
		where
			scs.student_id=#{studentId}
        and exists(select 1 from biz_xm_course_scope xcs where xcs.type_id = scs.type_id and xcs.xm_id=#{xmId})
        and scs.category = 'ZYK'
    </select>


</mapper>
