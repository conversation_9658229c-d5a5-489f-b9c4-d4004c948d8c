package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.common.enums.LogType;
import com.xunw.zjxx.common.enums.UserTypeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统日志表
 */
@TableName("sys_log")
public class SystemLog implements Serializable {

    private static final long serialVersionUID = -2700133747288407047L;

    //主键ID
    @TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

    //用户名
    @TableField("username")
    private String username;

    //用户类型
    @TableField("user_type")
    private UserTypeEnum userType;

    //日志类别
    @TableField("log_type")
    private LogType logType;

    //行为   操作名称
    @TableField("operation")
    private String operation;

    //访问地址
    @TableField("visit_url")
    private String visitUrl;

    //创建时间
    @TableField("create_time")
    private Date createTime;
    
    //主办单位ID
    @TableField("host_org_id")
    private String hostOrgId;

    //IP地址
    @TableField("ip")
    private String ip;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public UserTypeEnum getUserType() {
        return userType;
    }

    public void setUserType(UserTypeEnum userType) {
        this.userType = userType;
    }

    public LogType getLogType() {
        return logType;
    }

    public void setLogType(LogType logType) {
        this.logType = logType;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getVisitUrl() {
        return visitUrl;
    }

    public void setVisitUrl(String visitUrl) {
        this.visitUrl = visitUrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}
}
