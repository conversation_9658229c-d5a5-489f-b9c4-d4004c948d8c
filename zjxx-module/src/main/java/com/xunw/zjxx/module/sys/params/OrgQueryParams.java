package com.xunw.zjxx.module.sys.params;

import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.enums.OrgType;

public class OrgQueryParams extends BaseQueryParams {
    /**
     * 机构类型
     */
    private OrgType orgType;

    /**
     * 父节点id
     */
    private String parentId;

    /**
     * 是否有父机构
     */
    private String isParent;

    //当parentId不为空时是否做递归查询，默认不做递归查询
    private String isDeep = Constants.NO;

    /**
     * 主办单位id
     */
    private String hostOrgId;
    
    private String keyword;
    

    public OrgType getOrgType() {
        return orgType;
    }

    public void setOrgType(OrgType orgType) {
        this.orgType = orgType;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getIsParent() {
        return isParent;
    }

    public void setIsParent(String isParent) {
        this.isParent = isParent;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

    public String getIsDeep() {
        return isDeep;
    }

    public void setIsDeep(String isDeep) {
        this.isDeep = isDeep;
    }
}
