package com.xunw.zjxx.module.sys.mapper;


import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.sys.params.StudentUserQueryParams;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.xunw.zjxx.module.sys.entity.StudentInfo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【sys_student_info】的数据库操作Mapper
* @createDate 2023-08-08 13:32:10
*/
public interface StudentInfoMapper extends BaseMapper<StudentInfo> {

    public StudentInfo getByStudentId(@Param("studentId") String studentId);

    List<Map<String, Object>> pageQuery(Map<String, Object> condition, Page<?> page);

    void updateStudentOrgName(@Param("id") String id, @Param("name") String name);
}




