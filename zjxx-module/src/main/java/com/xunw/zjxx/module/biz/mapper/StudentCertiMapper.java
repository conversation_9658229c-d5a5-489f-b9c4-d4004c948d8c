package com.xunw.zjxx.module.biz.mapper;


import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.biz.entity.StudentCerti;
import com.xunw.zjxx.module.dto.CertiPassDto;
import com.xunw.zjxx.module.dto.StatisticDto;
import com.xunw.zjxx.module.inf.vo.PracticeVo;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【biz_student_certi】的数据库操作Mapper
* @createDate 2023-08-08 13:32:09
*/
public interface StudentCertiMapper extends BaseMapper<StudentCerti> {

    List<CertiPassDto> selectPassList(Map<String, Object> condition, Page<?> page);

    /**
     * 合格证书分页列表
     */
    List<Map<String, Object>> list(Map<String, Object> param, Page page);

    List<StatisticDto> statistic(@Param("years") String year, @Param("hostOrgId") String hostOrgId);

    List<Map<String, Object>> pageQuery(Map<String, Object> condition, Page<?> page);

    List<StudentCerti> getListByXmId(@Param("xmId") String xmId);

    List<PracticeVo> selectByNameAndSfzh(@Param("name") String name, @Param("sfzh") String sfzh);
}




