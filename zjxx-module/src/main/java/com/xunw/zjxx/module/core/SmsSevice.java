package com.xunw.zjxx.module.core;

import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.xunw.zjxx.common.config.SmsConfig;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.comm.entity.CommSmsLog;
import com.xunw.zjxx.module.comm.service.CommSmsLogService;
import net.sf.json.JSONObject;

/**
 * 短信发送服务
 * <AUTHOR>
 */
@Service
public class SmsSevice {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(SmsSevice.class);
	
	@Autowired
	private SmsConfig smsConfig;
	@Autowired
	private CommSmsLogService smsLogService;
	//短信模板配置
	private static Map<String, String> SMS_TEPMLATE_MAP = new ConcurrentHashMap<String, String>();
	static {
		SMS_TEPMLATE_MAP.put("212892", "您的验证码：{0},您正在进行身份验证，打死不告诉别人，90秒内有效。");
	}
	
	/**
	 * 根据短信模板发送
	 */
	@Transactional
	public void sendByTemplate(String sign, String templateId, String mobile,String[] params) {
		if(StringUtils.isEmpty(sign)) {
			throw BizException.withMessage("短信签名不能够为空");
		}
		if(StringUtils.isEmpty(templateId)) {
			throw BizException.withMessage("短信模板不能够为空");
		}
		if(StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("手机号不能够为空");
		}
		int statusCode = 0;
		String content = null;
		boolean success = false;
		try {
			HttpClient httpClient = new HttpClient();
			PostMethod postMethod = new PostMethod("http://api.1cloudsp.com/api/v2/single_send");
			postMethod.getParams().setContentCharset("UTF-8");
			postMethod.getParams().setParameter(HttpMethodParams.RETRY_HANDLER,new DefaultHttpMethodRetryHandler());
			content = params != null && params.length > 0 ? StringUtils.join(params,"##") : "";
			NameValuePair[] data = {
					new NameValuePair("accesskey", smsConfig.getKey()),
					new NameValuePair("secret", smsConfig.getSecret()),
					new NameValuePair("sign", sign),
					new NameValuePair("templateId", templateId),
					new NameValuePair("mobile", mobile),
					new NameValuePair("content", URLEncoder.encode(content, "utf-8"))
			};
			postMethod.setRequestBody(data);
			postMethod.setRequestHeader("Connection", "close");
			statusCode = httpClient.executeMethod(postMethod);
			if (statusCode == 200) {
				String resp = postMethod.getResponseBodyAsString();
				LOGGER.info("短信响应内容:"+resp);
				JSONObject jsonObject = JSONObject.fromObject(resp);
				success = "0".equals(BaseUtil.getStringValueFromJson(jsonObject, "code", "-1"));
			}
		} catch (Exception e) {
			LOGGER.error("短信发送失败,因为:",e);
			statusCode = -999;
		}
		try {
			CommSmsLog log = new CommSmsLog();
			log.setId(BaseUtil.generateId2());
			log.setContent(getContentByTemplateCode(templateId, params));
			log.setMobile(mobile);
			log.setTplCode(templateId);
			log.setTplSign(sign);
			log.setStatus(statusCode+"");
			log.setResp(success == true ? "发送成功" : "发送失败");
			log.setCreateTime(new Date());
			smsLogService.insert(log);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 根据短信模板发送，批量操作
	 */
	public void sendBatchByTemplate(String sign, String templateId, String[] mobiles,String[] params) {
		if(StringUtils.isEmpty(sign)) {
			throw BizException.withMessage("短信签名不能够为空");
		}
		if(StringUtils.isEmpty(templateId)) {
			throw BizException.withMessage("短信模板不能够为空");
		}
		if(mobiles == null || mobiles.length == 0) {
			throw BizException.withMessage("手机号不能够为空");
		}
		this.send(sign, templateId, mobiles, params);
	}
	
	/**
	 * 自定义发送短信内容,发送后会进入人工审核
	 */
	public void sendByContent(String sign, String mobile,String content) {
		if(StringUtils.isEmpty(sign)) {
			throw BizException.withMessage("短信签名不能够为空");
		}
		if(StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("手机号不能够为空");
		}
		if(StringUtils.isEmpty(content)) {
			throw BizException.withMessage("自定义短信内容不能够为空");
		}
		this.send(sign, null,  new String[] {mobile}, new String[] {content});
	}
	
	/**
	 * 自定义发送短信内容,发送后会进入人工审核,批量操作
	 */
	public void sendBatchByContent(String sign, String[] mobiles,String content) {
		if(StringUtils.isEmpty(sign)) {
			throw BizException.withMessage("短信签名不能够为空");
		}
		if(mobiles == null || mobiles.length == 0) {
			throw BizException.withMessage("手机号不能够为空");
		}
		if(StringUtils.isEmpty(content)) {
			throw BizException.withMessage("自定义短信内容不能够为空");
		}
		this.send(sign, null, mobiles, new String[] {content});
	}
	
	/**
	 * 根据短信模板发送,支持自定义短信内容，支持群发
	 * 如果是自定义短信内容，params请直接传入自定义短信内容
	 */
	@Transactional
	public void send(String sign, String templateId,String[] mobiles, String[] params) {
		int statusCode = 0;
		String content = null;
		boolean success = false;
		String resp = null;
		try {
			HttpClient httpClient = new HttpClient();
			PostMethod postMethod = new PostMethod("http://api.1cloudsp.com/api/v2/send");
			postMethod.getParams().setContentCharset("UTF-8");
			postMethod.getParams().setParameter(HttpMethodParams.RETRY_HANDLER,new DefaultHttpMethodRetryHandler());
			content = params != null && params.length > 0 ? StringUtils.join(params,"##") : "";
			NameValuePair[] data = {
					new NameValuePair("accesskey", smsConfig.getKey()),
					new NameValuePair("secret", smsConfig.getSecret()),
					new NameValuePair("sign", sign),
					new NameValuePair("templateId", StringUtils.isNotEmpty(templateId) ? templateId : ""),
					new NameValuePair("mobile", StringUtils.join(mobiles,",")),
					new NameValuePair("content", URLEncoder.encode(content, "utf-8"))
			};
			postMethod.setRequestBody(data);
			postMethod.setRequestHeader("Connection", "close");
			statusCode = httpClient.executeMethod(postMethod);
			if (statusCode == 200) {
				resp = postMethod.getResponseBodyAsString();
				LOGGER.info("短信响应内容:"+resp);
				JSONObject jsonObject = JSONObject.fromObject(resp);
				success = "0".equals(BaseUtil.getStringValueFromJson(jsonObject, "code", "-1"));
			}
		} catch (Exception e) {
			LOGGER.error("短信发送失败,因为:",e);
			statusCode = -999;
		}
		try {
			CommSmsLog log = new CommSmsLog();
			content = StringUtils.isNotEmpty(templateId) ? getContentByTemplateCode(templateId, params) : params[0];
			log.setContent(content);
			log.setTplCode(templateId);
			log.setTplSign(sign);
			log.setStatus(statusCode+"");
			log.setResp((success == true ? "发送成功" : "发送失败")+"【"+(StringUtils.isEmpty(resp) ? "接口无返回" : resp)+"】");
			Arrays.asList(mobiles).forEach(x -> {
				log.setId(BaseUtil.generateId2());
				log.setMobile(x);
				log.setCreateTime(new Date());
				smsLogService.insert(log);
			});
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private String getContentByTemplateCode(String code, String[] params) {
		String tplContent = SMS_TEPMLATE_MAP.get(code);
		if (tplContent != null) {
			return MessageFormat.format(tplContent, params);
		}
		else {
			return null;
		}
	}

}
