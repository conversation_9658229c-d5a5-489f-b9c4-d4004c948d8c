package com.xunw.zjxx.module.biz.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.xunw.zjxx.module.biz.entity.XmCourseScope;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【biz_xm_course_scope(项目课程范围表)】的数据库操作Mapper
* @createDate 2023-08-16 10:10:16
* @Entity com.xunw.zjxx.module.entity.XmCourseScope
*/
public interface XmCourseScopeMapper extends BaseMapper<XmCourseScope> {

    List<Map<String, Object>> getCourseSetting(String id);

    String getXmIdbyCourseId(@Param("courseId") String courseId);
}




