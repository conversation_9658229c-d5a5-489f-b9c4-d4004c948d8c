package com.xunw.zjxx.module.biz.params;


import com.xunw.zjxx.common.base.BaseQueryParams;

public class QuestionQueryParams extends BaseQueryParams {

   
	private static final long serialVersionUID = -8599184937531886753L;
	
	private String dbId;
	private String difficulty;
	private String status;
	private String type;
	private String courseId;
	private String courseName;
	private String realType;
	private String content;
	private String orgId;


	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getDbId() {
		return dbId;
	}
	public void setDbId(String dbId) {
		this.dbId = dbId;
	}
	public String getDifficulty() {
		return difficulty;
	}
	public void setDifficulty(String difficulty) {
		this.difficulty = difficulty;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getCourseId() {
		return courseId;
	}
	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}
	public String getRealType() {
		return realType;
	}
	public void setRealType(String realType) {
		this.realType = realType;
	}
	public String getCourseName() {
		return courseName;
	}
	public void setCourseName(String courseName) {
		this.courseName = courseName;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}
}
