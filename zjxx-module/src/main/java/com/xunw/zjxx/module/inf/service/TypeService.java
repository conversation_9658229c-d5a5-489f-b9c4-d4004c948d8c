package com.xunw.zjxx.module.inf.service;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.inf.entity.Course;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.IStabilityClassifier;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.module.dto.TypeTree;
import com.xunw.zjxx.module.enums.Category;
import com.xunw.zjxx.module.inf.entity.Type;
import com.xunw.zjxx.module.inf.mapper.TypeMapper;
import com.xunw.zjxx.module.inf.params.CourseQueryParams;
import com.xunw.zjxx.module.inf.params.TypeQueryParams;

/**
 * <AUTHOR>
 * @description 针对表【inf_type】的数据库操作Service
 * @createDate 2023-08-08 13:32:09
 */
@Service
public class TypeService extends BaseCRUDService<TypeMapper, Type> {

    public List<Type> pageQuery(TypeQueryParams params) {
        EntityWrapper<Type> wrapper = new EntityWrapper();
        if (StringUtils.isNotEmpty(params.getHostOrgId())) {
            wrapper.eq("host_org_id", params.getHostOrgId());
        }
        if (StringUtils.isNotEmpty(params.getParentId())) {
            wrapper.eq("parent_id",  params.getParentId());
        }else {
            wrapper.isNull("parent_id");
        }
        if (StringUtils.isNotEmpty(params.getName())) {
            wrapper.eq("name", params.getName());
        }
        if (StringUtils.isNotEmpty(params.getCategory())) {
            wrapper.eq("category", params.getCategory());
        }
        wrapper.orderBy("sort", false);
        return mapper.selectList(wrapper);
    }

    public Page pageQueryList(TypeQueryParams params) {
        params.setRecords(mapper.pageQuery(params.getCondition(), params));
        return params;
    }


    public Object pageQuery(CourseQueryParams params) {
        EntityWrapper<Type> wrapper = new EntityWrapper();
        wrapper.orderBy("sort", false);
        return mapper.selectList(wrapper);
    }

    public Object tree(String hostOrgId) {
    	List<TypeTree> result = new ArrayList<>();
    	//先查询一级分类
    	EntityWrapper<Type> wrapper =  new EntityWrapper<Type>() ;
    	wrapper.eq("host_org_id", hostOrgId);
    	wrapper.isNull("parent_id");
    	List<Type> rootTypes = mapper.selectList(wrapper);
    	for (Type typeTree : rootTypes) {
    		 List<Type> types = mapper.selectList(new EntityWrapper<Type>().eq("host_org_id", hostOrgId));
    		 List<TypeTree> typeTrees = types.stream().map(x -> {
	            TypeTree tr = new TypeTree();
	            tr.setId(x.getId());
	            tr.setParentId(x.getParentId());
	            tr.setCategory(x.getCategory());
	            tr.setName(x.getName());
	            tr.setChildren(new ArrayList<>());
	            return tr;
	        }).collect(Collectors.toList());
    		 TypeTree rooTypeTree = this.buildTree(typeTree.getId(), typeTrees);
    		 result.add(rooTypeTree);
    	}
    	return result;
    }

    private TypeTree buildTree(String id, List<TypeTree> list) {
        Optional<TypeTree> parentOptional = list.stream().filter(x -> x.getId().equals(id)).findFirst();
        TypeTree result = new TypeTree();
        if (parentOptional.isPresent()) {
            result = parentOptional.get();
        }
        //递归查询子类
        for (TypeTree typeTree : list) {
            //如果父类主键等于传过来实体类的ID
            if (Objects.equals(typeTree.getParentId(), org.apache.commons.lang3.StringUtils.isEmpty(id) ? "" : id)) {
                // 递归调用
                result.getChildren().add(buildTree(typeTree.getId(), list));
            }
        }
        return result;
    }

    /**
     * 获取当前类型下的所有子类（包括自身）
     */
    public List<Type> getAllTypes(String typeId, String hostOrgId) {
        List<Type> types = mapper.selectList(new EntityWrapper<Type>()
                .eq("host_org_id", hostOrgId));
        List<Type> result = new ArrayList<>();
        result.add(mapper.selectById(typeId));
        this.getAllChildrenTypes(typeId, types, result);
        return result;
    }

    /**
     * 递归查询所有子类 田军 已检查
     */
    private void getAllChildrenTypes(String typeId, List<Type> types, List<Type> result) {
        List<Type> childrenTypeList = types.stream().filter(x -> Objects.equals(x.getParentId(), typeId)).collect(Collectors.toList());
        result.addAll(childrenTypeList);
        for (Type type : childrenTypeList) {
            this.getAllChildrenTypes(type.getId(), types, result);
        }
    }
    
    /**
     * 查询直接子类型
     */
    public List<Type> getChildrenType(String typeId){
        List<Type> types = mapper.selectList(new EntityWrapper<Type>().eq("parent_id", typeId).orderBy("sort",false));
        return types;
    }

    /**
     * 获取公需课的根节点
     */
    public Type getGxkTopType(String hostOrgId) {
        List<Type> types = mapper.selectList(new EntityWrapper<Type>()
        		.eq("host_org_id", hostOrgId).eq("category", Category.GXK).isNull("parent_id"));
        return types.get(0);
    }
    
    /**
     * 获取专业课的根节点
     */
    public Type getZykTopType(String hostOrgId) {
        List<Type> types = mapper.selectList(new EntityWrapper<Type>()
                .eq("host_org_id", hostOrgId).eq("category", Category.ZYK).isNull("parent_id"));
        return types.isEmpty()?null:types.get(0);
    }
    
    /**
     * 获取某一个节点的顶层节点用于作为课程表的topTypeId  田军
     */
    public Type getCourseTopType(String typeId) {
    	Type type = mapper.selectById(typeId);
    	if (StringUtils.isNotEmpty(type.getParentId())) {
            return this.getCourseTopType(type.getParentId());
		}
    	return type;
    }

    public Object courseClassify(TypeQueryParams params) {
        List<Type> types = this.pageQuery(params);
        List<Type> parentTypes = types.stream().filter(x -> StringUtils.isEmpty(x.getParentId())).collect(Collectors.toList());
        Map<String, Object> result = new HashMap<>();
        for (Type parentType : parentTypes) {
            result.put(parentType.getCategory().name(), this.getChildrenType(parentType.getId()));
        }
        return result;
    }

    public Object delete(String id) {
        List<Course> courses = mapper.selectCourseByTypeId(id);
        if(courses.size() > 0){
            throw BizException.withMessage("该课程分类下有课程，不可删除！");
        }
        mapper.deleteById(id);
        return true;
    }
}
