package com.xunw.zjxx.module.biz.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName biz_certi_tpl
 */
@TableName(value ="biz_certi_tpl")
public class CertiTpl implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.INPUT)
    private String id;

    /**
     * 证书名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 证书样例URL 图片
     */
    @TableField(value = "demo")
    private String demo;

    /**
     * 证书模板URL地址
     */
    @TableField(value = "template_path")
    private String templatePath;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建用户id
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 主办单位id
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 证书名称
     */
    public String getName() {
        return name;
    }

    /**
     * 证书名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 证书样例URL 图片
     */
    public String getDemo() {
        return demo;
    }

    /**
     * 证书样例URL 图片
     */
    public void setDemo(String demo) {
        this.demo = demo;
    }

    /**
     * 证书模板URL地址
     */
    public String getTemplatePath() {
        return templatePath;
    }

    /**
     * 证书模板URL地址
     */
    public void setTemplatePath(String templatePath) {
        this.templatePath = templatePath;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建用户id
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建用户id
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 主办单位id
     */
    public String getHostOrgId() {
        return hostOrgId;
    }

    /**
     * 主办单位id
     */
    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }
}