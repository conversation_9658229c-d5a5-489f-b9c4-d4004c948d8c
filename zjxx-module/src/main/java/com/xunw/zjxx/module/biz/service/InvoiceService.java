package com.xunw.zjxx.module.biz.service;

import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.biz.entity.BizInvoiceApply;
import com.xunw.zjxx.module.biz.mapper.InvoiceMapper;
import com.xunw.zjxx.module.biz.params.InvoiceQueryParams;
import com.xunw.zjxx.module.dto.InvoiceDetailDto;
import com.xunw.zjxx.module.dto.InvoiceDto;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class InvoiceService extends BaseCRUDService<InvoiceMapper, BizInvoiceApply> {

    public Object list(InvoiceQueryParams params) {
        List<InvoiceDto> list = mapper.list(params.getCondition(), params);
        List<InvoiceDto> newlist = new ArrayList<>();
        for (InvoiceDto invoiceDto : list) {
            List<String> courselist = mapper.courselist(invoiceDto.getId());
            invoiceDto.setCourselist(courselist);
            newlist.add(invoiceDto);
        }
        params.setRecords(newlist);
        return params;
    }

    public Object upload(String id, String file) {
        BizInvoiceApply bizInvoiceApply = mapper.selectById(id);
        if (bizInvoiceApply == null) {
            throw BizException.withMessage("发票申请不存在");
        }
        bizInvoiceApply.setUrl(file);
        //上传发票之后修改状态
        bizInvoiceApply.setStatus("INVOICING");
        mapper.updateById(bizInvoiceApply);
        return true;
    }

    public Object getById(String id) {
        List<InvoiceDetailDto> invoiceDetailDtos = mapper.invoiceDetail(id);
        List<String> courselist = mapper.courselist(id);
        for (InvoiceDetailDto invoiceDetailDto : invoiceDetailDtos) {
            invoiceDetailDto.setCourselist(courselist);
        }
        return invoiceDetailDtos;
    }

    public List<Map<String, Object>> getOrdersByApplyID(String id) {
        return mapper.getOrdersByApplyID(id);
    }
}
