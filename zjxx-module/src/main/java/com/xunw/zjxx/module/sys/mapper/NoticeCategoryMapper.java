package com.xunw.zjxx.module.sys.mapper;


import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.sys.entity.NoticeCategory;

public interface NoticeCategoryMapper extends BaseMapper<NoticeCategory> {

    List<Map<String, Object>> pageQuery(Map<String, Object> condition, Page page);
}




