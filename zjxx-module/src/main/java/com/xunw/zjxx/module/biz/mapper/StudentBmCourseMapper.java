package com.xunw.zjxx.module.biz.mapper;


import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.biz.entity.StudentBmCourse;

/**
* <AUTHOR>
* @description 针对表【biz_student_bm_course】的数据库操作Mapper
* @createDate 2023-08-08 13:32:09
*/
public interface StudentBmCourseMapper extends BaseMapper<StudentBmCourse> {

    List<Map<String, Object>> progress(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> study(@Param("studentId")String studentId, @Param("xmId")String xmId);

    List<Map<String, Object>> getStudentBmCourseList(Map<String, Object> condition, Page<?> page);
    
    Map<String, Object> statisticCourseStudy(@Param("studentId")String studentId, @Param("xmId")String xmId);

    List<Map<String, Object>> statisticGXKCourseStudy(@Param("studentId")String studentId, @Param("xmId")String xmId);

    List<Map<String, Object>> statisticZYKCourseStudy(@Param("studentId")String studentId, @Param("xmId")String xmId);

}




