package com.xunw.zjxx.module.rpc.ExamService.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;

/***
 * 试卷/题库状态
 */
public enum Status implements IEnum {
    DRAFT("草稿","DRAFT"),
    DISABLED("禁用","DISABLED"),
    ENABLE("启用","ENABLE");

    private String name;

    private String id;

    private Status(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public static Status findById(String id) {
        for (Status status : Status.values()) {
            if (status.id.equals(id)) {
                return status;
            }
        }
        return null;
    }

    public static Status findByEnumName(String name) {
        for (Status status : Status.values()) {
            if (status.name().equals(name)) {
                return status;
            }
        }
        return null;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public Serializable getValue() {
        return this.name();
    }
}
