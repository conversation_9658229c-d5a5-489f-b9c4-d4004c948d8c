package com.xunw.zjxx.module.biz.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.PaperCategory;

/**
 * 试卷表
 * @TableName biz_exam_paper
 */
@TableName(value = "biz_exam_paper")
public class ExamPaper implements Serializable {
	
    private static final long serialVersionUID = -4816166705466070869L;

	/**
     * 试卷id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 试卷名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 课程id
     */
    @TableField(value = "course_id")
    private String courseId;

    /**
     * 试卷类型枚举  练习 考试
     */
    @TableField(value = "category")
    private PaperCategory category;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建用户id
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 总分
     */
    @TableField(value = "total_score")
    private String totalScore;

    /**
     * 试卷id
     */
    public String getId() {
        return id;
    }

    /**
     * 试卷id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 试卷名称
     */
    public String getName() {
        return name;
    }

    /**
     * 试卷名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 课程id
     */
    public String getCourseId() {
        return courseId;
    }

    /**
     * 课程id
     */
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建用户id
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建用户id
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

	public PaperCategory getCategory() {
		return category;
	}

	public void setCategory(PaperCategory category) {
		this.category = category;
	}

    public String getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(String totalScore) {
        this.totalScore = totalScore;
    }
}