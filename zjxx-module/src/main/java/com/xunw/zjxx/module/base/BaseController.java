package com.xunw.zjxx.module.base;

import javax.servlet.http.HttpServletRequest;

import com.alibaba.nacos.common.utils.StringUtils;
import com.xunw.zjxx.common.config.SystemConfig;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.CacheHelper;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.SpringBeanUtils;
import com.xunw.zjxx.module.enums.OrgType;
import com.xunw.zjxx.module.enums.RoleEnum;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.entity.Role;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.TreeSet;

/**
 *
 * <AUTHOR>
 *
 */
public class BaseController {
	
	/**
	 * 获取token
	 */
	protected String getAccessToken(HttpServletRequest request) {
		return request.getHeader(Constants.AUTH.AUTHORIZATION);
	}

	/**
	 * 获取登录用户信息
	 */
	protected LoginUser getLoginUser(HttpServletRequest request) {
		String token = this.getAccessToken(request);
   	 	LoginUser loginUser = CacheHelper.getCache(Constants.AUTH.USER_TOKEN_CACHE, token);
		return loginUser;
	}
	
	/**
	 * 获取登录用户ID
	 */
	protected String getLoginUserId(HttpServletRequest request) {
		LoginUser loginUser = this.getLoginUser(request);
		return loginUser != null ? loginUser.getId() : null;
	}

	/**
	 * 获取当前登录用户的角色
	 */
	public Role getLoginRole(HttpServletRequest request){
		LoginUser loginUser = this.getLoginUser(request);
		return loginUser.getRole();
	}

	/**
	 * 获取当前登录用户的角色
	 */
	public RoleEnum getLoginRoleEnum(HttpServletRequest request){
		LoginUser loginUser = this.getLoginUser(request);
		return loginUser.getRole() != null ? RoleEnum.valueOf(loginUser.getRole().getCode()) : null;
	}

	/**
	 * 获取当前登录用户的机构ID
	 */
	public String getLoginOrgId(HttpServletRequest request){
		LoginUser loginUser = this.getLoginUser(request);
		return loginUser.getOrg().getId();
	}

	/**
	 * 获取当前登录用户所在的机构
	 */
	public Org getLoginOrg(HttpServletRequest request){
		LoginUser loginUser = this.getLoginUser(request);
		return loginUser.getOrg();
	}

	/**
	 * 获取当前登录用户的机构类型
	 */
	public OrgType getLoginOrgType(HttpServletRequest request){
		LoginUser loginUser = this.getLoginUser(request);
		return loginUser.getOrg() != null ? loginUser.getOrg().getOrgType() : null;
	}

	/**
	 * 解析请求中的域名部分，并通过域名检索系统中配置的主办单位
	 */
	public String getCurrentHostOrgId(HttpServletRequest request)  {
		Org org = getCurrentHostOrg(request);
		return org !=null ? org.getId() : null;
	}

	/**
	 * 获取当前主办单位
	 */
	public Org getCurrentHostOrg(HttpServletRequest request)  {
		HostOrgResolveService hostOrgResolveService = SpringBeanUtils.getBean(HostOrgResolveService.class);
		return hostOrgResolveService._CURRENT_HOST_ORG(request);
	}

	/**
	 * 获取当前主办单位门-户访问地址
	 */
	public String getCurrentHostOrgPortalWebUrl(HttpServletRequest request) {
		HostOrgResolveService hostOrgResolveService = SpringBeanUtils.getBean(HostOrgResolveService.class);
		return hostOrgResolveService._CURRENT_HOST_ORG_PORTAL_WEB_URL(request);
	}

	/**
	 * 获取当前主办单位-管理端访问地址
	 */
	public String getCurrentHostOrgAdminWebUrl(HttpServletRequest request) {
		HostOrgResolveService hostOrgResolveService = SpringBeanUtils.getBean(HostOrgResolveService.class);
		return hostOrgResolveService._CURRENT_HOST_ORG_ADMIN_WEB_URL(request);
	}

	/**
	 * openApi签名校验
	 */
	public void doCheckSign(HttpServletRequest request) {
		if (BaseUtil.isEmpty(request.getParameter("sign"))) {
			throw BizException.withMessage("请传入sign值");
		}
		TreeSet<String> set = new TreeSet<>();
		for (String key : request.getParameterMap().keySet()) {
			if (!"sign".equals(key)) {
				if (BaseUtil.isNotEmpty(request.getParameter(key))) {
					set.add(key);
				}
			}
		}
		String sign = "";
		if (set.size() > 0) {
			sign = StringUtils.join(set, "&");
		}
		SystemConfig systemConfig = SpringBeanUtils.getBean(SystemConfig.class);
		sign = systemConfig.getOpenApiSecretKey() + sign + systemConfig.getOpenApiSecretKey();
		sign = DigestUtils.md5Hex(sign);
		if (!sign.equals(request.getParameter("sign"))) {
			throw BizException.withMessage("sign校验失败");
		}
	}
}
