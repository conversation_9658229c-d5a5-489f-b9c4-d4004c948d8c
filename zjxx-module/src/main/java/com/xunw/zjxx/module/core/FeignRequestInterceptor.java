package com.xunw.zjxx.module.core;

import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import com.xunw.zjxx.common.utils.Constants;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * 将当前请求中的token传给下游服务
 * <AUTHOR>
 *
 */
public class FeignRequestInterceptor implements RequestInterceptor {

	@Override
	public void apply(RequestTemplate template) {
	    RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
	    ServletRequestAttributes attr = (ServletRequestAttributes) requestAttributes;
	    HttpServletRequest request = attr.getRequest();
		template.header(Constants.AUTH.APP_ID, Constants.CURRENT_APPID);
	    String token = request.getHeader(Constants.AUTH.AUTHORIZATION);
	    if (StringUtils.isNotEmpty(token)) {
	      template.header(Constants.AUTH.AUTHORIZATION, token);
	    }
	}
}
