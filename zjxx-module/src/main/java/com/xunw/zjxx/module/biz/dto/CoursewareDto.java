package com.xunw.zjxx.module.biz.dto;


import com.xunw.zjxx.module.dto.ChapterDto;
import com.xunw.zjxx.module.enums.Status;

import java.io.Serializable;
import java.util.List;


public class CoursewareDto implements Serializable {


    private static final long serialVersionUID = -4363402303849484365L;
    private String id;//课件id
    private String courseId;//课程id
    private Status status;//课件状态
    private String source;//课件来源
    private String star;//课件星级
    private String remark;//课件介绍
    private String isTest;//是否开启随堂练习
    private String extNum;//随堂练习抽题数量
    private String teacherName;//讲师姓名
    private List<ChapterDto> chapters;//章节课时

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getStar() {
        return star;
    }

    public void setStar(String star) {
        this.star = star;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsTest() {
        return isTest;
    }

    public void setIsTest(String isTest) {
        this.isTest = isTest;
    }

    public String getExtNum() {
        return extNum;
    }

    public void setExtNum(String extNum) {
        this.extNum = extNum;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public List<ChapterDto> getChapters() {
        return chapters;
    }

    public void setChapters(List<ChapterDto> chapters) {
        this.chapters = chapters;
    }
}
