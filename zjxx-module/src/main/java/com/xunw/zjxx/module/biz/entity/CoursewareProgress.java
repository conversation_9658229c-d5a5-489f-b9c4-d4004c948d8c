package com.xunw.zjxx.module.biz.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 课件学习进度表
 * @TableName biz_courseware_progress
 */
@TableName(value ="biz_courseware_progress")
public class CoursewareProgress implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 学生id
     */
    @TableField(value = "student_id")
    private String studentId;

    /**
     * 课程id
     */
    @TableField(value = "course_id")
    private String courseId;

    /**
     * 课件ID
     */
    @TableField(value = "courseware_id")
    private String coursewareId;

    /**
     * 学习进度百分比，0-100
     */
    @TableField(value = "progress")
    private Integer progress;

    /**
     * 最后更新时间
     */
    @TableField(value = "last_modify_time")
    private Date lastModifyTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 学生id
     */
    public String getStudentId() {
        return studentId;
    }

    /**
     * 学生id
     */
    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    /**
     * 课程id
     */
    public String getCourseId() {
        return courseId;
    }

    /**
     * 课程id
     */
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    /**
     * 学习进度百分比，0-100
     */
    public Integer getProgress() {
        return progress;
    }

    /**
     * 学习进度百分比，0-100
     */
    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    /**
     * 最后更新时间
     */
    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    /**
     * 最后更新时间
     */
    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public String getCoursewareId() {
        return coursewareId;
    }

    public void setCoursewareId(String coursewareId) {
        this.coursewareId = coursewareId;
    }
}