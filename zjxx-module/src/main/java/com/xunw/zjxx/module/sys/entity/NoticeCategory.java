package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.Status;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 通知公告分类表
 */
@TableName("sys_notice_category")
public class NoticeCategory implements Serializable {

	private static final long serialVersionUID = 8250531316086906764L;

	//主键id
    @TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

    //分类名称
    @TableField("name")
    private String name;

    //父类型ID
    @TableField("parent_id")
    private String parentId;

    //排序
    @TableField("sort")
    private Integer sort;

    //备注
    @TableField("remark")
    private String remark;

    //创建用户id
    @TableField("creator_id")
    private String creatorId;

    //创建时间
    @TableField("create_time")
    private Date createTime;

    //修改用户id
    @TableField("updator_id")
    private String updatorId;

    //修改时间
    @TableField("update_time")
    private Date updateTime;

    //主办单位ID
    @TableField("host_org_id")
    private String hostOrgId;


    //状态
    @TableField("status")
    private Status status;

    //项目集合
    @TableField(exist = false)
    private List<Notice> noticeList = new ArrayList();


    public NoticeCategory() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    public List<Notice> getNoticeList() {
        return noticeList;
    }

    public void setNoticeList(List<Notice> noticeList) {
        this.noticeList = noticeList;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }
}

