package com.xunw.zjxx.module.comm.mapper;


import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.comm.params.OrderQueryParams;
import com.xunw.zjxx.module.enums.InvoiceStatus;
import com.xunw.zjxx.module.enums.OrderStatus;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.xunw.zjxx.module.comm.entity.CommOrder;

/**
* <AUTHOR>
* @description 针对表【comm_order】的数据库操作Mapper
* @createDate 2023-08-08 13:32:09
*/
public interface CommOrderMapper extends BaseMapper<CommOrder> {

    List<Map<String, Object>> getStudentOrderByStudentId(@Param("studentId") String studentId,@Param("status") String status);

    List<Map<String, Object>> getStudentOrderByOrderId(@Param("orderId") String orderId);

    List<Map<String, Object>> getCourseInfoByIds(@Param("courseIds") List<String> courseIds);

    List<Map<String, Object>> getStudentInvoiceByStudentId(@Param("studentId")String studentId, @Param("status") String status);

    List<Map<String, Object>> orderDetail(@Param("id") String id);

    List<Map<String, Object>> pageQuery(Map<String, Object> condition, Page<?> page);
}




