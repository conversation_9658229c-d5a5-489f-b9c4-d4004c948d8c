package com.xunw.zjxx.module.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.DateUtils;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.biz.entity.ExamData;
import com.xunw.zjxx.module.biz.entity.ExamPaper;
import com.xunw.zjxx.module.biz.mapper.ExamDataMapper;
import com.xunw.zjxx.module.biz.mapper.ExamPaperMapper;
import com.xunw.zjxx.module.enums.PaperCategory;
import com.xunw.zjxx.module.enums.SysSettingEnum;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.service.CourseService;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.sys.service.SettingService;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_exam_paper(试卷表)】的数据库操作Service
* @createDate 2023-08-18 13:33:27
*/
@Service
public class ExamPaperService extends BaseCRUDService<ExamPaperMapper, ExamPaper> {
    @Autowired
    private CourseService courseService;
    @Autowired
    private ExamDataMapper examDataMapper;
    @Autowired
    private SettingService settingService;
    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private CheckService checkService;

    /**
     * 查询学员的作业、考试
     */
    @GlobalTransactional
    @Transactional
    public Object examList(PaperCategory category,String courseId,String accessToken,String isDone, LoginUser loginUser) throws Exception {
        //获取所有的购买的课程
        List<Map<String,Object>> bmCouresList = courseService.getBmCourseByStudentId(loginUser.getId(), Constants.YES);
        if (bmCouresList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> courseIds = null;
        if (category != PaperCategory.HOMEWORK) {
        	courseIds = bmCouresList.stream().map(x->String.valueOf(x.get("id"))).collect(Collectors.toList());
        }
        int isSimulate = 0;
        if (category == PaperCategory.SIMULATE) {
            category = PaperCategory.EXAMINE;
            isSimulate = 1;
        }
        String s = JSONObject.toJSONString(examServiceApi.getStudentExamListBySimulate(category == PaperCategory.HOMEWORK?courseId : courseIds.size() > 0 ? StringUtils.join(courseIds, ",") : null,
                isDone, isSimulate, category,Integer.MAX_VALUE));
        JSONArray jsonArray = JSONObject.parseObject(s).getJSONObject("data").getJSONArray("records");
        List<Map> result = jsonArray.stream().map( x->{
            JSONObject map = JSON.parseObject(JSON.toJSONString(x));
            Course course = courseService.selectById(map.getString("courseId"));
            map.put("courseName", course.getName());
            return map;
        }).collect(Collectors.toList());;
        return result;
    }

    @GlobalTransactional
    @Transactional
    public Object examStart(String examDataId, String paperId, LoginUser loginUser, String accessToken) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("examDataId", examDataId);
        map.put("paperId", paperId);
        //获取系统配置的最短交卷时间
        ExamPaper examPaper = mapper.selectById(paperId);
        String shortestSubmitTime = "";
        if (examPaper.getCategory() == PaperCategory.HOMEWORK) {
            shortestSubmitTime = settingService.getHostOrgSettingByKey(loginUser.getHostOrg().getId(), SysSettingEnum.PSZY_MIN_JJSJ);
		} else {
            shortestSubmitTime = settingService.getHostOrgSettingByKey(loginUser.getHostOrg().getId(), SysSettingEnum.ZHCY_MIN_JJSJ);
		}
        map.put("shortestSubmitTime", shortestSubmitTime);
        Map data = JSON.parseObject(JSON.toJSONString(examServiceApi.getExamStart(examDataId,paperId,shortestSubmitTime).getData()), Map.class);
        List<ExamData> examDatas = examDataMapper.selectList(new EntityWrapper<ExamData>()
                .eq("student_id", loginUser.getId())
                .eq("paper_id", paperId));
        if (CollectionUtils.isEmpty(examDatas)) {
            ExamData examData = new ExamData();
            examData.setId(data.get("id").toString());
            examData.setPaperId(paperId);
            examData.setStudentId(loginUser.getId());
            examData.setStartTime(DateUtils.parse(data.get("startTime").toString()));
            examData.setIsSubmit(0);
            examDataMapper.insert(examData);
        } else {
            ExamData examData = examDatas.get(0);
            if (StringUtils.isEmpty(examDataId)) {
                examData.setStartTime(DateUtils.parse(data.get("startTime").toString()));
                examDataMapper.updateById(examData);
            }
        }
        return data;
    }

    @GlobalTransactional
    @Transactional
    public Object examDetails(String examDataId, LoginUser loginUser, String accessToken,String paperId) throws Exception {
        Map data = JSON.parseObject(JSONObject.parseObject(JSON.toJSONString(examServiceApi.getExamDetail(examDataId,paperId))).getString("data"), Map.class);
        ExamPaper examPaper = mapper.selectById(paperId);
        data.put("courseId", examPaper.getCourseId());
        return data;
    }

    @GlobalTransactional
    @Transactional
    public Object uploadAns(String examDataId, String url) throws Exception {
        examServiceApi.getExamUploadCard(examDataId,url);
        return true;
    }

    @GlobalTransactional
    @Transactional
    public Object deleteAns(String examDataId, String url) {
        examServiceApi.examDeleteCard(examDataId,url);
        return true;
    }

    /**
     * 智能组卷
     * @throws Exception
     */
    @GlobalTransactional
    @Transactional
    public Object autoBuildPaper(JSONObject json, String hostOrgId, String token) throws Exception {
        PaperCategory category = PaperCategory.findByEnumName(json.getString("category"));
        Integer isSimulate = category == PaperCategory.SIMULATE ? 1 : 0;
        category = category == PaperCategory.EXAMINE || category == PaperCategory.SIMULATE ? PaperCategory.EXAMINE : PaperCategory.HOMEWORK;
        Map<String, Object> map = new HashMap<>();
        map.put("name", json.get("name"));
        map.put("courseId", json.get("courseId"));
        map.put("status", json.get("status"));
        map.put("category", category);
        map.put("answerExpose", 1);
        map.put("needPhoto", Constants.NO);
        map.put("faceComparison", Constants.NO);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("startTime", sdf.format(new Date()));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.YEAR, 3);
        map.put("endTime", sdf.format(calendar.getTime()));
        map.put("duration", 120);
        map.put("isAllowPhone", Constants.YES);
        map.put("bizType", "NONE");
        map.put("orderType", 1);
        map.put("showAnswer", 0);
        map.put("showMode", 1);
        map.put("totalScore", json.getInteger("totalScore"));
        map.put("passScore", json.getInteger("totalScore") * 0.6);
        map.put("paperType", 1);//1 是模板试卷
        map.put("isSimulate", isSimulate);// 是否是模拟考试
        map.put("orgId", hostOrgId);
        map.put("batchId", Constants.UPDATE_STUDY_TIME_BATCH_ID);
        map.put("sections", JSONArray.parseArray(String.valueOf(json.get("sections"))));
        Object resp = examServiceApi.paperAddAndAuto(new JSONObject(map)).getData();
        JSONObject data = JSON.parseObject(JSON.toJSONString(resp));
        ExamPaper examPaper = new ExamPaper();
        examPaper.setId(data.getString("id"));
        examPaper.setName(data.getString("name"));
        examPaper.setCategory(category);//作业、考试
        examPaper.setCourseId(data.getString("courseId"));
        examPaper.setCreateTime((data.getDate("createdTime")));
        examPaper.setCreatorId(data.getString("createdBy"));
        examPaper.setTotalScore(data.getString("totalScore"));
        mapper.insert(examPaper);
        return examPaper;
    }

    @GlobalTransactional
    @Transactional
    public Object examSave(JSONObject json, LoginUser loginUser, String accessToken) throws Exception {
        examServiceApi.examSave(json);
        return true;
    }

    @GlobalTransactional
    @Transactional
    public void editPaperRepo(JSONObject json) {
        ExamPaper examPaper = mapper.selectById(json.getString("id"));
        examPaper.setTotalScore(json.getString("totalScore"));
        mapper.updateById(examPaper);
        examServiceApi.paperUpdate(json);
    }

    @GlobalTransactional
    @Transactional
    public void deletePaperRepo(String id) {
        checkService.isDeletePaper(id);
        examServiceApi.paperDelete(id);
        mapper.deleteById(id);
    }

    @GlobalTransactional
    @Transactional
    public void configPaper(JSONObject json) {
        examServiceApi.paperPackage(json);
        ExamPaper examPaper = mapper.selectById(json.getString("id"));
        examPaper.setTotalScore(json.getString("totalScore"));
        mapper.updateById(examPaper);
    }

    /**
     * 添加试卷
     */
    @GlobalTransactional
    @Transactional
    public void addPaper(JSONObject json, String hostOrgId) {
        PaperCategory category = PaperCategory.findByEnumName(json.getString("category"));
        Integer isSimulate = category == PaperCategory.SIMULATE ? 1 : 0;
        category = category == PaperCategory.EXAMINE || category == PaperCategory.SIMULATE ? PaperCategory.EXAMINE : PaperCategory.HOMEWORK;
        Map<String, Object> map = new HashMap<>();
        map.put("name", json.get("name"));
        map.put("courseId", json.get("courseId"));
        map.put("status", json.get("status"));
        map.put("category", category);
        map.put("answerExpose", 1);
        map.put("needPhoto", Constants.NO);
        map.put("faceComparison", Constants.NO);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("startTime", sdf.format(new Date()));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.YEAR, 3);
        map.put("endTime", sdf.format(calendar.getTime()));
        map.put("duration", 120);
        map.put("isAllowPhone", Constants.YES);
        map.put("bizType", "NONE");
        map.put("orderType", 1);
        map.put("showAnswer", 0);
        map.put("showMode", 1);
        map.put("totalScore", json.getInteger("totalScore"));
        map.put("passScore", json.getInteger("totalScore") * 0.6);
        map.put("paperType", 0);//0 是普通试卷
        map.put("isSimulate", isSimulate);// 是否是模拟考试
        map.put("orgId", hostOrgId);
        map.put("batchId", Constants.UPDATE_STUDY_TIME_BATCH_ID);
        Object resp = examServiceApi.paperAdd(new JSONObject(map)).getData();
        JSONObject data = JSON.parseObject(JSON.toJSONString(resp));
        ExamPaper examPaper = new ExamPaper();
        examPaper.setId(data.getString("id"));
        examPaper.setName(data.getString("name"));
        examPaper.setCategory(category);//作业、考试
        examPaper.setCourseId(data.getString("courseId"));
        examPaper.setCreateTime((data.getDate("createdTime")));
        examPaper.setCreatorId(data.getString("createdBy"));
        examPaper.setTotalScore(data.getString("totalScore"));
        mapper.insert(examPaper);
    }
}
