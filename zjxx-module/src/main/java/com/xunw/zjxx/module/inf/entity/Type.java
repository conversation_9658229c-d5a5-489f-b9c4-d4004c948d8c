package com.xunw.zjxx.module.inf.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.Category;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * @TableName inf_type
 */
@TableName(value ="inf_type")
public class Type implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 类型名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 类型代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 父类型id
     */
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建用户id
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 修改用户id
     */
    @TableField(value = "updator_id")
    private String updatorId;

    /**
     * 排序值
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 枚举 :公需课  专业课
     */
    @TableField(value = "category")
    private Category category;

    /**
     * 主办单位id
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private List<Type> child;


    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 类型名称
     */
    public String getName() {
        return name;
    }

    /**
     * 类型名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 类型代码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 父类型id
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 父类型id
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建用户id
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建用户id
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 修改用户id
     */
    public String getUpdatorId() {
        return updatorId;
    }

    /**
     * 修改用户id
     */
    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    /**
     * 排序值
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * 排序值
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Category getCategory() {
		return category;
	}

	public void setCategory(Category category) {
		this.category = category;
	}

	/**
     * 主办单位id
     */
    public String getHostOrgId() {
        return hostOrgId;
    }

    /**
     * 主办单位id
     */
    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }


    public List<Type> getChild() {
        return child;
    }

    public void setChild(List<Type> child) {
        this.child = child;
    }
}
