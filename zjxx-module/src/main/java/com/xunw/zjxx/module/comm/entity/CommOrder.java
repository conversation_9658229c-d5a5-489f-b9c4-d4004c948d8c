package com.xunw.zjxx.module.comm.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.module.enums.OrderStatus;
import com.xunw.zjxx.module.enums.PayMethod;
import com.xunw.zjxx.module.enums.PayPlatform;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName comm_order
 */
@TableName(value ="comm_order")
public class CommOrder implements Serializable {
    /**
     * 主键 即订单编号
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 下单时间 调用支付系统接口下单的时间
     */
    @TableField(value = "xd_time")
    private Date xdTime;

    /**
     * 缴费用户类型 ：admin 管理员, student 考生
     */
    @TableField(value = "user_type")
    private UserTypeEnum userType;

    /**
     * 缴费用户ID
     */
    @TableField(value = "pay_user_id")
    private String payUserId;

    /**
     * 缴费用户所属机构ID
     */
    @TableField(value = "pay_org_id")
    private String payOrgId;

    /**
     * 主办单位id
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    /**
     * 缴费用户姓名
     */
    @TableField(value = "pay_name")
    private String payName;

    /**
     * 订单金额 单位 元
     */
    @TableField(value = "amount")
    private Double amount;

    /**
     * 订单描述信息 支付系统商品描述
     */
    @TableField(value = "product")
    private String product;

    /**
     * 支付方式,ALI_PC_PAY 支付宝电脑网站扫码支付,WX_PC_PAY 微信电脑网站扫码支付
     */
    @TableField(value = "pay_method")
    private PayMethod payMethod;

    /**
     * 订单状态：已支付   未支付
     */
    @TableField(value = "status")
    private OrderStatus status;

    /**
     * 交易流水号 支付成功时才存在
     */
    @TableField(value = "trans_number")
    private String transNumber;

    /**
     * 支付时间 支付系统记录的支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 支付系统异步回调通知时间
     */
    @TableField(value = "notify_time")
    private Date notifyTime;

    /**
     * 备注信息
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 付款账号
     */
    @TableField(value = "pay_account")
    private String payAccount;

    /**
     * 订单创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "get_result_by")
    private String getResultBy;

    /**
     * 收款方名称,收款方
     */
    @TableField(value = "bank_id")
    private String bankId;

    /**
     * 聚合支付订单编号
     */
    @TableField(value = "union_order_no")
    private String unionOrderNo;

    /**
     * 主键 即订单编号
     */
    public String getId() {
        return id;
    }

    /**
     * 主键 即订单编号
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 下单时间 调用支付系统接口下单的时间
     */
    public Date getXdTime() {
        return xdTime;
    }

    /**
     * 下单时间 调用支付系统接口下单的时间
     */
    public void setXdTime(Date xdTime) {
        this.xdTime = xdTime;
    }

    public UserTypeEnum getUserType() {
		return userType;
	}

	public void setUserType(UserTypeEnum userType) {
		this.userType = userType;
	}

	/**
     * 缴费用户ID
     */
    public String getPayUserId() {
        return payUserId;
    }

    /**
     * 缴费用户ID
     */
    public void setPayUserId(String payUserId) {
        this.payUserId = payUserId;
    }

    /**
     * 缴费用户所属机构ID
     */
    public String getPayOrgId() {
        return payOrgId;
    }

    /**
     * 缴费用户所属机构ID
     */
    public void setPayOrgId(String payOrgId) {
        this.payOrgId = payOrgId;
    }

    /**
     * 主办单位id
     */
    public String getHostOrgId() {
        return hostOrgId;
    }

    /**
     * 主办单位id
     */
    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    /**
     * 缴费用户姓名
     */
    public String getPayName() {
        return payName;
    }

    /**
     * 缴费用户姓名
     */
    public void setPayName(String payName) {
        this.payName = payName;
    }

    /**
     * 订单描述信息 支付系统商品描述
     */
    public String getProduct() {
        return product;
    }

    /**
     * 订单描述信息 支付系统商品描述
     */
    public void setProduct(String product) {
        this.product = product;
    }
   
    public PayMethod getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(PayMethod payMethod) {
		this.payMethod = payMethod;
	}

	/**
     * 订单状态：已支付   未支付
     */
    public OrderStatus getStatus() {
        return status;
    }

    /**
     * 订单状态：已支付   未支付
     */
    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    /**
     * 交易流水号 支付成功时才存在
     */
    public String getTransNumber() {
        return transNumber;
    }

    /**
     * 交易流水号 支付成功时才存在
     */
    public void setTransNumber(String transNumber) {
        this.transNumber = transNumber;
    }

    /**
     * 支付时间 支付系统记录的支付时间
     */
    public Date getPayTime() {
        return payTime;
    }

    /**
     * 支付时间 支付系统记录的支付时间
     */
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    /**
     * 支付系统异步回调通知时间
     */
    public Date getNotifyTime() {
        return notifyTime;
    }

    /**
     * 支付系统异步回调通知时间
     */
    public void setNotifyTime(Date notifyTime) {
        this.notifyTime = notifyTime;
    }

    /**
     * 备注信息
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注信息
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 付款账号
     */
    public String getPayAccount() {
        return payAccount;
    }

    /**
     * 付款账号
     */
    public void setPayAccount(String payAccount) {
        this.payAccount = payAccount;
    }

    /**
     * 订单创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 订单创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 
     */
    public String getGetResultBy() {
        return getResultBy;
    }

    /**
     * 
     */
    public void setGetResultBy(String getResultBy) {
        this.getResultBy = getResultBy;
    }

    /**
     * 收款方名称,收款方
     */
    public String getBankId() {
        return bankId;
    }

    /**
     * 收款方名称,收款方
     */
    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    /**
     * 聚合支付订单编号
     */
    public String getUnionOrderNo() {
        return unionOrderNo;
    }

    /**
     * 聚合支付订单编号
     */
    public void setUnionOrderNo(String unionOrderNo) {
        this.unionOrderNo = unionOrderNo;
    }

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}
}