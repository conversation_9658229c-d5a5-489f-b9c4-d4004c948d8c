package com.xunw.zjxx.module.biz.service;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.DBUtils;
import com.xunw.zjxx.module.biz.entity.Xm;
import com.xunw.zjxx.module.biz.entity.XmCourseScope;
import com.xunw.zjxx.module.biz.mapper.XmCourseScopeMapper;
import com.xunw.zjxx.module.biz.mapper.XmMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
* <AUTHOR>
* @description 针对表【biz_xm_course_scope(项目课程范围表)】的数据库操作Service
* @createDate 2023-08-16 10:10:16
*/
@Service
public class XmCourseScopeService extends BaseCRUDService<XmCourseScopeMapper, XmCourseScope> {

    @Autowired
    private XmMapper xmMapper;

    public void add(Xm xm, Integer publicHours, Integer specialtyHours, String publicTypeId, String specialtyTypeId) {
        xm.setGxkHours(publicHours);
        xm.setZykHours(specialtyHours);
        xmMapper.updateById(xm);
        String[] publicTypeIds = publicTypeId.split(",");
        String[] specialtyTypeIds= specialtyTypeId.split(",");
        ArrayList<String> ids = new ArrayList<>();
        ids.addAll(Arrays.asList(publicTypeIds));
        ids.addAll(Arrays.asList(specialtyTypeIds));
        List<XmCourseScope> XmCourseScopeList = new ArrayList<>();
        for (String id : ids) {
            XmCourseScope xmCourseScope = new XmCourseScope();
            xmCourseScope.setId(BaseUtil.generateId());
            xmCourseScope.setXmId(xm.getId());
            xmCourseScope.setTypeId(id);
            XmCourseScopeList.add(xmCourseScope);
        }
        DBUtils.insertBatch(XmCourseScopeList, XmCourseScope.class);
    }

    @Transactional
    public void edit(Xm Xm, Integer publicHours, Integer specialtyHours, String publicTypeId, String specialtyTypeId) {
        mapper.delete(new EntityWrapper<XmCourseScope>()
                .eq("xm_id", Xm.getId()));
        this.add(Xm, publicHours, specialtyHours, publicTypeId, specialtyTypeId);
    }

    /**
     * 通过学习计划id获取课程设置信息
     * @param id
     * @return
     */
    public Map<String, Object> getCourseSetting(String id) {
        Map<String, Object> result = new HashMap<>();
        Xm xm = xmMapper.selectById(id);
        result.put("zykHours", xm.getZykHours());
        result.put("gxkHours", xm.getGxkHours());
        result.put("courseSetting", mapper.getCourseSetting(id));
        return result;
    }

    /**
     * 通过课程id获取项目信息
     * @param courseId
     * @return
     */

    public String getXmIdbyCourseId(String courseId){
        return mapper.getXmIdbyCourseId(courseId);
    }
}
