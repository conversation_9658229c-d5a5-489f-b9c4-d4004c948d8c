package com.xunw.zjxx.module.rpc;

import com.xunw.zjxx.common.core.ResultMsg;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "face-service")
public interface FaceServiceApi {

    @RequestMapping("/api/exam/verify/log")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examVerifyLog(@RequestParam(required = false, value = "batchId") String batchId,
                            @RequestParam(required = false, value = "courseId") String courseId,
                            @RequestParam(required = false, value = "paperId") String paperId,
                            @RequestParam(required = false, value = "studentId") String studentId,
                            @RequestParam(required = false, value = "minConfidence") Double minConfidence,
                            @RequestParam(required = false, value = "maxConfidence") Double maxConfidence,
                            @RequestParam(required = false, value = "bizType") String bizType,
                            @RequestParam(required = false, value = "isPass") Integer isPass,
                            @RequestParam(required = false, value = "marked") Integer marked,
                            @RequestParam(required = false, value = "uploadUserType") String uploadUserType,
                            @RequestParam(required = false, value = "keyword") String keyword,
                            @RequestParam(required = false, value = "orgId") String hostOrgId,
                            @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                            @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/learn/capture/log")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg learnCaptureLog(@RequestParam(required = false, value = "batchId") String batchId,
                              @RequestParam(required = false, value = "sourceId") String sourceId,
                              @RequestParam(required = false, value = "chapterId") String chapterId,
                              @RequestParam(required = false, value = "lessonId") String lessonId,
                              @RequestParam(required = false, value = "studentId") String studentId,
                              @RequestParam(required = false, value = "bizType") String bizType,
                              @RequestParam(required = false, value = "isFace") Integer isFace,
                              @RequestParam(required = false, value = "keyword") String keyword,
                              @RequestParam(required = false, value = "orgId") String hostOrgId,
                              @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                              @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/learn/verify/log")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg learnVerifyLog(@RequestParam(required = false, value = "batchId") String batchId,
                             @RequestParam(required = false, value = "paperId") String paperId,
                             @RequestParam(required = false, value = "courseId") String courseId,
                             @RequestParam(required = false, value = "sourceId") String sourceId,
                             @RequestParam(required = false, value = "studentId") String studentId,
                             @RequestParam(required = false, value = "minConfidence") Double minConfidence,
                             @RequestParam(required = false, value = "maxConfidence") Double maxConfidence,
                             @RequestParam(required = false, value = "bizType") String bizType,
                             @RequestParam(required = false, value = "isPass") Integer isPass,
                             @RequestParam(required = false, value = "marked") Integer marked,
                             @RequestParam(required = false, value = "uploadUserType") String uploadUserType,
                             @RequestParam(required = false, value = "keyword") String keyword,
                             @RequestParam(required = false, value = "orgId") String hostOrgId,
                             @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                             @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/exam/capture")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getExamCapture(@RequestParam(required = false, value = "batchId") String batchId,
                          @RequestParam(required = false, value = "paperId") String paperId,
                          @RequestParam(required = false, value = "courseId") String courseId,
                          @RequestParam(required = false, value = "photoPath") String photoPath,
                          @RequestParam(required = false, value = "bizType") String bizType,
                          @RequestParam(required = false, value = "isSaveDb") String isSaveDb,
                          @RequestParam(required = false, value = "isCheck") String isCheck,
                             @RequestParam(required = false, value = "orgId") String hostOrgId);

    @RequestMapping("/api/exam/verify")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examVerify(@RequestParam(required = false, value = "batchId") String batchId,
                      @RequestParam(required = false, value = "paperId") String paperId,
                      @RequestParam(required = false, value = "courseId") String courseId,
                      @RequestParam(required = false, value = "studentPhotoPath") String studentPhoto,
                      @RequestParam(required = false, value = "uploadPhotoPath") String photoPath,
                      @RequestParam(required = false, value = "uploadUserType") String uploadUserType,
                      @RequestParam(required = false, value = "threshold") Object threshold,
                      @RequestParam(required = false, value = "bizType") String bizType,
                      @RequestParam(required = false, value = "isSaveDb") String isSaveDb,
                         @RequestParam(required = false, value = "orgId") String hostOrgId);

    @RequestMapping("/api/learn/capture")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg learnCapture(@RequestParam(required = false, value = "batchId") String batchId,
                           @RequestParam(required = false, value = "sourceId") String coursewareId,
                           @RequestParam(required = false, value = "courseId") String courseId,
                           @RequestParam(required = false, value = "chapterId") String chapterId,
                           @RequestParam(required = false, value = "lessonId") String lessonId,
                           @RequestParam(required = false, value = "photoPath") String photoPath,
                           @RequestParam(required = false, value = "bizType") String bizType,
                           @RequestParam(required = false, value = "isSaveDb") String isSaveDb,
                           @RequestParam(required = false, value = "isCheck") String isCheck,
                           @RequestParam(required = false, value = "orgId") String hostOrgId);

    @RequestMapping("/api/learn/verify")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg learnVerify(@RequestParam(required = false, value = "batchId") String batchId,
                          @RequestParam(required = false, value = "sourceId") String coursewareId,
                          @RequestParam(required = false, value = "studentPhotoPath") String studentPhotoPath,
                          @RequestParam(required = false, value = "uploadPhotoPath") String uploadPhotoPath,
                          @RequestParam(required = false, value = "threshold") Object threshold,
                          @RequestParam(required = false, value = "bizType") String bizType,
                          @RequestParam(required = false, value = "isSaveDb") String isSaveDb,
                          @RequestParam(required = false, value = "orgId") String hostOrgId);
}
