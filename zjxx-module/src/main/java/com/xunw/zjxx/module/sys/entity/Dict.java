package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName sys_dict
 */
@TableName(value ="sys_dict")
public class Dict implements Serializable {
    /**
     * 主键
     */
    @TableField(value = "id")
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 字典编码
     */
    @TableField(value = "dict_code")
    private String dictCode;

    /**
     * 字典名称，用于展示
     */
    @TableField(value = "dict_name")
    private String dictName;

    /**
     * 字典值
     */
    @TableField(value = "dict_value")
    private String dictValue;

    /**
     * 字典描述
     */
    @TableField(value = "dict_desc")
    private String dictDesc;

    /**
     * 字典状态 OK启用 BLOCK停用
     */
    @TableField(value = "dict_status")
    private String dictStatus;

    /**
     * 排序值
     */
    @TableField(value = "dict_sort")
    private Integer dictSort;

    /**
     * 是否默认 1默认 0否
     */
    @TableField(value = "is_default")
    private String isDefault;

    /**
     * 创建人
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新人id
     */
    @TableField(value = "updator_id")
    private String updatorId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 父id
     */
    @TableField(value = "parent_id")
    private String parentId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 字典编码
     */
    public String getDictCode() {
        return dictCode;
    }

    /**
     * 字典编码
     */
    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }

    /**
     * 字典名称，用于展示
     */
    public String getDictName() {
        return dictName;
    }

    /**
     * 字典名称，用于展示
     */
    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    /**
     * 字典值
     */
    public String getDictValue() {
        return dictValue;
    }

    /**
     * 字典值
     */
    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }

    /**
     * 字典描述
     */
    public String getDictDesc() {
        return dictDesc;
    }

    /**
     * 字典描述
     */
    public void setDictDesc(String dictDesc) {
        this.dictDesc = dictDesc;
    }

    /**
     * 字典状态 OK启用 BLOCK停用
     */
    public String getDictStatus() {
        return dictStatus;
    }

    /**
     * 字典状态 OK启用 BLOCK停用
     */
    public void setDictStatus(String dictStatus) {
        this.dictStatus = dictStatus;
    }

    /**
     * 排序值
     */
    public Integer getDictSort() {
        return dictSort;
    }

    /**
     * 排序值
     */
    public void setDictSort(Integer dictSort) {
        this.dictSort = dictSort;
    }

    /**
     * 是否默认 1默认 0否
     */
    public String getIsDefault() {
        return isDefault;
    }

    /**
     * 是否默认 1默认 0否
     */
    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * 创建人
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建人
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 更新人id
     */
    public String getUpdatorId() {
        return updatorId;
    }

    /**
     * 更新人id
     */
    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    /**
     * 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 父id
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 父id
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
}