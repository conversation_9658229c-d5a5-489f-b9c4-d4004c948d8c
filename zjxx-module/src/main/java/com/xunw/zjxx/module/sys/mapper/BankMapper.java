package com.xunw.zjxx.module.sys.mapper;


import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.sys.entity.Bank;

/**
* <AUTHOR>
* @description 针对表【sys_bank】的数据库操作Mapper
* @createDate 2023-08-08 13:32:09
* @Entity generator.entity.Bank
*/
public interface BankMapper extends BaseMapper<Bank> {

    List<Map<String,Object>> getBankList(Map<String, Object> condition , Page<?> page);
}




