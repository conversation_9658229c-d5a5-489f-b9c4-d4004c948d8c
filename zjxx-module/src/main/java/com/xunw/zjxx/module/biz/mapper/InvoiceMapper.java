package com.xunw.zjxx.module.biz.mapper;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.biz.entity.BizInvoiceApply;
import com.xunw.zjxx.module.dto.InvoiceDetailDto;
import com.xunw.zjxx.module.dto.InvoiceDto;
import org.apache.ibatis.annotations.Param;

public interface InvoiceMapper extends BaseMapper<BizInvoiceApply> {

    List<InvoiceDto> list(Map<String, Object> condition, Page<?> page);

    List<String> courselist(@Param("id") String id);

    List<InvoiceDetailDto> invoiceDetail(@Param("id") String id);

    List<Map<String, Object>> getOrdersByApplyID(@Param("id") String id);
}
