package com.xunw.zjxx.module.dto;

import java.util.Date;
import java.util.List;

public class InvoiceDetailDto {

    private String serailNumber;//流水号
    private String orderNumber;//订单号
    private String amount;//金额
    private List<String> courselist;//课程列表
    private String invoiceTitle;
    private String invoiceCode;
    private String invoiceBank;
    private String invoiceBankAccount;
    private String invoiceOrgAddress;
    private String invoiceOrgPhone;
    private String url;
    private String ids;
    private String id;//发票申请id

    public String getSerailNumber() {
        return serailNumber;
    }

    public void setSerailNumber(String serailNumber) {
        this.serailNumber = serailNumber;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public List<String> getCourselist() {
        return courselist;
    }

    public void setCourselist(List<String> courselist) {
        this.courselist = courselist;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoiceBank() {
        return invoiceBank;
    }

    public void setInvoiceBank(String invoiceBank) {
        this.invoiceBank = invoiceBank;
    }

    public String getInvoiceBankAccount() {
        return invoiceBankAccount;
    }

    public void setInvoiceBankAccount(String invoiceBankAccount) {
        this.invoiceBankAccount = invoiceBankAccount;
    }

    public String getInvoiceOrgAddress() {
        return invoiceOrgAddress;
    }

    public void setInvoiceOrgAddress(String invoiceOrgAddress) {
        this.invoiceOrgAddress = invoiceOrgAddress;
    }

    public String getInvoiceOrgPhone() {
        return invoiceOrgPhone;
    }

    public void setInvoiceOrgPhone(String invoiceOrgPhone) {
        this.invoiceOrgPhone = invoiceOrgPhone;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
