package com.xunw.zjxx.module.biz.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 *
 * @TableName biz_student_bm_course
 */
@TableName(value ="biz_student_bm_course")
public class StudentBmCourse implements Serializable {
	
    private static final long serialVersionUID = -5652323799117018432L;
    

	/**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 课程id
     */
    @TableField(value = "course_id")
    private String courseId;

    /**
     * 学员用户id
     */
    @TableField(value = "student_id")
    private String studentId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建用户id
     */
    @TableField(value = "creator_id")
    private String creatorId;


    /**
     * 是否支付
     */
    @TableField(value = "is_payed")
    private String isPayed;

    /**
     * 主键id
     */
    public String getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 课程id
     */
    public String getCourseId() {
        return courseId;
    }

    /**
     * 课程id
     */
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    /**
     * 学员用户id
     */
    public String getStudentId() {
        return studentId;
    }

    /**
     * 学员用户id
     */
    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建用户id
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建用户id
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public StudentBmCourse() {
    }

    public String getIsPayed() {
        return isPayed;
    }

    public void setIsPayed(String isPayed) {
        this.isPayed = isPayed;
    }

}
