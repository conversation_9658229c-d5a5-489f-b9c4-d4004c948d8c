package com.xunw.zjxx.module.inf.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.biz.service.CheckService;
import com.xunw.zjxx.module.dto.CoursewareVo;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.params.CoursewareQueryParams;
import com.xunw.zjxx.module.rpc.LearningCoursewareServiceApi;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Service
public class CoursewareService {

	@Autowired
	private CourseService courseService;
	@Autowired
	private CheckService checkService;
	@Autowired
	private LearningCoursewareServiceApi learningCoursewareServiceApi;

	@GlobalTransactional
	@Transactional
	public void deleteById(String id, String hostOrgId,String token) {
		//查询课件
		Object resp = learningCoursewareServiceApi.coursewareGetById(id, null).getData();
		String courseId = JSON.parseObject(JSON.toJSONString(resp)).getString("courseId");
		//查询是否有学习记录
		checkService.isDeleteKj(id, token);
		//删除课件
		learningCoursewareServiceApi.coursewareRemove(id);
		//更新课程表
		this.isKjUploaded(courseId, hostOrgId, token);
	}

	@GlobalTransactional
	@Transactional
	public void add(JSONObject jsonObject, String hostOrgId, String token) {
		if (jsonObject.getString("status").equals(Status.OK.name())) {
			this.checkCanUploadEnabledCoursewareForAdd(jsonObject.getString("courseId"));
		}
		String chapters = jsonObject.getJSONArray("chapters").toString();
		learningCoursewareServiceApi.coursewareAdd(jsonObject.getString("courseId"), jsonObject.getString("teacherName"),
				jsonObject.getString("lecturerPhoto"), 0, Status.findByEnumName(jsonObject.getString("status")), null, null, 0, hostOrgId, null, chapters);
		//更新本地课程课件状态
		this.isKjUploaded(jsonObject.getString("courseId"), hostOrgId, token);
	}

	private void isKjUploaded(String courseId, String hostOrgId, String token) {
		//查询开启状态的课件列表
		String resp = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareList(null,courseId,Status.OK.name(),null,hostOrgId,null,1,Integer.MAX_VALUE));
		JSONArray jsonArray = JSON.parseObject(resp).getJSONObject("data").getJSONArray("records");
		Course course = courseService.selectById(courseId);
		course.setIsKjUploaded(jsonArray != null && !jsonArray.isEmpty()?Constants.YES:Constants.NO);
		course.setKjStatus(jsonArray != null && !jsonArray.isEmpty()?Status.OK:Status.BLOCK);
		courseService.updateAllColumnById(course);
	}

	/**
	 * 判断添加课件时是否允许上传当前课程启用的课件
	 */
	@GlobalTransactional
	@Transactional
	public void checkCanUploadEnabledCoursewareForAdd(String courseId) {
		CoursewareQueryParams params = new CoursewareQueryParams();
		String respStr = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareList(null,courseId,Status.OK.name(),null,null,null,params.getCurrent(),params.getSize()));
		Long count = JSONObject.parseObject(respStr).getJSONObject("data").getLong("total");
        if (count > 0) {
			throw BizException.withMessage("当前课程已经存在启用的课件，若继续操作，请修改课件状态为禁用");
		}
	}

	/**
	 * 判断编辑课件时是否允许上传当前课程启用的课件
	 */
	@GlobalTransactional
	@Transactional
	public void checkCanUploadEnabledCoursewareForEdit(String courseId, String coursewareId) {
		CoursewareQueryParams params = new CoursewareQueryParams();
		String respStr = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareList(null,courseId,Status.OK.name(),null,null,coursewareId,params.getCurrent(),params.getSize()));
        Long count = JSONObject.parseObject(respStr).getJSONObject("data").getLong("total");
        if (count > 0) {
			throw BizException.withMessage("当前课程已经存在启用的课件，若继续操作，请修改课件状态为禁用");
		}
	}

	@GlobalTransactional
	@Transactional
	public void coursewareImport(String id, String courseId, String teacherName,String lecturerPhoto, Status status, MultipartFile file, String hostOrgId, String token) throws IOException {
		if (status == Status.OK) {
			this.checkCanUploadEnabledCoursewareForAdd(courseId);
		}
		learningCoursewareServiceApi.coursewareImport(file, hostOrgId, courseId, teacherName, lecturerPhoto, 0, status,
				null, null, 0, null, id).getData();
		this.isKjUploaded(courseId, hostOrgId, token);
	}

	@GlobalTransactional
	@Transactional
	public void edit(JSONObject jsonObject, String hostOrgId, String token) {
		Object resp = learningCoursewareServiceApi.coursewareGetById(jsonObject.getString("id"), null).getData();
		//原课程id，防止修改课程
		String courseId = JSON.parseObject(JSON.toJSONString(resp)).getString("courseId");
		//课件由启用改为禁用
		if (Status.OK.name().equals(JSON.parseObject(JSON.toJSONString(resp)).getString("status"))
				&& Status.BLOCK.name().equals(jsonObject.getString("status"))
		&& !checkService.isAllowUpdateKj(jsonObject.getString("id"), token)) {
			throw BizException.withMessage("课件资源已被学员使用，无法禁用");
		}
		Course course = courseService.selectById(courseId);
		if (course != null) {
			//更新原来课程的是否有课件的状态
			this.isKjUploaded(courseId, hostOrgId, token);
		}
		//启用限制校验
		if (jsonObject.getString("status").equals(Status.OK.name())) {
			this.checkCanUploadEnabledCoursewareForEdit(jsonObject.getString("courseId"), jsonObject.getString("id"));
		}
		//修改课件
		String chapters = jsonObject.getJSONArray("chapters").toString();
		learningCoursewareServiceApi.coursewareEdit(jsonObject.getString("id"), jsonObject.getString("courseId"),
				jsonObject.getString("teacherName"), jsonObject.getString("lecturerPhoto"), 5,
				Status.findByEnumName(jsonObject.getString("status")), null, null, 0, hostOrgId, null, chapters);
		//更新现有本地课程课件状态
		this.isKjUploaded(jsonObject.getString("courseId"), hostOrgId, token);
	}
}
