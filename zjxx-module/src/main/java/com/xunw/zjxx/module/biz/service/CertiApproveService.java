package com.xunw.zjxx.module.biz.service;

import java.io.*;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.plugins.Page;
import com.microsoft.schemas.vml.STTrueFalse;
import com.xunw.zjxx.common.utils.*;
import com.xunw.zjxx.module.params.ZypxStudentCertiMgrParams;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BarcodeQRCode;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.biz.entity.CertiTpl;
import com.xunw.zjxx.module.biz.entity.StudentCerti;
import com.xunw.zjxx.module.biz.entity.Xm;
import com.xunw.zjxx.module.biz.mapper.CertiTplMapper;
import com.xunw.zjxx.module.biz.mapper.StudentCertiMapper;
import com.xunw.zjxx.module.biz.mapper.XmMapper;
import com.xunw.zjxx.module.biz.params.DoApproveQueryParams;
import com.xunw.zjxx.module.biz.params.ZypxCertiApproveQueryParams;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.service.StudentInfoService;

@Service
public class CertiApproveService  extends BaseCRUDService<StudentCertiMapper, StudentCerti> {

    @Autowired
    private XmMapper xmMapper;
    @Autowired
    private CertiTplMapper certiTplMapper;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private AttConfig attConfig;
    @Autowired
    private ScoreService scoreService;

    public Object list(ZypxCertiApproveQueryParams params) {
        return params.setRecords(mapper.pageQuery(params.getCondition(), params));
    }

    public void doApprove(DoApproveQueryParams params) {
        List<StudentCerti> list = new ArrayList<>();
        for (String id : params.getId().split(",")) {
            StudentCerti studentCerti = mapper.selectById(id);
            studentCerti.setCheckResult(params.getResult());
            studentCerti.setCheckRemark(params.getReason());
            if (Objects.equals(params.getResult(), "PASS")) {
                studentCerti.setCertiNum(this.generateCertiNum(studentCerti));
            }
            list.add(studentCerti);
        }
        DBUtils.updateAllColumnBatchById(list, StudentCerti.class);
    }

    /**
     * 生成证书编号
     */
    private String generateCertiNum(StudentCerti studentCerti) {
        Xm xm = xmMapper.selectById(studentCerti.getXmId());
        String prefix = String.valueOf((char) (new Random().nextInt(26) + 'A'));//生成随机字母
        String suffix = "00001";
        List<StudentCerti> studentCertis = mapper.getListByXmId(studentCerti.getXmId());
        if (CollectionUtils.isNotEmpty(studentCertis)) {
            //序列号递增
            suffix = String.valueOf(Integer.parseInt(studentCertis.get(0).getCertiNum().substring(studentCertis.get(0).getCertiNum().length()-5)) + 1);
        }
        //学习计划配置了就用学习计划的规则
        if (StringUtils.isNotEmpty(xm.getCertiNoPrefix())) {
            prefix = xm.getCertiNoPrefix();
        }
        if (StringUtils.isNotEmpty(xm.getCertiStartNo())) {
            Integer certiStartNo = Integer.valueOf(xm.getCertiStartNo());
            suffix = new DecimalFormat("00000").format(certiStartNo);
        }
        return prefix + suffix;
    }

    public Object selectPassList(ZypxCertiApproveQueryParams params) {
        params.setRecords(mapper.selectPassList(params.getCondition(), params));
        return params;
    }

    //根据项目配置的证书模板生成学员的证书,返回证书PDF下载地址
    public StudentCerti buildStudentCerti(String xmId, String studentId, String portalWebUrl) throws IOException, DocumentException {
        StudentCerti studentCerti = this.getStudentCerti(studentId,xmId);
        if (studentCerti != null && StringUtils.isNotEmpty(studentCerti.getCertiUrl()) && StringUtils.isNotEmpty(studentCerti.getCertiImgUrl())) {
            return studentCerti;
        }
        Xm zypxXm = xmMapper.selectById(xmId);
        if (org.apache.commons.lang3.StringUtils.isEmpty(zypxXm.getCertiTplId())) {
            throw BizException.withMessage("培训项目未设置结业证书模板");
        }
        if (zypxXm.getCertiDate() == null) {
            throw BizException.withMessage("培训项目未设置证书证芯日期");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(studentCerti.getCertiNum())) {
            throw BizException.withMessage("该学员尚未设置证书编号，无法生成结业证书");
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(portalWebUrl)) {
            throw BizException.withMessage("平台未配置学员端门户域名");
        }
        //准备数据对象
        CertiTpl certiTpl = certiTplMapper.selectById(zypxXm.getCertiTplId());
        StudentInfo studentInfo = studentInfoService.selectById(studentId);
        Map<String, Object> data = new HashMap<String, Object>();
        data.put(Constants.CertiTemplateField.name, studentInfo.getName());
        data.put(Constants.CertiTemplateField.sfzh, studentInfo.getSfzh());
        data.put(Constants.CertiTemplateField.certi_number, studentCerti.getCertiNum());
        if (studentInfo.getGender() != null) {
            data.put(Constants.CertiTemplateField.gender, studentInfo.getGender());
        }
        data.put(Constants.CertiTemplateField.photo, studentInfo.getStudentPhoto());
        String validateUrl = portalWebUrl + "/personal/certiValidateQr/"+studentCerti.getId();
        data.put(Constants.CertiTemplateField.qrcode, validateUrl);
        data.put(Constants.CertiTemplateField.xm_name, org.apache.commons.lang3.StringUtils.isNotEmpty(zypxXm.getCertiXmName()) ? zypxXm.getCertiXmName() : zypxXm.getName());
        String time = DateUtils.format(zypxXm.getCertiDate(), "yyyy-M-dd");
        String[] times = org.apache.commons.lang3.StringUtils.split(time, "-");
        data.put(Constants.CertiTemplateField.year, times[0]);
        data.put(Constants.CertiTemplateField.month, times[1]);
        data.put(Constants.CertiTemplateField.day, times[2]);
        //生成证书pdf
        File tempdir = new File(attConfig.getTempdir());
        tempdir = new File(tempdir, "temp_student_certi");
        if (!tempdir.exists()) {
            tempdir.mkdirs();
        }
        String randomName = UUID.randomUUID().toString() ;
        File outPdf = new File(tempdir, randomName + ".pdf");
        this.fillCertiInfo(certiTpl.getTemplatePath(), data, outPdf);
        String path = attConfig.getRootDir()+ "/training/certi/" + xmId;
        String newFileName = randomName + ".pdf";
        String certiUrl = FileHelper.storeFile(path, new FileInputStream(outPdf), newFileName);

        //生成PDF缩略图
        File outImage = new File(tempdir,randomName + ".png");
        BaseUtil.singlePagePdf2Image(outPdf, outImage);
        String newImageFileName = randomName + ".png";
        String certiImgUrl = FileHelper.storeFile(path, new FileInputStream(outImage), newImageFileName);

        studentCerti.setCertiUrl(certiUrl);
        studentCerti.setCertiImgUrl(certiImgUrl);
        studentCerti.setCreateTime(new Date());
        mapper.updateAllColumnById(studentCerti);
        return studentCerti;
    }

    public StudentCerti getStudentCerti(String studentId,String xmId) {
        EntityWrapper<StudentCerti> wrapper = new EntityWrapper();
        wrapper.eq("student_id", studentId);
        wrapper.eq("xm_id", xmId);
        List<StudentCerti> checkCertis = mapper.selectList(wrapper);
        StudentCerti studentCerti = checkCertis.size() > 0 ? checkCertis.get(0) : null;
        return studentCerti;
    }

    private void fillCertiInfo(String templateUrl, Map<String, Object> data, File outPdf) throws IOException, DocumentException {
        PdfReader reader = new PdfReader(templateUrl);
        FileOutputStream out = new FileOutputStream(outPdf);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        PdfStamper stamper = new PdfStamper(reader, bos);
        AcroFields form = stamper.getAcroFields();
        //设置基础信息
        form.setField(Constants.CertiTemplateField.name, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.name, ""));
        form.setField(Constants.CertiTemplateField.sfzh, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.sfzh, ""));
        form.setField(Constants.CertiTemplateField.year, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.year, ""));
        form.setField(Constants.CertiTemplateField.month, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.month, ""));
        form.setField(Constants.CertiTemplateField.day, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.day, ""));

        if (form.getField(Constants.CertiTemplateField.gender) != null) {
            form.setField(Constants.CertiTemplateField.gender, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.gender,""));
        }
        if (form.getField(Constants.CertiTemplateField.certi_number) != null) {
            form.setField(Constants.CertiTemplateField.certi_number, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.certi_number,""));
        }
        if (form.getField(Constants.CertiTemplateField.xm_name) != null) {
            form.setField(Constants.CertiTemplateField.xm_name, BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.xm_name,""));
        }
        //设置学员头像
        if (form.getField(Constants.CertiTemplateField.photo) != null && org.apache.commons.lang3.StringUtils.isNotEmpty(BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.photo))) {
            int pageNo = form.getFieldPositions(Constants.CertiTemplateField.photo).get(0).page;
            Rectangle signRect = form.getFieldPositions(Constants.CertiTemplateField.photo).get(0).position;
            float x = signRect.getLeft();
            float y = signRect.getBottom();
            //根据路径读取图片
            Image image = Image.getInstance(BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.photo));
            //获取图片页面
            PdfContentByte under = stamper.getOverContent(pageNo);
            //图片大小自适应
            image.scaleToFit(signRect.getWidth(), signRect.getHeight());
            //添加图片
            image.setAbsolutePosition(x, y);
            under.addImage(image);
        }
        //设置防伪二维码
        if (form.getField(Constants.CertiTemplateField.qrcode) != null && org.apache.commons.lang3.StringUtils.isNotEmpty(BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.qrcode))) {
            int pageNo = form.getFieldPositions(Constants.CertiTemplateField.qrcode).get(0).page;
            Rectangle signRect = form.getFieldPositions(Constants.CertiTemplateField.qrcode).get(0).position;
            float x = signRect.getLeft();
            float y = signRect.getBottom();
            //绘制二维码
            float width = signRect.getRight() - signRect.getLeft();
            BarcodeQRCode barcodeQRCode = new BarcodeQRCode(BaseUtil.getStringValueFromMap(data, Constants.CertiTemplateField.qrcode), (int)width, (int)width, null);
            //生成二维码图像
            Image qrcodeImage = barcodeQRCode.getImage();
            //绘制在第一页
            PdfContentByte cb = stamper.getOverContent(pageNo);
            //图片大小自适应
            qrcodeImage.scaleToFit(signRect.getWidth(), signRect.getHeight());
            //添加图片
            qrcodeImage.setAbsolutePosition(x, y);
            cb.addImage(qrcodeImage);
        }
        //生成目标PDF文件
        stamper.setFormFlattening(true);
        stamper.close();
        Document doc = new Document();
        PdfCopy copy = new PdfCopy(doc, out);
        doc.open();
        PdfImportedPage importPage = copy.getImportedPage(new PdfReader(bos.toByteArray()), 1);
        copy.addPage(importPage);
        doc.close();
    }

    // 查询
    public Page pageQuery(ZypxStudentCertiMgrParams params) throws IOException, SQLException {
        List<Map<String, Object>> list = mapper.list(params.getCondition(), params);
        params.setRecords(list);
        return params;
    }

    //导出已经审核通过的学员数据
    public void exportPassedStudent(List<Map<String, Object>> bizzypxBmList, OutputStream os) throws Exception {
        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("已审核通过的学员数据", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "培训项目编号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
            ws.addCell(new Label(i, row, "培训项目名称", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "身份证号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
            ws.addCell(new Label(i, row, "证书编号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 20);
        }
        row = 1;
        for (Map<String, Object> map : bizzypxBmList) {
            if (map == null) {
                continue;
            } else {
                int col = 0;
                ws.addCell(new Label(col++, row,
                        BaseUtil.convertNullToEmpty(map.get("serialNumber") != null ? map.get("serialNumber") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row,
                        BaseUtil.convertNullToEmpty(map.get("title") != null ? map.get("title") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(
                        new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
                                OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row,
                        BaseUtil.convertNullToEmpty(map.get("studentSfzh") != null ? map.get("studentSfzh") : ""),
                        OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row,
                        BaseUtil.convertNullToEmpty(map.get("certiNum") != null ? map.get("certiNum").toString() : ""),
                        OfficeToolExcel.getNormolCell()));
                row++;
            }
        }
        wwb.write();
        wwb.close();
        os.flush();
    }

    // 统计分析查询
    public Object statistic(String year, String hostOrgId) {
        return mapper.statistic(year, hostOrgId);
    }

    public Object certiDetail(String id) {
        StudentCerti studentCerti = mapper.selectById(id);
        if (studentCerti == null) {
            throw BizException.withMessage("证书不存在");
        }
        List<Map<String, Object>> list = scoreService.trainingScoreDetails(studentCerti.getStudentId(), studentCerti.getXmId());
        return list.stream().collect(Collectors.groupingBy(x -> x.get("category")));
    }
}
