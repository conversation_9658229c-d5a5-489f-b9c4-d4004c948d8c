package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.Receiver;
import com.xunw.zjxx.module.enums.Status;

import java.io.Serializable;
import java.util.Date;

/**
 * 新闻资讯
 * 通知公告信息表
 */
@TableName("sys_notice")
public class Notice  implements Serializable {

    //主键id
    @TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

    //文章标题
    @TableField("title")
    private String title;

    //公告分类表主键
    @TableField("category_id")
    private String categoryId;

    //同类置顶('0' 否 '1' 是)
    @TableField("is_to_top")
    private String isToTop;

    //文章作者
    @TableField("writer")
    private String writer;

    //访问量
    @TableField("count")
    private Integer count;

    //状态
    @TableField("status")
    private Status status;

    //文章来源('0' 线上 '1' 线下)
    @TableField("source")
    private String source;

	//文章内容
    @TableField("content")
    private String content;

    //缩略图路径
    @TableField("url")
    private String url;

    //是否是外部链接 1是 0否
  	@TableField("is_link")
    private String isLink;

    //链接
	@TableField("link_url")
    private String linkUrl;

    //创建用户id
    @TableField("creator_id")
    private String creatorId;

    //创建时间
    @TableField("create_time")
    private Date createTime;

    //修改用户id
    @TableField("updator_id")
    private String updatorId;

    //修改时间
    @TableField("update_time")
    private Date updateTime;

    //接收对象("0"不限 "1"仅管理员 "2"仅学员)
	@TableField("receiver")
	private Receiver receiver;

	//第三方ID
	@TableField("third_party_id")
	private String thirdPartyId;

	//摘要
	@TableField("summary")
	private String summary;

	//发布时间
	@TableField("publish_time")
	private Date publishTime;

	//主办单位id
	@TableField("host_org_id")
	private String hostOrgId;

	public Notice() {
	}

	public Notice(String id, String title, String categoryId, String isToTop, String writer, Integer count, Status status, String source, String content, String url, String isLink, String linkUrl, String creatorId, Date createTime, Receiver receiver, String thirdPartyId) {
		this.id = id;
		this.title = title;
		this.categoryId = categoryId;
		this.isToTop = isToTop;
		this.writer = writer;
		this.count = count;
		this.status = status;
		this.source = source;
		this.content = content;
		this.url = url;
		this.isLink = isLink;
		this.linkUrl = linkUrl;
		this.creatorId = creatorId;
		this.createTime = createTime;
		this.receiver = receiver;
		this.thirdPartyId = thirdPartyId;
	}

	public Receiver getReceiver() {
		return receiver;
	}

	public void setReceiver(Receiver receiver) {
		this.receiver = receiver;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(String categoryId) {
		this.categoryId = categoryId;
	}

	public String getIsToTop() {
		return isToTop;
	}

	public void setIsToTop(String isToTop) {
		this.isToTop = isToTop;
	}

	public String getWriter() {
		return writer;
	}

	public void setWriter(String writer) {
		this.writer = writer;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(String creatorId) {
		this.creatorId = creatorId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUpdatorId() {
		return updatorId;
	}

	public void setUpdatorId(String updatorId) {
		this.updatorId = updatorId;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getLinkUrl() {
		return linkUrl;
	}

	public void setLinkUrl(String linkUrl) {
		this.linkUrl = linkUrl;
	}

	public String getIsLink() {
		return isLink;
	}

	public void setIsLink(String isLink) {
		this.isLink = isLink;
	}

	public String getThirdPartyId() {
		return thirdPartyId;
	}

	public void setThirdPartyId(String thirdPartyId) {
		this.thirdPartyId = thirdPartyId;
	}

	public String getSummary() {
		return summary;
	}

	public void setSummary(String summary) {
		this.summary = summary;
	}

	public Date getPublishTime() {
		return publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}
}

