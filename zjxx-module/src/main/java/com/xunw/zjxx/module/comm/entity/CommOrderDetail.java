package com.xunw.zjxx.module.comm.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 
 * @TableName comm_order_detail
 */
@TableName(value ="comm_order_detail")
public class CommOrderDetail implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 订单编号
     */
    @TableField(value = "order_id")
    private String orderId;

    /**
     * 费用类型小类
     */
    @TableField(value = "fee_type")
    private String feeType;

    /**
     * 业务表ID
     */
    @TableField(value = "yw_id")
    private String ywId;

    /**
     * 费用金额，单位元
     */
    @TableField(value = "amount")
    private Double amount;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 订单编号
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * 订单编号
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * 费用类型小类
     */
    public String getFeeType() {
        return feeType;
    }

    /**
     * 费用类型小类
     */
    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    /**
     * 业务表ID
     */
    public String getYwId() {
        return ywId;
    }

    /**
     * 业务表ID
     */
    public void setYwId(String ywId) {
        this.ywId = ywId;
    }

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}
}