package com.xunw.zjxx.module.biz.params;

import com.xunw.zjxx.common.base.BaseQueryParams;

public class PaperRepoQueryParams extends BaseQueryParams {

    private String dbId;  //题库
    private String courseId;  //课程
    private String name;  //试卷名称
    private String category;  //试卷分类
    private String status;  //状态
    private String appId;

    public String getDbId() {
        return dbId;
    }

    public void setDbId(String dbId) {
        this.dbId = dbId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }
}
