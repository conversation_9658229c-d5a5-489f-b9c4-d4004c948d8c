package com.xunw.zjxx.module.sys.service;

import java.util.ArrayList;
import java.util.List;

import com.xunw.zjxx.module.sys.params.SystemLogQueryParams;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.utils.DBUtils;
import com.xunw.zjxx.module.core.TomSystemQueue;
import com.xunw.zjxx.module.sys.entity.SystemLog;
import com.xunw.zjxx.module.sys.mapper.SystemLogMapper;

/**
* <AUTHOR>
* @description 针对表【sys_log】的数据库操作Service
* @createDate 2023-08-08 13:32:09
*/
@Service
public class SystemLogService extends BaseCRUDService<SystemLogMapper, SystemLog> {

    @Transactional
    public int persistLogFromQueue(int n) {
        List<SystemLog> list = new ArrayList<>();
        for (int i=0; i<n; i++) {
            SystemLog sysXtrz = TomSystemQueue.SYSTEM_LOG_QUEUE.poll();
            if (sysXtrz == null){
                continue;
            }
            list.add(sysXtrz);
        }
        if (list.size() > 0) {
            DBUtils.insertBatch(list, 10, SystemLog.class);
        }
        return list.size();
    }
}
