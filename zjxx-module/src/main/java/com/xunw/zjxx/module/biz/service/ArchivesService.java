package com.xunw.zjxx.module.biz.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.biz.mapper.ArchivesMapper;
import com.xunw.zjxx.module.biz.params.ArchiveQueryParams;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.service.StudentInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class ArchivesService {

    @Autowired
    private ArchivesMapper archivesMapper;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private StudentBmCourseService studentBmCourseService;
    @Autowired
    private ExamDataService examDataService;
    @Autowired
    private XmService xmService;



    public Page pageQuery(ArchiveQueryParams params) {
        params.setRecords(archivesMapper.pageQuery(params.getCondition(), params));
        return params;
    }

    public Object getArchives(String studentId, String hostOrgId) {
        StudentInfo studentInfo = studentInfoService.selectById(studentId);
        List<Map<String, Object>> xmStudyRecords = xmService.getXmStudyRecords(studentId, hostOrgId);
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> xm : xmStudyRecords) {
            Map<String, Object> map = new HashMap<>();
            map.put("studentName", studentInfo.getName());
            map.put("sfzh", studentInfo.getSfzh());
            map.put("orgName", studentInfo.getOrgName());
            map.put("zw", studentInfo.getZw());
            map.put("zc", studentInfo.getZc());
            map.put("plan", xm);
            map.put("studyRecords", studentBmCourseService.study(studentId, xm.get("id").toString()));
            map.put("examRecords", examDataService.exam(studentId, xm.get("id").toString()));
            result.add(map);
        }
        return result;
    }
}
