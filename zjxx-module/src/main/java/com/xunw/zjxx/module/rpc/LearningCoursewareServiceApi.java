package com.xunw.zjxx.module.rpc;

import com.xunw.zjxx.common.core.ResultMsg;
import com.xunw.zjxx.module.dto.CoursewareVo;
import com.xunw.zjxx.module.enums.Status;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(value = "learning-courseware-service")
public interface LearningCoursewareServiceApi {

    @RequestMapping("/api/study/addLearn")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg addLearn(@RequestParam(required = false, value = "studentId") String studentId,
                       @RequestParam(required = false, value = "batchId") String batchId,
                       @RequestParam(required = false, value = "coursewareId") String coursewareId,
                       @RequestParam(required = false, value = "chapterId") String chapterId,
                       @RequestParam(required = false, value = "lessonId") String lessonId);

    @RequestMapping("/api/study/getCoursewareProgress")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareProgress(@RequestParam(required = false, value = "batchId") String batchId,
                                 @RequestParam(required = false, value = "studentId") String studentId,
                                 @RequestParam(required = false, value = "coursewareId") String coursewareId);

    @RequestMapping("/api/courseware/getEnableCoursewareByCourseId")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getEnableCoursewareByCourseId(@RequestParam(required = false, value = "courseId") String id);

    @RequestMapping("/api/study/addLearnNote")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg addNotes(@RequestParam(required = false, value = "lessonId") String lessonId,
                       @RequestParam(required = false, value = "pointTime") Long point,
                       @RequestParam(required = false, value = "content") String content);

    @RequestMapping("/api/study/getLearnNotes")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getLearnNotes(@RequestParam(required = false, value = "coursewareId") String coursewareId,
                            @RequestParam(required = false, value = "lessonId") String lessonId);

    @RequestMapping("/api/study/deleteLearnNote")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg deleteLearnNote(@RequestParam(required = false, value = "id") String notesId);

    @RequestMapping("/api/study/addLearnComment")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg addLearnComment(@RequestParam(required = false, value = "lessonId") String lessonId,
                              @RequestParam(required = false, value = "content") String content);

    @RequestMapping("/api/study/getLearnComments")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getLearnComments(@RequestParam(required = false, value = "coursewareId") String coursewareId,
                               @RequestParam(required = false, value = "lessonId") String lessonId);

    @RequestMapping("/api/courseware/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareList(@RequestParam(required = false, value = "source") String source,
                             @RequestParam(required = false, value = "courseId") String courseId,
                             @RequestParam(required = false, value = "status") String status,
                             @RequestParam(required = false, value = "isTest") Integer isTest,
                             @RequestParam(required = false, value = "orgIds") String orgIds,
                             @RequestParam(required = false, value = "idNot") String idNot,
                             @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                             @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/courseware/getById")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareGetById(@RequestParam(required = false, value = "id") String id,
                                @RequestParam(required = false, value = "courseId") String courseId);

    @RequestMapping("/api/study/getLearn")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg studyGetLearn(@RequestParam(required = false, value = "studentId") String studentId,
                            @RequestParam(required = false, value = "batchId") String batchId,
                            @RequestParam(required = false, value = "coursewareId") String coursewareId);

    @RequestMapping("/api/courseware/edit")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareEdit(@RequestParam(required = false, value = "id") String id,
                             @RequestParam(required = false, value = "courseId") String courseId,
                             @RequestParam(required = false, value = "lecturer") String lecturer,
                             @RequestParam(required = false, value = "lecturerPhoto") String lecturerPhoto,
                             @RequestParam(required = false, value = "star") Integer star,
                             @RequestParam(required = false, value = "status") Status status,
                             @RequestParam(required = false, value = "source") String source,
                             @RequestParam(required = false, value = "remark") String remark,
                             @RequestParam(required = false, value = "isTest") Integer isTest,
                             @RequestParam(required = false, value = "orgId") String orgId,
                             @RequestParam(required = false, value = "extNum") Integer extNum,
                             @RequestParam(required = false, value = "chapters") String chapters);

    @RequestMapping("/api/courseware/remove")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareRemove(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/courseware/add")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareAdd(@RequestParam(required = false, value = "courseId") String courseId,
                            @RequestParam(required = false, value = "lecturer") String lecturer,
                            @RequestParam(required = false, value = "lecturerPhoto") String lecturerPhoto,//讲师照片
                            @RequestParam(required = false, value = "star") Integer star,
                            @RequestParam(required = false, value = "status") Status status,
                            @RequestParam(required = false, value = "source") String source,
                            @RequestParam(required = false, value = "remark") String remark,
                            @RequestParam(required = false, value = "isTest") Integer isTest,
                            @RequestParam(required = false, value = "orgId") String orgId,
                            @RequestParam(required = false, value = "extNum") Integer extNum,
                            @RequestParam(required = false, value = "chapters") String chapters);

    @RequestMapping(value = "/api/courseware/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResultMsg coursewareImport(@RequestPart(required = false, value = "file") MultipartFile file,
                               @RequestParam(required = false, value = "orgId") String orgId,   // 机构id
                               @RequestParam(required = false, value = "courseId") String courseId,// 课程id
                               @RequestParam(required = false, value = "lecturer") String lecturer,// 课程讲师
                               @RequestParam(required = false, value = "lecturerPhoto") String lecturerPhoto,// 讲师照片
                               @RequestParam(required = false, value = "star") Integer star,// 课程星级
                               @RequestParam(required = false, value = "status") Status status,// 课件状态 不传则默认禁用
                               @RequestParam(required = false, value = "source") String source,// 课件来源
                               @RequestParam(required = false, value = "remark") String remark,// 课程介绍
                               @RequestParam(required = false, value = "isTest") Integer isTest,// 是否开启随堂练习
                               @RequestParam(required = false, value = "extNum") Integer extNum,// 开启随堂练习必传
                               @RequestParam(required = false, value = "id") String id);   // 课件id

    @RequestMapping("/api/courseware/addStudyInfo")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareAddStudyInfo(@RequestParam(required = false, value = "id") String id,
                                     @RequestParam(required = false, value = "filePath") String filePath,
                                     @RequestParam(required = false, value = "fileName") String fileName,
                                     @RequestParam(required = false, value = "fileSize") String fileSize);

    @RequestMapping("/api/courseware/getStudyMaterials")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareGetStudyMaterials(@RequestParam(required = false, value = "coursewareId") String coursewareId,
                                          @RequestParam(required = false, value = "lessonId") String lessonId);

    @RequestMapping("/api/courseware/removeStudyMaterial")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareRemoveStudyMaterial(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/courseware/addExtConfig")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareAddExtConfig(@RequestParam(required = false, value = "id") String id,
                                     @RequestParam(required = false, value = "quesDbIds") String quesDbIds);

    @RequestMapping("/api/courseware/removeExtConfig")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg coursewareRemoveExtConfig(@RequestParam(required = false, value = "id") String id,
                                        @RequestParam(required = false, value = "quesDbId") String quesDbId);

    @RequestMapping("/api/study/addPlayQues")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg studyAddPlayQues(@RequestParam(required = false, value = "recordId") String recordId,
                               @RequestParam(required = false, value = "questionId") String questionId,
                               @RequestParam(required = false, value = "content") String content,
                               @RequestParam(required = false, value = "isCorrect") Integer isCorrect);

}
