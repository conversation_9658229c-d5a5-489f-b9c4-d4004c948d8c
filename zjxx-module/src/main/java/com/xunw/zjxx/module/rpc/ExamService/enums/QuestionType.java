package com.xunw.zjxx.module.rpc.ExamService.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;

public enum QuestionType implements IEnum {

    SINGLECHOICE("单选题","SINGLECHOICE"),
    MULT<PERSON>LECHOICE("多选题","MULTIPLECHOICE"),
    JUDGMENT("判断","JUDGMENT"),
    BLANKFILL("填空","BLANKFILL"),
    ESSAY("问答","ESSAY"),
    MCJST("名词解释题","MCJST"),
    LST("论述题","LST"),
    JDT("简答题","JDT"),
    TT("套题","TT");

    private String name;

    private String id;

    private QuestionType(String name,String id) {
        this.name = name;
        this.id = id;
    }

    public static QuestionType findById(String id) {
        for (QuestionType status : QuestionType.values()) {
            if (status.id.equals(id)) {
                return status;
            }
        }
        return null;
    }

    public static QuestionType findByEnumName(String name) {
        for (QuestionType status : QuestionType.values()) {
            if (status.name().equals(name)) {
                return status;
            }
        }
        return null;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Serializable getValue() {
        return this.name();
    }
}
