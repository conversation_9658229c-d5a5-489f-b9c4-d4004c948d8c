package com.xunw.zjxx.module.biz.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.biz.entity.ExamData;

/**
* <AUTHOR>
* @description 针对表【biz_exam_data(已做考试练习表)】的数据库操作Mapper
* @createDate 2023-08-18 13:33:27
* @Entity com.xunw.zjxx.module.entity.ExamData
*/
public interface ExamDataMapper extends BaseMapper<ExamData> {

    List<Map<String, Object>> getHomeworkList(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> doneList(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> undoList(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> exam(@Param("studentId")String studentId, @Param("xmId")String xmId);
}




