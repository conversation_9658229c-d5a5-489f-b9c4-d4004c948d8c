package  com.xunw.zjxx.module.biz.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.enums.StudyWay;

import java.io.Serializable;
import java.util.Date;

/**
 * 学习计划表
 * @TableName biz_xm
 */
@TableName(value ="biz_xm")
public class Xm implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 年份
     */
    @TableField(value = "years")
    private String years;

    /**
     * 总课时
     */
    @TableField(value = "hours")
    private Integer hours;

    /**
     * 线上 线下
     */
    @TableField(value = "study_way")
    private StudyWay studyWay;

    /**
     * 学习计划开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 学习计划结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * LOGO
     */
    @TableField(value = "logo")
    private String logo;

    /**
     * 是否开启抓拍
     */
    @TableField(value = "is_open_camera")
    private Integer isOpenCamera;

    /**
     * 抓拍间隔时间 单位秒
     */
    @TableField(value = "photo_catch_interval")
    private Integer photoCatchInterval;

    /**
     * 是否开启人脸比对
     */
    @TableField(value = "is_open_verify")
    private Integer isOpenVerify;

    /**
     * 人脸对比阈值
     */
    @TableField(value = "verify_threshold")
    private Integer verifyThreshold;

    /**
     * 结业证书项目名称
     */
    @TableField(value = "certi_xm_name")
    private String certiXmName;

    /**
     * 结业证书模板id
     */
    @TableField(value = "certi_tpl_id")
    private String certiTplId;

    /**
     * 结业证书证芯日期
     */
    @TableField(value = "certi_date")
    private Date certiDate;

    /**
     * 证书编号固定前缀
     */
    @TableField(value = "certi_no_prefix")
    private String certiNoPrefix;

    /**
     * 证书起始编号
     */
    @TableField(value = "certi_start_no")
    private String certiStartNo;

    /**
     * 公需课最低完成学时
     */
    @TableField(value = "gxk_hours")
    private Integer gxkHours;

    /**
     * 专业课最低完成学时
     */
    @TableField(value = "zyk_hours")
    private Integer zykHours;

    /**
     * 开启  关闭
     */
    @TableField(value = "status")
    private Status status;

    /**
     * 创建用户id
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改用户id
     */
    @TableField(value = "updator_id")
    private String updatorId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 修改时间
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public String getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 年份
     */
    public String getYears() {
        return years;
    }

    /**
     * 年份
     */
    public void setYears(String years) {
        this.years = years;
    }

    /**
     * 总课时
     */
    public Integer getHours() {
        return hours;
    }

    /**
     * 总课时
     */
    public void setHours(Integer hours) {
        this.hours = hours;
    }

    /**
     * 线上 线下
     */
    public StudyWay getStudyWay() {
        return studyWay;
    }

    /**
     * 线上 线下
     */
    public void setStudyWay(StudyWay studyWay) {
        this.studyWay = studyWay;
    }

    /**
     * 学习计划开始时间
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 学习计划开始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 学习计划结束时间
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * 学习计划结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * LOGO
     */
    public String getLogo() {
        return logo;
    }

    /**
     * LOGO
     */
    public void setLogo(String logo) {
        this.logo = logo;
    }

    /**
     * 是否开启抓拍
     */
    public Integer getIsOpenCamera() {
        return isOpenCamera;
    }

    /**
     * 是否开启抓拍
     */
    public void setIsOpenCamera(Integer isOpenCamera) {
        this.isOpenCamera = isOpenCamera;
    }

    /**
     * 抓拍间隔时间 单位秒
     */
    public Integer getPhotoCatchInterval() {
        return photoCatchInterval;
    }

    /**
     * 抓拍间隔时间 单位秒
     */
    public void setPhotoCatchInterval(Integer photoCatchInterval) {
        this.photoCatchInterval = photoCatchInterval;
    }

    /**
     * 是否开启人脸比对
     */
    public Integer getIsOpenVerify() {
        return isOpenVerify;
    }

    /**
     * 是否开启人脸比对
     */
    public void setIsOpenVerify(Integer isOpenVerify) {
        this.isOpenVerify = isOpenVerify;
    }

    /**
     * 人脸对比阈值
     */
    public Integer getVerifyThreshold() {
        return verifyThreshold;
    }

    /**
     * 人脸对比阈值
     */
    public void setVerifyThreshold(Integer verifyThreshold) {
        this.verifyThreshold = verifyThreshold;
    }

    /**
     * 结业证书项目名称
     */
    public String getCertiXmName() {
        return certiXmName;
    }

    /**
     * 结业证书项目名称
     */
    public void setCertiXmName(String certiXmName) {
        this.certiXmName = certiXmName;
    }

    /**
     * 结业证书模板id
     */
    public String getCertiTplId() {
        return certiTplId;
    }

    /**
     * 结业证书模板id
     */
    public void setCertiTplId(String certiTplId) {
        this.certiTplId = certiTplId;
    }

    /**
     * 结业证书证芯日期
     */
    public Date getCertiDate() {
        return certiDate;
    }

    /**
     * 结业证书证芯日期
     */
    public void setCertiDate(Date certiDate) {
        this.certiDate = certiDate;
    }

    /**
     * 证书编号固定前缀
     */
    public String getCertiNoPrefix() {
        return certiNoPrefix;
    }

    /**
     * 证书编号固定前缀
     */
    public void setCertiNoPrefix(String certiNoPrefix) {
        this.certiNoPrefix = certiNoPrefix;
    }

    /**
     * 证书起始编号
     */
    public String getCertiStartNo() {
        return certiStartNo;
    }

    /**
     * 证书起始编号
     */
    public void setCertiStartNo(String certiStartNo) {
        this.certiStartNo = certiStartNo;
    }

    public Integer getGxkHours() {
        return gxkHours;
    }

    public void setGxkHours(Integer gxkHours) {
        this.gxkHours = gxkHours;
    }

    public Integer getZykHours() {
        return zykHours;
    }

    public void setZykHours(Integer zykHours) {
        this.zykHours = zykHours;
    }

    /**
     * 开启  关闭
     */
    public Status getStatus() {
        return status;
    }

    /**
     * 开启  关闭
     */
    public void setStatus(Status status) {
        this.status = status;
    }

    /**
     * 创建用户id
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建用户id
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改用户id
     */
    public String getUpdatorId() {
        return updatorId;
    }

    /**
     * 修改用户id
     */
    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    /**
     * 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }
}