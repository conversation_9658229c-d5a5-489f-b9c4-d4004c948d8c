package com.xunw.zjxx.module.dto.reqParams;

import com.baomidou.mybatisplus.annotations.TableField;
import com.xunw.zjxx.module.enums.Receiver;
import com.xunw.zjxx.module.enums.Status;

import java.util.Date;

public class NoticeReq {

    /**
     * 公告id
     */
    private String id;

    /**
     * 类型id
     */
    private String categoryId;

    /**
     * 标题
     */
    private String title;

    /**
     * 是否同类置顶
     */
    private String isToTop;

    /**
     * 状态
     */
    private Status status;

    /**
     * 来源
     */
    private String source;

    /*
     * 是否外部链接
     */
    private String isLink;

    /**
     * 文档链接
     */
    private String linkUrl;

    /**
     * 缩略图
     */
    private String url;

    /**
     * 摘要
     */
    private String summary;

    /**
     *  内容
     */
    private String content;

    /**
     * 作者
     */
    private String writer;

    /**
     * 接收者
     */
    private Receiver receiver;

    /**
     * 发布时间
     */
    private String publishTime;

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIsToTop() {
        return isToTop;
    }

    public void setIsToTop(String isToTop) {
        this.isToTop = isToTop;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getIsLink() {
        return isLink;
    }

    public void setIsLink(String isLink) {
        this.isLink = isLink;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getWriter() {
        return writer;
    }

    public void setWriter(String writer) {
        this.writer = writer;
    }

    public Receiver getReceiver() {
        return receiver;
    }

    public void setReceiver(Receiver receiver) {
        this.receiver = receiver;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public String getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }
}
