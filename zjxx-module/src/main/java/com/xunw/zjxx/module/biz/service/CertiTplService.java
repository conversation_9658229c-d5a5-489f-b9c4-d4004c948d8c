package com.xunw.zjxx.module.biz.service;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.module.biz.entity.CertiTpl;
import com.xunw.zjxx.module.biz.mapper.CertiTplMapper;
import com.xunw.zjxx.module.biz.params.ZypxCertiApproveQueryParams;

/**
* <AUTHOR>
* @description 针对表【biz_certi_tpl】的数据库操作Service
* @createDate 2023-08-08 13:32:09
*/
@Service
public class CertiTplService extends BaseCRUDService<CertiTplMapper, CertiTpl> {

    public Object list(ZypxCertiApproveQueryParams params) {
        EntityWrapper<CertiTpl> wrapper = new EntityWrapper();
        if (StringUtils.isNotEmpty(params.getHostOrgId())) {
            wrapper.eq("host_org_id", params.getHostOrgId());
        }
        wrapper.orderBy("create_time", false);
        return mapper.selectList(wrapper);
    }

}
