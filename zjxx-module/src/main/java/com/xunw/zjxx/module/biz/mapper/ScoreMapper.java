package com.xunw.zjxx.module.biz.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.plugins.Page;

/**
 * 成绩查询
 * <AUTHOR>
 *
 */
public interface ScoreMapper {
	
	List<Map<String, Object>> learningScoreList(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> trainingScoreList(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> trainingScoreDetails(@Param("studentId") String studentId, @Param("xmId") String xmId);

    List<Map<String, Object>> xmScore(@Param("studentId") String studentId, @Param("xmId") String xmId);

}
