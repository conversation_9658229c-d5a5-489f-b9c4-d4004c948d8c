package com.xunw.zjxx.module.base;

import java.io.Serializable;
import java.util.List;
import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.entity.Role;
import com.xunw.zjxx.module.sys.entity.StudentInfo;

public class LoginUser implements Serializable {
	
	private static final long serialVersionUID = -3955214358024549061L;
	
	/**
	 * 以下是公共属性
	 */
	private String id;
	private String username;
	private UserTypeEnum userType;
	private String name;
	private Org org;//所属机构
	private Org hostOrg;//所属主办单位
	
	/**
	 * 管理用户属性
	 */
	private List<Role> roles;//所有角色
	private Role role;//当前角色
	
	/**
	 * 学员用户
	 */
	private StudentInfo studentInfo;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public UserTypeEnum getUserType() {
		return userType;
	}

	public void setUserType(UserTypeEnum userType) {
		this.userType = userType;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Org getOrg() {
		return org;
	}

	public void setOrg(Org org) {
		this.org = org;
	}

	public Org getHostOrg() {
		return hostOrg;
	}

	public void setHostOrg(Org hostOrg) {
		this.hostOrg = hostOrg;
	}

	public List<Role> getRoles() {
		return roles;
	}

	public void setRoles(List<Role> roles) {
		this.roles = roles;
	}

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	public StudentInfo getStudentInfo() {
		return studentInfo;
	}

	public void setStudentInfo(StudentInfo studentInfo) {
		this.studentInfo = studentInfo;
	}
}
