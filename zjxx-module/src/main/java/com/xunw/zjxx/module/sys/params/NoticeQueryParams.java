package com.xunw.zjxx.module.sys.params;

import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.module.enums.Status;

public class NoticeQueryParams extends BaseQueryParams {

    /**
     * 分类id
     */
    private String categoryId;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 状态
     */
    private Status status;

    /**
     * 主办单位id
     */
    private String hostOrgId;

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}
}
