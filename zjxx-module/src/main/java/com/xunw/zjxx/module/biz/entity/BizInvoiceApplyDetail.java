package com.xunw.zjxx.module.biz.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

@TableName(value ="biz_invoice_apply_detail")
public class BizInvoiceApplyDetail implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.INPUT)
    private String id;

    /**
     * 发票申请id
     */
    @TableField(value = "apply_id")
    private String applyId;

    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private String orderId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApplyId() {
        return applyId;
    }

    public void setApplyId(String applyId) {
        this.applyId = applyId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
