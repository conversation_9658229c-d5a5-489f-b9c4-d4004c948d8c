package com.xunw.zjxx.module.biz.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 学员报名课程表 ，当培训项目允许个人选课的时候，此表用于存储学员的报名课程信息。
 * @TableName biz_bm_course_history
 */
@TableName(value ="biz_bm_course_history")
public class BmCourseHistory implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 报名表id
     */
    @TableField(value = "bm_id")
    private String bmId;

    /**
     * 课程设置id
     */
    @TableField(value = "course_setting_id")
    private String courseSettingId;

    /**
     * 报名时间
     */
    @TableField(value = "bm_time")
    private Date bmTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 报名表id
     */
    public String getBmId() {
        return bmId;
    }

    /**
     * 报名表id
     */
    public void setBmId(String bmId) {
        this.bmId = bmId;
    }

    /**
     * 课程设置id
     */
    public String getCourseSettingId() {
        return courseSettingId;
    }

    /**
     * 课程设置id
     */
    public void setCourseSettingId(String courseSettingId) {
        this.courseSettingId = courseSettingId;
    }

    /**
     * 报名时间
     */
    public Date getBmTime() {
        return bmTime;
    }

    /**
     * 报名时间
     */
    public void setBmTime(Date bmTime) {
        this.bmTime = bmTime;
    }
}