package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;

/**
 * 机构关系表
 */
@TableName(value ="sys_org_relation")
public class OrgRelation implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 主机构id
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    /**
     * 合作机构id,只存储顶层机构id
     */
    @TableField(value = "org_id")
    private String orgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    public String getId() {
        return id;
    }

    /**
     * id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 主机构id
     */
    public String getHostOrgId() {
        return hostOrgId;
    }

    /**
     * 主机构id
     */
    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    public String getOrgId() {
        return orgId;
    }
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}