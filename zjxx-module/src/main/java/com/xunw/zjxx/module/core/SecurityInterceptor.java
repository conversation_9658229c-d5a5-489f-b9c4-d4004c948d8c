package com.xunw.zjxx.module.core;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xunw.zjxx.common.core.ResultMsg;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.CacheHelper;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.JWTUtils;
import com.xunw.zjxx.module.base.LoginUser;

/**
 * 全局登录认证拦截器
 * <AUTHOR>
 */
public class SecurityInterceptor extends HandlerInterceptorAdapter {

    private Logger logger = LoggerFactory.getLogger(SecurityInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        logger.info("@@API Request URI:" + request.getRequestURI() + ",Method:" + request.getMethod().toUpperCase());
        HandlerMethod method = (HandlerMethod) handler;
        Operation operation = method.getMethodAnnotation(Operation.class);
        if(operation == null){
            errorReturn(response, BizException.REQUIRED_OPERATION_NNOTATION);
            return false;
        }
        if (!operation.loginRequired()) {
            return true;
        }
        String authToken = request.getHeader(Constants.AUTH.AUTHORIZATION);
        if (StringUtils.isNotEmpty(authToken) && JWTUtils.verify(authToken) && !JWTUtils.isExpired(authToken) && isTokenExists(authToken)) {
            return true;
        }
        errorReturn(response, BizException.LOGIN_REQUIRED);
        return false;
    }

    private void errorReturn(HttpServletResponse response, BizException exception) {
        ResultMsg result = new ResultMsg();
        result.returnError(exception.getCode(), exception.getMessage());
        try {
            ObjectMapper mapper = new ObjectMapper();
            response.setStatus(200);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(mapper.writeValueAsString(result));
        } catch (Exception e) {
            logger.error("errorReturn:",e);
        }
    }

    private boolean isTokenExists(String token) {
    	 LoginUser loginUser = CacheHelper.getCache(Constants.AUTH.USER_TOKEN_CACHE, token);
    	 return loginUser != null;
    }
}
