package com.xunw.zjxx.module.sys.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.module.enums.PayPlatform;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.entity.BankConfig;
import com.xunw.zjxx.module.sys.mapper.BankConfigMapper;

import net.sf.json.JSONObject;

/**
* <AUTHOR>
* @description 针对表【sys_bank_config】的数据库操作Service
* @createDate 2023-08-08 13:32:09
*/
@Service
public class BankConfigService extends BaseCRUDService<BankConfigMapper, BankConfig> {

	/**
	 * 获取收款方配置的支付平台
	 */
	public List<Map<String, Object>> getEnabledPlatform(String bankId){
		EntityWrapper<BankConfig> wrapper = new EntityWrapper<>();
		wrapper.eq("bank_id", bankId);
		wrapper.eq("status", Status.OK);
		List<BankConfig> bankConfigs = mapper.selectList(wrapper);
		List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
		for (BankConfig bankConfig : bankConfigs) {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("payPlat", bankConfig.getPayPlat());
			if (bankConfig.getPayPlat() == PayPlatform.WX) {
				JSONObject setting = JSONObject.fromObject(bankConfig.getPaySettting());
				String app_id = setting.getString("wx_app_id");
				map.put("wxAPPId", app_id);
			}
			list.add(map);
		}
		return list;
	}


	public JSONObject getCCBPayConfig(String bankId) {
		EntityWrapper<BankConfig> wrapper = new EntityWrapper<>();
		wrapper.eq("bank_id", bankId);
		wrapper.eq("pay_plat", PayPlatform.CCB.name());
		List<BankConfig> list = mapper.selectList(wrapper);
		return list.size() > 0 ? JSONObject.fromObject(list.get(0).getPaySettting()) : null;
	}

	public JSONObject getICBCPayConfig(String bankId) {
		EntityWrapper<BankConfig> wrapper = new EntityWrapper<>();
		wrapper.eq("bank_id", bankId);
		wrapper.eq("pay_plat", PayPlatform.ICBC.name());
		List<BankConfig> list = mapper.selectList(wrapper);
		return list.size() > 0 ? JSONObject.fromObject(list.get(0).getPaySettting()) : null;
	}
}
