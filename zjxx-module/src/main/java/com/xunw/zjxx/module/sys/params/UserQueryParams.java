package com.xunw.zjxx.module.sys.params;

import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.enums.RoleEnum;
import com.xunw.zjxx.module.enums.Status;

public class UserQueryParams extends BaseQueryParams {

    private String orgId;

    private String keyword;

    private RoleEnum role;

    private String hostOrgId;

    //当orgId不为空时是否做递归查询，默认不做递归查询
    private String isDeep = Constants.NO;

    private Status status;


    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public RoleEnum getRole() {
        return role;
    }

    public void setRole(RoleEnum role) {
        this.role = role;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getIsDeep() {
        return isDeep;
    }

    public void setIsDeep(String isDeep) {
        this.isDeep = isDeep;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }
}
