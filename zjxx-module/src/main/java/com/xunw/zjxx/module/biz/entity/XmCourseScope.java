package com.xunw.zjxx.module.biz.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;

/**
 * 项目课程范围表
 * @TableName biz_xm_course_scope
 */
@TableName(value ="biz_xm_course_scope")
public class XmCourseScope implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.INPUT)
    @TableField(value = "id")
    private String id;

    /**
     * 项目id
     */
    @TableField(value = "xm_id")
    private String xmId;

    /**
     * 课程分类id
     */
    @TableField(value = "type_id")
    private String typeId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public String getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 项目id
     */
    public String getXmId() {
        return xmId;
    }

    /**
     * 项目id
     */
    public void setXmId(String xmId) {
        this.xmId = xmId;
    }

    /**
     * 课程分类id
     */
    public String getTypeId() {
        return typeId;
    }

    /**
     * 课程分类id
     */
    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        XmCourseScope other = (XmCourseScope) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getXmId() == null ? other.getXmId() == null : this.getXmId().equals(other.getXmId()))
            && (this.getTypeId() == null ? other.getTypeId() == null : this.getTypeId().equals(other.getTypeId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getXmId() == null) ? 0 : getXmId().hashCode());
        result = prime * result + ((getTypeId() == null) ? 0 : getTypeId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", xmId=").append(xmId);
        sb.append(", typeId=").append(typeId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}