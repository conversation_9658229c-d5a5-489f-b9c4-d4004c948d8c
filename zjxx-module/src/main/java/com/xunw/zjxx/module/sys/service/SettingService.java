package com.xunw.zjxx.module.sys.service;

import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.module.enums.SysSettingEnum;
import com.xunw.zjxx.module.sys.entity.Setting;
import com.xunw.zjxx.module.sys.mapper.SettingMapper;

/**
* <AUTHOR>
* @description 针对表【sys_setting(系统设置表)】的数据库操作Service
* @createDate 2023-08-08 13:32:10
*/
@Service
public class SettingService extends BaseCRUDService<SettingMapper, Setting> {
	
	/**
	 * 获取主办单位的配置
	 */
	public String getHostOrgSettingByKey(String hostOrgId, SysSettingEnum key) {
		EntityWrapper<Setting> wrapper = new EntityWrapper<>();
        wrapper.eq("host_org_id", hostOrgId);
        List<Setting> list = mapper.selectList(wrapper);
        return list.size() > 0 && StringUtils.isNotEmpty(list.get(0).getContent()) ? JSONObject.parseObject(
        		list.get(0).getContent()).getString(key.name()) : null;
	}
	
	/**
	 * 获取平台级别的配置
	 */
	public String getPlatformSettingByKey(SysSettingEnum key) {
		EntityWrapper<Setting> wrapper = new EntityWrapper<>();
        wrapper.isNull("host_org_id");
        List<Setting> list = mapper.selectList(wrapper);
        return list.size() > 0 && StringUtils.isNotEmpty(list.get(0).getContent()) ? JSONObject.parseObject(
        		list.get(0).getContent()).getString(key.name()) : null;
	}


}
