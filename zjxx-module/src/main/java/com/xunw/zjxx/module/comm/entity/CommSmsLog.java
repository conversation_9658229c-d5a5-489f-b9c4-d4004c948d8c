package com.xunw.zjxx.module.comm.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 短信记录表
 */
@TableName("comm_sms_log")
public class CommSmsLog implements Serializable {
	
	private static final long serialVersionUID = -354116107759164784L;

	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;
	
	//手机号
	@TableField("mobile")
	private String mobile;
	
	//短信内容
	@TableField("content")
	private String content;
	
	//短信平台返回状态码
	@TableField("status")
	private String status;
	
	//短信平台响应内容
	@TableField("resp")
	private String resp;
	
	@TableField("create_time")
	private Date createTime;
	
	//短信模板代码
	@TableField("tpl_code")
	private String tplCode;
	
	//短信签名
	@TableField("tpl_sign")
	private String tplSign;
		
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getResp() {
		return resp;
	}

	public void setResp(String resp) {
		this.resp = resp;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getTplCode() {
		return tplCode;
	}

	public void setTplCode(String tplCode) {
		this.tplCode = tplCode;
	}

	public String getTplSign() {
		return tplSign;
	}

	public void setTplSign(String tplSign) {
		this.tplSign = tplSign;
	}
}
