package com.xunw.zjxx.module.biz.params;

import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.module.enums.Status;

public class XmQueryParams extends BaseQueryParams {

    /**
     * 年度
     */
    private String years;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 状态
     */
    private Status status;

    /**
     * 主办单位id
     */
    private String hostOrgId;

    public String getYears() {
        return years;
    }

    public void setYears(String years) {
        this.years = years;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }
}
