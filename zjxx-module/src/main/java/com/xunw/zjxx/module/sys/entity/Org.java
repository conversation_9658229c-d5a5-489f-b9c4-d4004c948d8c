package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.OrgType;
import com.xunw.zjxx.module.enums.Status;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构表
 */
@TableName(value ="sys_org")
public class Org implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    @TableField(value = "id")
    private String id;

    /**
     * 机构代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 机构名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 机构类型 ：枚举：主办单位等
     */
    @TableField(value = "org_type")
    private OrgType orgType;

    /**
     * 父机构id
     */
    @TableField(value = "parent_id")
    private String parentId;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Status status;

    /**
     * 描述是否是顶级机构，也就是根节点 , 如0 否   1是
     */
    @TableField(value = "is_parent")
    private String isParent;

    /**
     * 门户域名名称，门户域名前缀
     */
    @TableField(value = "portal_domain")
    private String portalDomain;

    /**
     * 主办单位短信签名
     */
    @TableField(value = "sms_sign")
    private String smsSign;

    /**
     * 主办单位管理端域名
     */
    @TableField(value = "admin_domain")
    private String adminDomain;

    /**
     * 主办单位管理端系统名称
     */
    @TableField(value = "admin_sys_name")
    private String adminSysName;

    /**
     * 主办单位门户系统名称
     */
    @TableField(value = "portal_sys_name")
    private String portalSysName;

    /**
     * 管理端logo
     */
    @TableField(value = "admin_logo")
    private String adminLogo;

    /**
     * 门户系统logo
     */
    @TableField(value = "portal_logo")
    private String portalLogo;

    /**
     * 微信小程序二维码
     */
    @TableField(value = "applet_qr_code")
    private String appletQrCode;

    /**
     * 联系人
     */
    @TableField(value = "contact")
    private String contact;

    /**
     * 联系电话
     */
    @TableField(value = "telephone")
    private String telephone;
    
    /**
     * 创建用户主键
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改用户id
     */
    @TableField(value = "updator_id")
    private String updatorId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
   
    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 机构代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 机构代码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 机构名称
     */
    public String getName() {
        return name;
    }

    /**
     * 机构名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 备注
     */
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
    public OrgType getOrgType() {
        return orgType;
    }
    
    public void setOrgType(OrgType orgType) {
        this.orgType = orgType;
    }
    
    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
    
    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public String getUpdatorId() {
        return updatorId;
    }
    
    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }
    
    public Date getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public Status getStatus() {
        return status;
    }
    
    public void setStatus(Status status) {
        this.status = status;
    }
    
    public String getIsParent() {
        return isParent;
    }
    
    public void setIsParent(String isParent) {
        this.isParent = isParent;
    }
    
    public String getPortalDomain() {
        return portalDomain;
    }
    
    public void setPortalDomain(String portalDomain) {
        this.portalDomain = portalDomain;
    }
    
    public String getSmsSign() {
        return smsSign;
    }
    
    public void setSmsSign(String smsSign) {
        this.smsSign = smsSign;
    }
    
    public String getAdminDomain() {
        return adminDomain;
    }
    
    public void setAdminDomain(String adminDomain) {
        this.adminDomain = adminDomain;
    }
    
    public String getAdminSysName() {
        return adminSysName;
    }
    
    public void setAdminSysName(String adminSysName) {
        this.adminSysName = adminSysName;
    }
    
    public String getPortalSysName() {
        return portalSysName;
    }
    
    public void setPortalSysName(String portalSysName) {
        this.portalSysName = portalSysName;
    }
    
    public String getAdminLogo() {
        return adminLogo;
    }
    
    public void setAdminLogo(String adminLogo) {
        this.adminLogo = adminLogo;
    }

    public String getPortalLogo() {
        return portalLogo;
    }
    
    public void setPortalLogo(String portalLogo) {
        this.portalLogo = portalLogo;
    }

    public String getAppletQrCode() {
        return appletQrCode;
    }
    
    public void setAppletQrCode(String appletQrCode) {
        this.appletQrCode = appletQrCode;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
}