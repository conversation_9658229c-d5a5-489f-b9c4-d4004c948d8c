package com.xunw.zjxx.module.inf.mapper;


import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.entity.Type;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【inf_type】的数据库操作Mapper
 * @createDate 2023-08-08 13:32:09
 */
public interface TypeMapper extends BaseMapper<Type> {

    List<Map<String, Object>> pageQuery(Map<String, Object> condition, Page<?> page);

    List<Course> selectCourseByTypeId(@Param("id")String id);

}




