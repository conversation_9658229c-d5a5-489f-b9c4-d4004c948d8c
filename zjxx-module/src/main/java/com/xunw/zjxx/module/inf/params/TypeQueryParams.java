package com.xunw.zjxx.module.inf.params;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.module.enums.Category;

import java.util.Date;

public class TypeQueryParams extends BaseQueryParams {
    private String id;
    private String name;
    private String code;
    private String parentId;
    private Date createTime;
    private String creatorId;
    private Date updateTime;
    private String updatorId;
    private Integer sort;
    private String category;
    private String hostOrgId;

    public TypeQueryParams() {
    }

    public TypeQueryParams(String id, String name, String code, String parentId, Date createTime, String creatorId, Date updateTime, String updatorId, Integer sort, String category, String hostOrgId) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.parentId = parentId;
        this.createTime = createTime;
        this.creatorId = creatorId;
        this.updateTime = updateTime;
        this.updatorId = updatorId;
        this.sort = sort;
        this.category = category;
        this.hostOrgId = hostOrgId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }
}
