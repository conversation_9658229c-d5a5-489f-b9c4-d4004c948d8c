package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;

/**
 * 角色权限
 *
 */
@TableName("sys_role_resource")
public class RoleResource implements Serializable {

	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;
	
	/**角色ID
	*/
	@TableField("role_id")
	private String roleId;
	
	/**权限ID
	*/
	@TableField("resource_id")
	private String resourceId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getResourceId() {
		return resourceId;
	}

	public void setResourceId(String resourceId) {
		this.resourceId = resourceId;
	}
}
