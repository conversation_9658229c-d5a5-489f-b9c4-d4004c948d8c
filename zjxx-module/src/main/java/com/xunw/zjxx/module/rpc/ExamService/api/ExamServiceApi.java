package com.xunw.zjxx.module.rpc.ExamService.api;

import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.core.ResultMsg;
import com.xunw.zjxx.module.enums.ExamStatus;
import com.xunw.zjxx.module.enums.PaperCategory;
import com.xunw.zjxx.module.rpc.ExamService.enums.QuestionType;
import com.xunw.zjxx.module.rpc.ExamService.enums.Status;
import com.xunw.zjxx.module.rpc.ExamService.params.QuestionCollectQueryParams;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;

@FeignClient(value = "exam-service")
public interface ExamServiceApi {

    @RequestMapping("/api/exam/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examList(@RequestParam(required = false, value = "orgId") String orgId,
                       @RequestParam(required = false, value = "batchId") String batchId,
                       @RequestParam(required = false, value = "courseId") String courseId,
                       @RequestParam(required = false, value = "courseName") String courseName,
                       @RequestParam(required = false, value = "keyword") String keyword,
                       @RequestParam(required = false, value = "paperId") String paperId,
                       @RequestParam(required = false, value = "category") String category,
                       @RequestParam(required = false, value = "paperName") String paperName,
                       @RequestParam(required = false, value = "studentId") String studentId,
                       @RequestParam(required = false, value = "status") String status,
                       @RequestParam(required = false, value = "minScore") Integer minScore,
                       @RequestParam(required = false, value = "maxScore") Integer maxScore,
                       @RequestParam(required = false, value = "bizType") String bizType,
                       @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                       @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/exam/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examList(@RequestParam(required = false, value = "paperId") String paperId,
                       @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                       @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/exam/getById")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examGetById(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/exam/revisereset")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg reviseReset(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("/api/exam/delete")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examDelete(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("/api/exam/next")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examNext(@RequestParam(required = false, value = "id") String id,
                       @RequestParam(required = false, value = "paperId") String paperId,
                       @RequestParam(required = false, value = "status") ExamStatus status);

    @RequestMapping("/api/exam/revise")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examRevise(@RequestParam(required = false, value = "id") String id,
                         @RequestParam(required = false, value = "questionId") String questionId,
                         @RequestParam(required = false, value = "score") Integer score);

    @RequestMapping("/api/paper/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg paperList(@RequestParam(required = false, value = "name") String name,
                        @RequestParam(required = false, value = "category") String category,
                        @RequestParam(required = false, value = "orgIds") String orgIds,
                        @RequestParam(required = false, value = "status") String status,
                        @RequestParam(required = false, value = "courseId") String courseId,
                        @RequestParam(required = false, value = "isObject") Integer isObject,
                        @RequestParam(required = false, value = "isSimulate") Integer isSimulate,
                        @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                        @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/paper/getById")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg paperGetById(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/paper/update")
    @Headers({"Content-Type=application/json"})
    ResultMsg paperUpdate(@RequestBody JSONObject json);

    @RequestMapping("/api/paper/delete")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg paperDelete(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("/api/paper/package")
    @Headers({"Content-Type=application/json"})
    ResultMsg paperPackage(@RequestBody JSONObject json);

    @RequestMapping("/api/paper/addAndAuto")
    @Headers({"Content-Type=application/json"})
    ResultMsg paperAddAndAuto(@RequestBody JSONObject json);

    @RequestMapping("/api/paper/add")
    @Headers({"Content-Type=application/json"})
    ResultMsg paperAdd(@RequestBody JSONObject json);

    @RequestMapping("/api/question/add")
    @Headers({"Content-Type=application/json"})
    ResultMsg questionAdd(@RequestBody JSONObject json);

    @RequestMapping("/api/question/edit")
    @Headers({"Content-Type=application/json"})
    ResultMsg questionEdit(@RequestBody JSONObject json);

    @RequestMapping("/api/question/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionList(@RequestParam(required = false, value = "courseId") String courseId,
                           @RequestParam(required = false, value = "courseName") String courseName,
                           @RequestParam(required = false, value = "realType") String realType,
                           @RequestParam(required = false, value = "dbId") String dbId,
                           @RequestParam(required = false, value = "difficulty") String difficulty,
                           @RequestParam(required = false, value = "content") String content,
                           @RequestParam(required = false, value = "type") QuestionType type,
                           @RequestParam(required = false, value = "status") Status status,
                           @RequestParam(required = false, value = "isOldPaper") String isOldPaper,
                           @RequestParam(required = false, value = "source") String source,
                           @RequestParam(required = false, value = "parentId") String parentId,
                           @RequestParam(required = false, value = "answer") String answer,
                           @RequestParam(required = false, value = "createdBy") String createdBy,
                           @RequestParam(required = false, value = "createdTime") Date createdTime,
                           @RequestParam(required = false, value = "lastModifiedBy") String lastModifiedBy,
                           @RequestParam(required = false, value = "lastModifiedTime") Date lastModifiedTime,
                           @RequestParam(required = false, value = "isCorrected") Integer isCorrected,
                           @RequestParam(required = false, value = "no") Integer no,
                           @RequestParam(required = false, value = "orgId") String orgId,
                           @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                           @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/question/preview")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionGetQuesDetailsById(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/question/delete")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionDeleteById(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("/api/question/updateStatus")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionUpdateStatus(@RequestParam(required = false, value = "ids") String ids,
                                   @RequestParam(required = false, value = "status") String status);

    @RequestMapping(value = "/api/question/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResultMsg questionImport(@RequestPart(value = "file", required = false) MultipartFile file,
                             @RequestParam(required = false, value = "dbId") String dbId,
                             @RequestParam(required = false, value = "parentId") String parentId,
                             @RequestParam(required = false, value = "isOldPaper") String isOldPaper,
                             @RequestParam(required = false, value = "type") String type,
                             @RequestParam(required = false, value = "status") Status status);

    @RequestMapping("/api/question/count")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionCount(@RequestParam(required = false, value = "courseId") String courseId,
                            @RequestParam(required = false, value = "courseName") String courseName,
                            @RequestParam(required = false, value = "realType") String realType,
                            @RequestParam(required = false, value = "dbId") String dbId,
                            @RequestParam(required = false, value = "difficulty") String difficulty,
                            @RequestParam(required = false, value = "content") String content,
                            @RequestParam(required = false, value = "type") QuestionType type,
                            @RequestParam(required = false, value = "status") Status status,
                            @RequestParam(required = false, value = "isOldPaper") String isOldPaper,
                            @RequestParam(required = false, value = "source") String source,
                            @RequestParam(required = false, value = "parentId") String parentId,
                            @RequestParam(required = false, value = "answer") String answer,
                            @RequestParam(required = false, value = "createdBy") String createdBy,
                            @RequestParam(required = false, value = "createdTime") Date createdTime,
                            @RequestParam(required = false, value = "lastModifiedBy") String lastModifiedBy,
                            @RequestParam(required = false, value = "lastModifiedTime") Date lastModifiedTime,
                            @RequestParam(required = false, value = "isCorrected") Integer isCorrected,
                            @RequestParam(required = false, value = "no") Integer no,
                            @RequestParam(required = false, value = "orgId") String orgId,
                            @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                            @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/question/getChildByPid")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionGetChildByPid(@RequestParam(required = false, value = "parentId") String parentId);

    @RequestMapping("/api/question/selectRealType")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionSelectRealType(@RequestParam(required = false, value = "orgId") String orgId);

    @RequestMapping("/api/questionDb/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionDbList(@RequestParam(required = false, value = "name") String name,
                             @RequestParam(required = false, value = "status") String status,
                             @RequestParam(required = false, value = "isOldPaper") String isOldPaper,
                             @RequestParam(required = false, value = "courseId") String courseId,
                             @RequestParam(required = false, value = "courseName") String courseName,
                             @RequestParam(required = false, value = "orgId") String orgId,
                             @RequestParam(required = false, value = "orgIds") String orgIds,//多个值逗号分开  田军
                             @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                             @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/questionDb/add")
    @Headers({"Content-Type=application/json"})
    ResultMsg questionDbAdd(@RequestBody JSONObject json);

    @RequestMapping("/api/questionDb/delete")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionDbDelete(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("/api/questionDb/preview")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionDbPreview(@RequestParam(required = false, value = "id") String id);

    /**
     * 题库试题预览
     */
    @RequestMapping("/api/questionDb/questionPreview")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionPreview(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/questionDb/update")
    @Headers({"Content-Type=application/json"})
    ResultMsg questionDbUpdate(@RequestBody JSONObject json);

    @RequestMapping("/api/questionDb/select")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg questionDbSelect(@RequestParam(required = false, value = "id") String name,
                               @RequestParam(required = false, value = "status") String status,
                               @RequestParam(required = false, value = "isOldPaper") String isOldPaper,
                               @RequestParam(required = false, value = "courseId") String courseId,
                               @RequestParam(required = false, value = "courseName") String courseName,
                               @RequestParam(required = false, value = "orgId") String orgId,
                               @RequestParam(required = false, value = "orgIds") String orgIds,
                               @RequestParam(required = false, defaultValue = "1", value = "current") Integer current,
                               @RequestParam(required = false, defaultValue = "10", value = "size") Integer size);

    @RequestMapping("/api/student/exam/start")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getExamStart(@RequestParam(required = false, value = "examDataId") String examDataId,
                           @RequestParam(required = false, value = "paperId") String paperId,
                           @RequestParam(required = false, value = "shortestSubmitTime") String shortestSubmitTime);

    @RequestMapping("/api/student/exam/detail")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getExamDetail(@RequestParam(required = false, value = "id") String examDataId,
                            @RequestParam("paperId") String paperId);

    @RequestMapping("/api/student/autoSubmit")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg autoSubmit(@RequestParam(required = false, value = "id") String examDataId);

    @RequestMapping("/api/student/exam/uploadCard")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getExamUploadCard(@RequestParam(required = false, value = "id") String examDataId,
                                @RequestParam(required = false, value = "url") String url);

    @RequestMapping("/api/student/exam/deleteCard")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg examDeleteCard(@RequestParam(required = false, value = "id") String examDataId,
                             @RequestParam(required = false, value = "url") String url);

    @RequestMapping("/api/student/examSubmit")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getExamSubmit(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/student/redoExam")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getRedoExam(@RequestParam(required = false, value = "id") String id);

    @RequestMapping("/api/questionDb/getRandomObjectiveQuestion")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getQuestionDbGetRandomObjectiveQuestion(@RequestParam(required = false, value = "dbIds") String quesDbIds);

    @RequestMapping("/api/student/paper/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getStudentExamList(@RequestParam(required = false, value = "courseIds") String courseIds,
                                 @RequestParam(required = false, value = "isDone") String isDone,
                                 @RequestParam(required = false, value = "category") PaperCategory category,
                                 @RequestParam(required = false, value = "size") Integer size);

    /**
     * 用于查询模拟考试
     */
    @RequestMapping("/api/student/paper/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg getStudentExamListBySimulate(@RequestParam(required = false, value = "courseIds") String courseIds,
                                           @RequestParam(required = false, value = "isDone") String isDone,
                                           @RequestParam(required = false, value = "isSimulate") Integer isSimulate,
                                           @RequestParam(required = false, value = "category") PaperCategory category,
                                           @RequestParam(required = false, value = "size") Integer size);

    @RequestMapping("/api/student/examSave")
    @Headers({"Content-Type=application/json"})
    ResultMsg examSave(@RequestBody JSONObject json);

    @RequestMapping("/api/question/collect/list")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg collectList(QuestionCollectQueryParams params);

    @RequestMapping("/api/question/collect/add")
    @Headers({"Content-Type=application/json"})
    ResultMsg collectAdd(@RequestBody JSONObject json);

    @RequestMapping("/api/question/collect/delete")
    @Headers({"Content-Type=multipart/form-data"})
    ResultMsg collectDetele(@RequestParam(required = false, value = "ids") String ids);

    @RequestMapping("/api/question/collect/question/practise")
    @Headers({"Content-Type=application/json"})
    ResultMsg collectQuestionPractise(JSONObject json);
}
