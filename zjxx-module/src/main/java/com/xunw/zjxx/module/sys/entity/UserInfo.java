package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.Education;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName sys_user_info
 */
@TableName(value ="sys_user_info")
public class UserInfo implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 系统用户ID
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 学历
     */
    @TableField(value = "education")
    private Education education;

    /**
     * 岗位
     */
    @TableField(value = "gw")
    private String gw;

    /**
     * 职务
     */
    @TableField(value = "zw")
    private String zw;

    /**
     * 职称
     */
    @TableField(value = "zc")
    private String zc;

    /**
     * 简介
     */
    @TableField(value = "brief")
    private String brief;

    /**
     * 工作照片
     */
    @TableField(value = "photo")
    private String photo;

    /**
     * 创建人
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 创建时间 
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 最后修改用户ID
     */
    @TableField(value = "updator_id")
    private String updatorId;

    /**
     * 最后修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 性别
     */
    @TableField(value = "gender")
    private String gender;

    /**
     * 工作单位
     */
    @TableField(value = "work_unit")
    private String workUnit;

    /**
     * 研究方向
     */
    @TableField(value = "study_direction")
    private String studyDirection;

    /**
     * 主讲课程
     */
    @TableField(value = "courses")
    private String courses;

    /**
     * 办公电话
     */
    @TableField(value = "office_tel")
    private String officeTel;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 是否外聘
     */
    @TableField(value = "is_out")
    private String isOut;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public String getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 系统用户ID
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 系统用户ID
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 学历
     */
    public Education getEducation() {
        return education;
    }

    /**
     * 学历
     */
    public void setEducation(Education education) {
        this.education = education;
    }

    /**
     * 岗位
     */
    public String getGw() {
        return gw;
    }

    /**
     * 岗位
     */
    public void setGw(String gw) {
        this.gw = gw;
    }

    /**
     * 职务
     */
    public String getZw() {
        return zw;
    }

    /**
     * 职务
     */
    public void setZw(String zw) {
        this.zw = zw;
    }

    /**
     * 职称
     */
    public String getZc() {
        return zc;
    }

    /**
     * 职称
     */
    public void setZc(String zc) {
        this.zc = zc;
    }

    /**
     * 简介
     */
    public String getBrief() {
        return brief;
    }

    /**
     * 简介
     */
    public void setBrief(String brief) {
        this.brief = brief;
    }

    /**
     * 工作照片
     */
    public String getPhoto() {
        return photo;
    }

    /**
     * 工作照片
     */
    public void setPhoto(String photo) {
        this.photo = photo;
    }

    /**
     * 创建人
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建人
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 创建时间 
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间 
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 最后修改用户ID
     */
    public String getUpdatorId() {
        return updatorId;
    }

    /**
     * 最后修改用户ID
     */
    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    /**
     * 最后修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 最后修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 性别
     */
    public String getGender() {
        return gender;
    }

    /**
     * 性别
     */
    public void setGender(String gender) {
        this.gender = gender;
    }

    /**
     * 工作单位
     */
    public String getWorkUnit() {
        return workUnit;
    }

    /**
     * 工作单位
     */
    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    /**
     * 研究方向
     */
    public String getStudyDirection() {
        return studyDirection;
    }

    /**
     * 研究方向
     */
    public void setStudyDirection(String studyDirection) {
        this.studyDirection = studyDirection;
    }

    /**
     * 主讲课程
     */
    public String getCourses() {
        return courses;
    }

    /**
     * 主讲课程
     */
    public void setCourses(String courses) {
        this.courses = courses;
    }

    /**
     * 办公电话
     */
    public String getOfficeTel() {
        return officeTel;
    }

    /**
     * 办公电话
     */
    public void setOfficeTel(String officeTel) {
        this.officeTel = officeTel;
    }

    /**
     * 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getIsOut() {
        return isOut;
    }

    public void setIsOut(String isOut) {
        this.isOut = isOut;
    }
}