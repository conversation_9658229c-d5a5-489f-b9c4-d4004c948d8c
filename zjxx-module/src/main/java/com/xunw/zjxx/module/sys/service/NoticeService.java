package com.xunw.zjxx.module.sys.service;


import java.util.Date;

import com.xunw.zjxx.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.dto.reqParams.NoticeReq;
import com.xunw.zjxx.module.sys.entity.Notice;
import com.xunw.zjxx.module.sys.mapper.NoticeMapper;
import com.xunw.zjxx.module.sys.params.NoticeQueryParams;

/**
* <AUTHOR>
* @description 针对表【sys_bank】的数据库操作Service
* @createDate 2023-08-08 13:32:09
*/
@Service
public class NoticeService extends BaseCRUDService<NoticeMapper, Notice> {

    public Page pageQuery(NoticeQueryParams params) {
        params.setRecords(mapper.pageQuery(params.getCondition(), params));
        return params;
    }

    public void add(NoticeReq req, String userId, String hostOrgId) {
        Notice notice = new Notice();
        notice.setId(BaseUtil.generateId());
        notice.setTitle(req.getTitle());
        notice.setCategoryId(req.getCategoryId());
        notice.setIsToTop(req.getIsToTop());
        notice.setStatus(req.getStatus());
        notice.setSource(req.getSource());
        notice.setIsLink(req.getIsLink());
        notice.setLinkUrl(req.getLinkUrl());
        notice.setUrl(req.getUrl());
        notice.setContent(req.getContent());
        notice.setCount(0);
        notice.setWriter(req.getWriter());
        notice.setReceiver(req.getReceiver());
        notice.setSummary(req.getSummary());
        notice.setCreatorId(userId);
        notice.setCreateTime(new Date());
        notice.setHostOrgId(hostOrgId);
        notice.setPublishTime(DateUtils.parse(req.getPublishTime()));
        mapper.insert(notice);
    }

    public void edit(NoticeReq req, String userId) {
        Notice notice = mapper.selectById(req.getId());
        if (notice == null) {
            throw BizException.withMessage("该公告不存在");
        }
        notice.setTitle(req.getTitle());
        notice.setCategoryId(req.getCategoryId());
        notice.setIsToTop(req.getIsToTop());
        notice.setStatus(req.getStatus());
        notice.setSource(req.getSource());
        notice.setIsLink(req.getIsLink());
        notice.setLinkUrl(req.getLinkUrl());
        notice.setUrl(req.getUrl());
        notice.setContent(req.getContent());
        notice.setWriter(req.getWriter());
        notice.setReceiver(req.getReceiver());
        notice.setContent(req.getContent());
        notice.setSummary(req.getSummary());
        notice.setUpdatorId(userId);
        notice.setUpdateTime(new Date());
        notice.setPublishTime(DateUtils.parse(req.getPublishTime()));
        mapper.updateById(notice);
    }
}
