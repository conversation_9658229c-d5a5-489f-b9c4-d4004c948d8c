package com.xunw.zjxx.module.inf.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.OfficeToolExcel;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.biz.entity.CoursewareProgress;
import com.xunw.zjxx.module.biz.entity.LessonPractice;
import com.xunw.zjxx.module.biz.entity.StudentBmCourse;
import com.xunw.zjxx.module.biz.mapper.CoursewareProgressMapper;
import com.xunw.zjxx.module.biz.mapper.LessonPracticeMapper;
import com.xunw.zjxx.module.biz.mapper.StudentBmCourseMapper;
import com.xunw.zjxx.module.biz.service.CheckService;
import com.xunw.zjxx.module.dto.ChapterDto;
import com.xunw.zjxx.module.enums.PaperCategory;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.entity.Type;
import com.xunw.zjxx.module.inf.mapper.CourseMapper;
import com.xunw.zjxx.module.inf.params.CourseQueryParams;
import com.xunw.zjxx.module.inf.vo.*;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.rpc.LearningCoursewareServiceApi;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.service.StudentInfoService;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【inf_course】的数据库操作Service
 * @createDate 2023-08-08 13:32:09
 */
@Service
public class CourseService extends BaseCRUDService<CourseMapper, Course> {
	@Autowired
	private LearningCoursewareServiceApi learningCoursewareServiceApi;
	@Autowired
	private CoursewareProgressMapper coursewareProgressMapper;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private StudentBmCourseMapper studentBmCourseMapper;
	@Autowired
	private TypeService typeService;
	@Autowired
	private CourseMapper courseMapper;
	@Autowired
	private AttConfig attConfig;
	@Autowired
	private CheckService checkService;
    @Autowired
    private LessonPracticeMapper lessonPracticeMapper;
    @Autowired
    private ExamServiceApi examServiceApi;

	public Object pageQuery(CourseQueryParams params) {
		return params.setRecords(mapper.pageQuery(params.getCondition(), params));
	}

	@GlobalTransactional
	@Transactional
	public void updateStudyTime(LoginUser loginUser, String courseId, String coursewareId, String chapterId, String lessonId,
			String accessToken) throws Exception {
		// 更新学习进度
		learningCoursewareServiceApi.addLearn(loginUser.getId(), Constants.UPDATE_STUDY_TIME_BATCH_ID, coursewareId, chapterId, lessonId);
		//更新本地学习进度百分比
		JSONObject data = (JSONObject) learningCoursewareServiceApi.coursewareProgress(Constants.UPDATE_STUDY_TIME_BATCH_ID, loginUser.getId(), coursewareId).getData();
		Optional<CoursewareProgress> coursewareProgress = coursewareProgressMapper.selectList(new EntityWrapper<CoursewareProgress>()
				.eq("student_id", loginUser.getId())
				.eq("courseware_id", coursewareId)
				.eq("course_id", courseId)).stream().findFirst();
		if (coursewareProgress.isPresent()) {
			CoursewareProgress progress = coursewareProgress.get();
			progress.setProgress(data.getInteger("progress"));
			progress.setLastModifyTime(data.getDate("lastModifyTime"));
			coursewareProgressMapper.updateById(progress);
		} else {
			CoursewareProgress progress = new CoursewareProgress();
			progress.setId(data.getString("id"));
			progress.setCourseId(courseId);
			progress.setCoursewareId(coursewareId);
			progress.setStudentId(loginUser.getId());
			progress.setProgress(data.getInteger("progress"));
			progress.setLastModifyTime(data.getDate("lastModifyTime"));
			coursewareProgressMapper.insert(progress);
		}
	}

	@GlobalTransactional
	@Transactional
	public Object addNotes(String accessToken, String lessonId, String content, Long point) {
		// 保存笔记
		learningCoursewareServiceApi.addNotes(lessonId,point,content);
		List<Map> records = JSON.parseArray(JSONObject.parseObject(JSON.toJSONString(learningCoursewareServiceApi.getLearnNotes(null,lessonId))).getString("data"), Map.class);
		if (records != null && records.size() > 0) {
			records.forEach(x -> {
				long pointTime = Long.parseLong(x.get("pointTime").toString());
				String time = TimeUnit.SECONDS.toHours(pointTime) + "时" + TimeUnit.SECONDS.toMinutes(pointTime) % 60 + "分" + pointTime % 60 + "秒";
				x.put("point", time);
				x.remove("time");
			});
		}
		return records;
	}

	@GlobalTransactional
	@Transactional
	public Object delNotes(String accessToken, String lessonId, String notesId) throws Exception {
		// 删除笔记
		learningCoursewareServiceApi.deleteLearnNote(notesId);
		// 返回笔记
		List<Map> records = JSON.parseArray(JSONObject.parseObject(JSON.toJSONString(learningCoursewareServiceApi.getLearnNotes(null,lessonId))).getString("data"), Map.class);
		if (records != null && records.size() > 0) {
			records.forEach(x -> {
				long pointTime = Long.parseLong(x.get("pointTime").toString());
				String time = TimeUnit.SECONDS.toHours(pointTime) + "时" + TimeUnit.SECONDS.toMinutes(pointTime) % 60 + "分" + TimeUnit.SECONDS.toSeconds(pointTime) % 60 + "秒";
				x.put("point", time);
				x.remove("time");
			});
		}
		return records;
	}

	@GlobalTransactional
	@Transactional
	public Object addComments(String accessToken, String lessonId, String content) throws Exception {
		// 保存评论
		learningCoursewareServiceApi.addLearnComment(lessonId,content);
		// 返回评论
		List<Map> records = JSON.parseArray(JSONObject.parseObject(JSON.toJSONString(learningCoursewareServiceApi.getLearnComments(null,lessonId))).getString("data"), Map.class);
		if (records != null && records.size() > 0) {
			records.forEach(x -> {
				x.put("point", x.get("time"));
				EntityWrapper<StudentInfo> wrap = new EntityWrapper<>();
				wrap.eq("id", x.get("studentId"));
				List<StudentInfo> studentList = studentInfoService.selectList(wrap);
				if (studentList != null && studentList.size() > 0) {
					x.put("name", studentList.get(0).getName());
				}
			});
		}
		return records;
	}

	/**
	 * 根据父类id查询其下的所有课程包含子类的课程
	 */
	public List<Course> getAllCourse(String typeId, String hostOrgId) {
		List<Type> allTypes = typeService.getAllTypes(typeId, hostOrgId);
		List<Course> courses = courseMapper
				.selectList(new EntityWrapper<Course>().eq("status", Status.OK).eq("host_org_id", hostOrgId)
						.in("type_id", allTypes.stream().map(Type::getId).collect(Collectors.toList())));
		return courses;
	}

	public Object selectPageList(CourseQueryParams params) {
		if (StringUtils.isNotEmpty(params.getTypeId())) {
			List<Type> allTypes = typeService.getAllTypes(params.getTypeId(), params.getHostOrgId());
			params.setAllTypes(allTypes);
		}
		params.setRecords(mapper.selectPageList(params.getCondition(), params));
		return params;
	}

	public List<Map<String, Object>> getBmCourseByStudentId(String studentId, String isPayed) {
		return mapper.getBmCourseByStudentId(studentId, isPayed);
	}

	@GlobalTransactional
	@Transactional
	public Object getCourseDetails(String id, String isOnlyStructer, String token, String studentId, String hostOrgId) throws Exception {
		Course course = mapper.selectById(id);
		if (course == null) {
			throw BizException.withMessage("课程不存在");
		}
		Type type = typeService.selectById(course.getTypeId());
		// 获取可用的课件
		List<CourseDetailVo> courseDetailVoList = JSON.parseArray(JSON.toJSONString(learningCoursewareServiceApi.getEnableCoursewareByCourseId(id).getData()), CourseDetailVo.class);
		if (courseDetailVoList == null || courseDetailVoList.size() == 0) {
			return null;
		}
		//当前业务已经限制了一个课程只能够启用一个
		CourseDetailVo courseDetailVo = courseDetailVoList.get(0);
		String kjId = courseDetailVo.getId();
		courseDetailVo.setId(course.getId());
		courseDetailVo.setUrl(course.getLogo());
		courseDetailVo.setHour(course.getHours());
		courseDetailVo.setName(course.getName());
		courseDetailVo.setCoursewareId(kjId);
		courseDetailVo.setTypeName(type == null ? null : type.getName());
		courseDetailVo.setMoney(course.getAmount());

		//如果只需要课件的章节目录结构则直接返回
		if (Constants.YES.equals(isOnlyStructer)) {
			return courseDetailVo;
		}

		CourseDetailVo studyRecords = null;
		//已登录则获取当前学员的学习时长
		if (StringUtils.isNotEmpty(studentId)) {
			Optional<StudentBmCourse> bmCourse = studentBmCourseMapper
					.selectList(new EntityWrapper<StudentBmCourse>().eq("course_id", id).eq("student_id", studentId))
					.stream().findFirst();
			courseDetailVo.setIsBuy(bmCourse.map(StudentBmCourse::getIsPayed).orElse(null));
			courseDetailVo.setIsBm(bmCourse.isPresent()?Constants.YES:Constants.NO);
			// 获取学习记录
			studyRecords = JSON.parseObject(JSON.toJSONString(learningCoursewareServiceApi.studyGetLearn(studentId,Constants.UPDATE_STUDY_TIME_BATCH_ID,kjId))).getJSONObject("data")
					.toJavaObject(CourseDetailVo.class);
		}

		//获取学习资料
		List<FileVo> files = this.getLessonFiles(kjId, null, token);
		//获取课件评论
		List<CommentVo> commentVos = this.getLessonComments(kjId, null, token);
		//获取学员笔记
		List<NoteVo> noteVos = StringUtils.isNotEmpty(studentId) ? this.getLessonNotes(kjId, null, token) : new ArrayList<>();

		// 组合
		for (ChapterVo chapter : courseDetailVo.getChapters()) {
			for (LessonVo lesson : chapter.getLessons()) {
				lesson.setFiles(files.stream().filter(f->f.getLessonId().equals(lesson.getId())).collect(Collectors.toList()));
				lesson.setComments(commentVos.stream().filter(f->f.getLessonId().equals(lesson.getId())).collect(Collectors.toList()));
				lesson.setNotes(noteVos.stream().filter(f->f.getLessonId().equals(lesson.getId())).collect(Collectors.toList()));
				//回填学习记录信息
				if (studyRecords == null || CollectionUtils.isEmpty(studyRecords.getChapters())) {
					continue;
				}
				List<LessonVo> lessons = studyRecords.getChapters().stream().flatMap(x -> x.getLessons().stream())
						.filter(x -> Objects.equals(x.getId(), lesson.getId())).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(lessons)) {
					lesson.setDuration(lessons.get(0).getDuration());
					lesson.setIsFinished(lessons.get(0).getIsFinished());
				}
			}
		}
		// 获取课程练习
		courseDetailVo.setPractices(this.getCoursePartice(id, studentId));
		return courseDetailVo;
	}

	/**
	 * 获取学员的课程练习以及记录
	 */
	private List<PracticeVo> getCoursePartice(String courseId, String studentId) {
		return mapper.getCoursePartice(courseId, studentId);
	}

	/**
	 * 获取课件或者课时的评论（2个参数不能同时为空） 田军2023-09-25
	 */
	private List<CommentVo> getLessonComments(String coursewareId, String lessonId, String token) throws Exception {
		String resp = JSONObject.toJSONString(learningCoursewareServiceApi.getLearnComments(coursewareId,lessonId));
		List<CommentVo> result = JSON.parseArray(JSON.parseObject(resp).getString("data"), CommentVo.class);
		return result;
	}

	/**
	 * 获取学员的课时笔记
	 */
	private List<NoteVo> getLessonNotes(String coursewareId, String lessonId, String token) throws Exception {
		String resp = JSONObject.toJSONString(learningCoursewareServiceApi.getLearnNotes(coursewareId,lessonId));
		JSONArray data = JSON.parseObject(resp).getJSONArray("data");
		List<NoteVo> result = new ArrayList<>();
		for (Object o : data) {
			JSONObject comment = JSON.parseObject(o.toString());
			Long pointTime = comment.getLong("pointTime");
			long l = pointTime % 60;
			String time = TimeUnit.SECONDS.toHours(pointTime) + "时" + TimeUnit.SECONDS.toMinutes(pointTime)%60 + "分" + l + "秒";
			result.add(new NoteVo(comment.getString("content"), time, comment.getString("id"), comment.getString("lessonId")));
		}
		return result;
	}

	@Transactional
	public Course addCourse(Course course) throws Exception {
		course.setId(BaseUtil.generateId2());
		mapper.insert(course);
		return course;
	}

	@Transactional
	public void editCourse(Course course, String token) throws Exception {
		mapper.updateById(course);
	}

	@GlobalTransactional
	@Transactional
	public void deleteCourse(String id, String token) throws Exception {
		//课程删除校验
		checkService.isDeleteCourse(id, token);
		mapper.deleteById(id);
	}

	/**
	 * 获取某一个课件或者某一个课时学习资料（2个参数不可同时为空） 田军 2023-09-25
	 */
	private List<FileVo> getLessonFiles(String coursewareId, String lessonId, String token) throws Exception {
		String resp = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareGetStudyMaterials(coursewareId,lessonId));
		JSONArray data = JSON.parseObject(resp).getJSONArray("data");
		List<FileVo> result = new ArrayList<>();
		for (Object o : data) {
			JSONObject comment = JSON.parseObject(o.toString());
			result.add(new FileVo(
					comment.getString("lessonId"),
					comment.getString("fileName"),
					comment.getString("filePath")));
		}
		return result;
	}

	@GlobalTransactional
	@Transactional
	public Object myCourseList(CourseQueryParams params, String loginUserId, String accessToken) throws Exception {
		List<Map<String, Object>> list = mapper.myCourseList(params.getCondition(), params);// 查询课程列表
		if (list.size() > 0) {
			for (Map map : list) {
				String s = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareList(null, map.get("id").toString(), Status.OK.name(), null, null, null, 1, Integer.MAX_VALUE));
				List<Map> maps = JSON.parseArray(JSONObject.parseObject(s).getJSONObject("data").getString("records"), Map.class);
				if (maps.size() == 0) {
					continue;
				}
				Map<String, Object> data = maps.get(0);
				if (data.isEmpty()) {
					throw BizException.withMessage("当前课程未找到对应的课件");
				}
				map.put("coursewareId", data.get("id"));
				if (data.get("countMin") != null || Integer.valueOf(data.get("countMin").toString()) > 0) {
					map.put("countTime", String.valueOf(data.get("countMin")));// 总时长
				} else {
					throw BizException.withMessage("课件总时长应大于0");
				}
				// 获取学习进度
				String str = JSONObject.toJSONString(learningCoursewareServiceApi.studyGetLearn(loginUserId, Constants.UPDATE_STUDY_TIME_BATCH_ID, String.valueOf(data.get("id"))));
				List<Map> chaptersList = JSON
						.parseArray(JSONObject.parseObject(str).getJSONObject("data").getString("chapters"), Map.class);
				// 遍历每个章节下每个课时的学习时长累加总学习时长
				Integer studyRatio = 0;
				if (chaptersList.size() > 0) {
					int studyTime = 0;
					for (Map chapters : chaptersList) {
						List<Map> lessonsList = (List<Map>) chapters.get("lessons");
						if (lessonsList.size() > 0) {
							for (Map lessons : lessonsList) {
								studyTime += lessons.get("duration") != null ? Integer.parseInt(lessons.get("duration").toString().split("\\.")[0]) : 0;
							}
						}
					}
					studyRatio = studyTime * 100 / Integer.parseInt(map.get("countTime").toString().split("\\.")[0]);// 统计学习进度，
				}
				map.put("studyRatio", studyRatio > 100 ? 100 : studyRatio);
				List<String> lessonIds = chaptersList.stream().flatMap(x -> {
					List<Map> lessons = (List<Map>) x.get("lessons");
					return lessons.stream();
				}).map(l -> l.get("id").toString()).collect(Collectors.toList());
				// 获取作业进度
				String workRatio = "";
				List<LessonPractice> lessonPractices = lessonPracticeMapper.selectList(new EntityWrapper<LessonPractice>().in("lesson_id", lessonIds));
				List<String> paperIds = lessonPractices.stream().map(LessonPractice::getPaperId).collect(Collectors.toList());
				int total = 0;//练习总数
				Integer num = 0;//练习作答数
				List<String> paperIds1 = new ArrayList<>();//开启的练习试卷id
				for (String paperId : paperIds) {
					Object o = examServiceApi.paperGetById(paperId).getData();
					JSONObject paper = JSON.parseObject(JSONObject.toJSONString(o));
					if (paper != null && "ENABLE".equals(paper.getString("status"))) {
						total++;
						paperIds1.add(paperId);
						Object examdata = examServiceApi.examList(params.getHostOrgId(), null, null, null, null, paperId, PaperCategory.HOMEWORK.name(), null, params.getStudentId(), "ENABLE", null, null, null, 1, Integer.MAX_VALUE).getData();
						if (examdata != null) {
							num++;
						}
					}
				}
				workRatio = num + "/" + total;
				map.put("workRatio", workRatio);
			}
		}
		return list;
	}

	/**
	 * 批量导入
	 */
	@Transactional
	public Map<String, Object> importCourse(MultipartFile file, String token, String currentHostOrgId, String typeId) throws Exception {
		if (file == null) {
			throw BizException.withMessage("请上传文件");
		}
		if (org.apache.commons.lang3.StringUtils.isEmpty(attConfig.getTempdir())) {
			throw BizException.withMessage("系统参数中没有配置上传文件的临时目录");
		}
		File tempdir = new File(attConfig.getTempdir());
		tempdir = new File(tempdir, "course_imp");
		if (!tempdir.exists()) {
			tempdir.mkdirs();
		}
		File target = new File(tempdir,
				UUID.randomUUID().toString() + "." + BaseUtil.getFileExtension(file.getOriginalFilename()));
		target.createNewFile();
		FileUtils.copyInputStreamToFile(file.getInputStream(), target);
		if (!target.exists()) {
			throw BizException.withMessage("文件不存在," + target.getAbsolutePath());
		}
		StringBuffer log = new StringBuffer();
		List<Map<String, String>> list = OfficeToolExcel.readExcel(target,
				new String[] { "name", "tag" });
		if (list == null || list.size() < 1) {
			throw BizException.withMessage("导入文件为空");
		}
		if (list.size() == 1) {
			throw BizException.withMessage("导入文件数据不存在");
		}

		int row = 0;
		int cfCou = 0;
		int cwCou = 0;
		for (Map<String, String> map : list) {
			row++;
			if (row < 2) {
				continue;
			}
			String name = StringUtils.trimToNull(map.get("name"));
			if (StringUtils.isEmpty(name)) {
				log.append("<br>");
				String msg = "第" + row + "行课程名称为空。";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
			EntityWrapper<Course> qw = new EntityWrapper<>();
			qw.eq("name", name);
			qw.eq("host_org_id", currentHostOrgId);
			List<Course> listCourse = mapper.selectList(qw);
			if (listCourse.size() > 0) {
				log.append("<br>");
				String msg = "第" + row + "行课程" + name + "已存在";
				log.append(msg);
				// 标记错误数据+1
				cwCou++;
				continue;
			}
//			String hoursStr = StringUtils.trimToNull(map.get("hours"));
//			if (StringUtils.isEmpty(hoursStr)) {
//				log.append("<br>");
//				String msg = "第" + row + "行课程学时为空。";
//				log.append(msg);
//				// 标记错误数据+1
//				cwCou++;
//				continue;
//			}
//
//			boolean result = hoursStr.matches("(^[0-9]*$)|(^[0-9]*\\.[0-9]+$)");
//			if (result == false) {
//				log.append("<br>");
//				String msg = "第" + row + "行课程学时必须是大于0的数字。";
//				log.append(msg);
//				// 标记错误数据+1
//				cwCou++;
//				continue;
//			}
//			Double hours = Double.valueOf(hoursStr);
//			if (hours <= 0) {
//				log.append("<br>");
//				String msg = "第" + row + "行课程学时必须大于0";
//				log.append(msg);
//				// 标记错误数据+1
//				cwCou++;
//				continue;
//			}
//			String amountStr = StringUtils.trimToNull(map.get("amount"));
//			Double amount = null;
//			if (StringUtils.isNotEmpty(amountStr)) {
//				boolean result2 = amountStr.matches("(^[0-9]*$)|(^[0-9]*\\.[0-9]+$)");
//				if (result2 == false) {
//					log.append("<br>");
//					String msg = "第" + row + "行课程费用必须是大于等于0的数字";
//					log.append(msg);
//					// 标记错误数据+1
//					cwCou++;
//					continue;
//				}
//				amount = Double.valueOf(amountStr);
////				if (amount < 0.01) {
////					log.append("<br>");
////					String msg = "第" + row + "行课程费用必须大于等于0.01";
////					log.append(msg);
////					// 标记错误数据+1
////					cwCou++;
////					continue;
////				}
//			}
			String tag = StringUtils.trimToNull(map.get("tag"));
			Course course = new Course();
			course.setName(name);
			course.setCreateTime(new Date());
			course.setCreatorId(currentHostOrgId);
			course.setTypeId(typeId);
			course.setStatus(Status.OK);
//			course.setAmount(amount);
			course.setTag(tag);
//			course.setHours(hours);
			course.setHostOrgId(currentHostOrgId);
			this.addCourse(course);
		}
		int sucCount = (list.size() - 1) - cfCou - cwCou;
		// 提示总共多少条，导入成功多少条，重复数据多少条，错误数据多少条。能提供错误数据下载并标记错误原因。
		StringBuffer message = new StringBuffer(
				"总共" + (list.size() - 1) + "条，导入成功" + sucCount + "条，重复数据" + cfCou + "条，错误数据" + cwCou + "条。");
		message.append(log);
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", 0);
		result.put("message", message);
		return result;
	}

	/**
	 * 获取课程总时长
	 */
	@GlobalTransactional
	@Transactional
	public double getCourseMinutes(String courseId, String token) throws Exception {
		String s = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareList(null,courseId,Status.OK.name(),null,null,null,1,Integer.MAX_VALUE));
		//获取课件列表
		List<Map> records = JSON.parseArray(JSONObject.parseObject(s).getJSONObject("data").getString("records"), Map.class);
		if (CollectionUtils.isEmpty(records)) {
			return 0;
		}
		List<String> coursewareIds = records.stream().map(x -> String.valueOf(x.get("id"))).collect(Collectors.toList());
		String learn = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareGetById(coursewareIds.get(0),null));
		List<ChapterDto> chapterDtoList = JSONObject.parseArray(JSONObject.parseObject(learn).getJSONObject("data").getString("chapters"), ChapterDto.class);
		return chapterDtoList.stream().flatMap(x -> x.getLessons().stream()).mapToInt(x -> Integer.parseInt(x.getMinutes())).sum();
	}
}
