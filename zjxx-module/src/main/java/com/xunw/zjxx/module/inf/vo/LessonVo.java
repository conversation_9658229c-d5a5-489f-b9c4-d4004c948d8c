package com.xunw.zjxx.module.inf.vo;

import java.util.List;

public class LessonVo {
    private String id;//节id
    private String name;//节名称
    private String sortNumber;//序号
    private String isFinished;//是否学习完
    private String duration; //已观看时长,
    private String minutes;//课时时长
    private String remark;//课时介绍
    private String fileType;//课时文件类型
    private String filePath;//课件地址
    private String quesDbIds;//弹题题库id
    private List<CommentVo> comments;//评论
    private List<NoteVo> notes;//笔记
    private List<FileVo> files;//学习资料

    public LessonVo() {
    }

    public LessonVo(String id, String name, String sortNumber, String isFinished, String duration, String minutes, String remark, String fileType, String filePath, List<CommentVo> comments, List<NoteVo> notes, List<FileVo> files) {
        this.id = id;
        this.name = name;
        this.sortNumber = sortNumber;
        this.isFinished = isFinished;
        this.duration = duration;
        this.minutes = minutes;
        this.remark = remark;
        this.fileType = fileType;
        this.filePath = filePath;
        this.comments = comments;
        this.notes = notes;
        this.files = files;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(String sortNumber) {
        this.sortNumber = sortNumber;
    }

    public String getIsFinished() {
        return isFinished;
    }

    public void setIsFinished(String isFinished) {
        this.isFinished = isFinished;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getMinutes() {
        return minutes;
    }

    public void setMinutes(String minutes) {
        this.minutes = minutes;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public List<CommentVo> getComments() {
        return comments;
    }

    public void setComments(List<CommentVo> comments) {
        this.comments = comments;
    }

    public List<NoteVo> getNotes() {
        return notes;
    }

    public void setNotes(List<NoteVo> notes) {
        this.notes = notes;
    }

    public List<FileVo> getFiles() {
        return files;
    }

    public void setFiles(List<FileVo> files) {
        this.files = files;
    }

    public String getQuesDbIds() {
        return quesDbIds;
    }

    public void setQuesDbIds(String quesDbIds) {
        this.quesDbIds = quesDbIds;
    }
}
