package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.PayPlatform;
import com.xunw.zjxx.module.enums.Status;
import net.sf.json.JSONObject;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName sys_bank_config
 */
@TableName(value ="sys_bank_config")
public class BankConfig implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.INPUT)
    private String id;

    /**
     * 支付平台 ：微信   支付宝
     */
    @TableField(value = "pay_plat")
    private PayPlatform payPlat;

    /**
     * 支付配置
     */
    @TableField(value = "pay_settting")
    private String paySettting;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建用户ID
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 修改用户ID
     */
    @TableField(value = "updator_id")
    private String updatorId;

    /**
     * 收款方id
     */
    @TableField(value = "bank_id")
    private String bankId;

    /**
     * 是否默认
     */
    @TableField(value = "is_default")
    private String isDefault;

    /**
     * 启用OK   禁用BLOCK
     */
    @TableField(value = "status")
    private Status status;

    @TableField(exist = false)
    private JSONObject setting;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 支付平台 ：微信   支付宝
     */
    public PayPlatform getPayPlat() {
        return payPlat;
    }

    /**
     * 支付平台 ：微信   支付宝
     */
    public void setPayPlat(PayPlatform payPlat) {
        this.payPlat = payPlat;
    }

    /**
     * 支付配置
     */
    public String getPaySettting() {
        return paySettting;
    }

    /**
     * 支付配置
     */
    public void setPaySettting(String paySettting) {
        this.paySettting = paySettting;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建用户ID
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建用户ID
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 修改用户ID
     */
    public String getUpdatorId() {
        return updatorId;
    }

    /**
     * 修改用户ID
     */
    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    /**
     * 收款方id
     */
    public String getBankId() {
        return bankId;
    }

    /**
     * 收款方id
     */
    public void setBankId(String bankId) {
        this.bankId = bankId;
    }

    /**
     * 是否默认
     */
    public String getIsDefault() {
        return isDefault;
    }

    /**
     * 是否默认
     */
    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * 启用OK   禁用BLOCK
     */
    public Status getStatus() {
        return status;
    }

    /**
     * 启用OK   禁用BLOCK
     */
    public void setStatus(Status status) {
        this.status = status;
    }

    public JSONObject getSetting() {
        return setting;
    }

    public void setSetting(JSONObject setting) {
        this.setting = setting;
    }
}