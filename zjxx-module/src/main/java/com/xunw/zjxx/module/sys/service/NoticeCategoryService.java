package com.xunw.zjxx.module.sys.service;


import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.dto.NoticeCategoryTree;
import com.xunw.zjxx.module.dto.reqParams.NoticeCategoryReq;
import com.xunw.zjxx.module.sys.entity.NoticeCategory;
import com.xunw.zjxx.module.sys.mapper.NoticeCategoryMapper;
import com.xunw.zjxx.module.sys.params.NoticeCategoryQueryParams;

/**
* <AUTHOR>
* @description 针对表【sys_bank】的数据库操作Service
* @createDate 2023-08-08 13:32:09
*/
@Service
public class NoticeCategoryService extends BaseCRUDService<NoticeCategoryMapper, NoticeCategory> {

    public Page pageQuery(NoticeCategoryQueryParams params) {
        params.setRecords(mapper.pageQuery(params.getCondition(), params));
        return params;
    }

    public void add(NoticeCategoryReq req, String hostOrgId, String  userId) {
        if (StringUtils.isEmpty(req.getName())) {
            throw BizException.withMessage("分类名称不能为空");
        }
        if (req.getSort() == null) {
            throw BizException.withMessage("排序不能为空");
        }
        if (req.getStatus() == null) {
            throw BizException.withMessage("状态不能为空");
        }
        NoticeCategory noticeCategory = new NoticeCategory();
        noticeCategory.setId(BaseUtil.generateId());
        noticeCategory.setName(req.getName());
        noticeCategory.setSort(req.getSort());
        noticeCategory.setStatus(req.getStatus());
        noticeCategory.setRemark(req.getRemark());
        noticeCategory.setParentId(req.getParentId());
        noticeCategory.setCreateTime(new Date());
        noticeCategory.setCreatorId(userId);
        noticeCategory.setHostOrgId(hostOrgId);
        mapper.insert(noticeCategory);
    }

    public void edit(NoticeCategoryReq req, String userId) {
        if (StringUtils.isEmpty(req.getId())) {
            throw BizException.withMessage("id不能为空");
        }
        if (StringUtils.isEmpty(req.getName())) {
            throw BizException.withMessage("分类名称不能为空");
        }
        if (req.getSort() == null) {
            throw BizException.withMessage("排序不能为空");
        }
        if (req.getStatus() == null) {
            throw BizException.withMessage("状态不能为空");
        }
        NoticeCategory noticeCategory = mapper.selectById(req.getId());
        if (noticeCategory == null) {
            throw BizException.withMessage("公告分类不存在");
        }
        noticeCategory.setName(req.getName());
        noticeCategory.setSort(req.getSort());
        noticeCategory.setStatus(req.getStatus());
        noticeCategory.setRemark(req.getRemark());
        noticeCategory.setParentId(req.getParentId());
        noticeCategory.setUpdateTime(new Date());
        noticeCategory.setUpdatorId(userId);
        mapper.updateById(noticeCategory);
    }

    public Object tree(NoticeCategoryQueryParams params, String userId) {
        List<Map<String, Object>> records = this.pageQuery(params).getRecords();
        List<NoticeCategoryTree> trees = records.stream().map(x -> {
            NoticeCategoryTree tree = new NoticeCategoryTree();
            tree.setId(x.get("id").toString());
            tree.setParentId(x.get("parentId") == null?null:x.get("parentId").toString());
            tree.setName(x.get("name").toString());
            tree.setChildren(new ArrayList<>());
            return tree;
        }).collect(Collectors.toList());
        return this.buildTree(trees, params.getParentId());
    }

    public NoticeCategoryTree buildTree(List<NoticeCategoryTree> trees, String parentId) {
        List<NoticeCategoryTree> parents = trees.stream().filter(x -> Objects.equals(x.getId(), parentId)).collect(Collectors.toList());
        List<NoticeCategoryTree> childrens = trees.stream().filter(x -> Objects.equals(x.getParentId(), parentId)).collect(Collectors.toList());
        NoticeCategoryTree parent = new NoticeCategoryTree();
        if (CollectionUtils.isNotEmpty(parents)) {
            parent = parents.get(0);
        }
        NoticeCategoryTree finalParent = parent;
        childrens.forEach(x -> {
            if (CollectionUtils.isEmpty(finalParent.getChildren())) {
                finalParent.setChildren(new ArrayList<>());
            }
            finalParent.getChildren().add(buildTree(trees, x.getId()));
        });
        return finalParent;
    }
}
