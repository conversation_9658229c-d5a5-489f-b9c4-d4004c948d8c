package com.xunw.zjxx.module.biz.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName biz_student_certi
 */
@TableName(value ="biz_student_certi")
public class StudentCerti implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 培训项目id
     */
    @TableField(value = "xm_id")
    private String xmId;

    /**
     * 学生id
     */
    @TableField(value = "student_id")
    private String studentId;

    /**
     * 证书申请时间
     */
    @TableField(value = "apply_time")
    private Date applyTime;

    /**
     * 证书审核用户id
     */
    @TableField(value = "check_user_id")
    private String checkUserId;

    /**
     * 1 待审核  2、审核通过 3、 不通过
     */
    @TableField(value = "check_result")
    private String checkResult;

    /**
     * 证书审核备注
     */
    @TableField(value = "check_remark")
    private String checkRemark;

    /**
     * 证书编号
     */
    @TableField(value = "certi_num")
    private String certiNum;

    /**
     * 证书下载url,pdf
     */
    @TableField(value = "certi_url")
    private String certiUrl;

    /**
     * 证书预览图url
     */
    @TableField(value = "certi_img_url")
    private String certiImgUrl;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建用户id
     */
    @TableField(value = "creator_id")
    private String creatorId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getXmId() {
        return xmId;
    }

    public void setXmId(String xmId) {
        this.xmId = xmId;
    }

    public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public String getCheckUserId() {
        return checkUserId;
    }

    public void setCheckUserId(String checkUserId) {
        this.checkUserId = checkUserId;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public String getCheckRemark() {
        return checkRemark;
    }

    public void setCheckRemark(String checkRemark) {
        this.checkRemark = checkRemark;
    }

    public String getCertiNum() {
        return certiNum;
    }

    public void setCertiNum(String certiNum) {
        this.certiNum = certiNum;
    }

    public String getCertiUrl() {
        return certiUrl;
    }

    public void setCertiUrl(String certiUrl) {
        this.certiUrl = certiUrl;
    }

    public String getCertiImgUrl() {
        return certiImgUrl;
    }

    public void setCertiImgUrl(String certiImgUrl) {
        this.certiImgUrl = certiImgUrl;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }
}