package com.xunw.zjxx.module.inf.service;

import cn.hutool.core.util.CharsetUtil;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.utils.*;
import com.xunw.zjxx.module.enums.MaterialType;
import com.xunw.zjxx.module.inf.dto.MaterialRespDto;
import com.xunw.zjxx.module.inf.entity.Material;
import com.xunw.zjxx.module.inf.mapper.MaterialMapper;
import com.xunw.zjxx.module.inf.params.MaterialQueryParams;
import net.lingala.zip4j.util.Zip4jUtil;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.util.*;

@Service
public class MaterialService extends BaseCRUDService<MaterialMapper, Material> {

    @Autowired
    private MaterialMapper mapper;
    @Autowired
    private AttConfig attConfig;

    public Page pageQuery(MaterialQueryParams params) {
        List<MaterialRespDto> pageResult = mapper.list(params.getCondition(), params);
        params.setRecords(pageResult);
        return params;
    }

    public void batchImport(MultipartFile[] files, String courseId, String hostOrgId, String userId) throws IOException {
        for (MultipartFile multipartFile : files) {
            String filename = multipartFile.getOriginalFilename();
            File tmpFile = new File(attConfig.getTempdir() + "/material/"+System.currentTimeMillis()+"/tmp", filename);
            if (!tmpFile.getParentFile().exists()) {
                tmpFile.getParentFile().mkdirs();
            }
            try {
                multipartFile.transferTo(tmpFile);
                if (tmpFile.getName().endsWith(".zip")) {
                    File tmpFile1 = new File(attConfig.getTempdir() + "/material/"+System.currentTimeMillis()+"/tmp", filename);
                    if (!tmpFile1.getParentFile().exists()) {
                        tmpFile1.getParentFile().mkdirs();
                    }
                    try {
                        cn.hutool.core.util.ZipUtil.unzip(tmpFile, tmpFile1, CharsetUtil.CHARSET_UTF_8);
                        for (File file : tmpFile1.listFiles()) {
                            if (file.isFile()) {
                                this.saveMaterialFile(file, courseId, hostOrgId, userId);
                            }
                        }
                    } finally {
                        FileUtils.forceDelete(tmpFile1);
                    }
                } else {
                    this.saveMaterialFile(tmpFile, courseId, hostOrgId, userId);
                }
            } finally {
                FileUtils.forceDelete(tmpFile);
            }
        }
    }

    private void saveMaterialFile(File file, String courseId, String hostOrgId, String userId) throws IOException {
        String name = file.getName();
        long size = file.length();
        MaterialType type = MaterialType.DOC;
        if (name.endsWith(".mp4")) {
            type = MaterialType.VIDEO;
        }
        Material material = new Material();
        material.setId(BaseUtil.generateId());
        material.setName(name);
        material.setType(type);
        material.setSize(size);
        try (FileInputStream is = new FileInputStream(file)){
            material.setUrl(FileHelper.storeFile(attConfig.getRootDir() + "/material/" + System.currentTimeMillis() + name.substring(0, name.lastIndexOf(".")), is, name));
        }
        material.setCourseId(courseId);
        material.setHostOrgId(hostOrgId);
        material.setCreatorId(userId);
        material.setCreateTime(new Date());
        mapper.insert(material);
    }

        public Object materialList (String name, String courseId, String studentId, Page page){
            page.setRecords(mapper.materialList(name, courseId, studentId, page));
            return page;
        }
    }
