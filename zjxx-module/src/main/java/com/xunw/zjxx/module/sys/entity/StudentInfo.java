package com.xunw.zjxx.module.sys.entity;


import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.CertiType;
import com.xunw.zjxx.module.enums.Education;
import com.xunw.zjxx.module.enums.Gender;
import com.xunw.zjxx.module.enums.PoliticalType;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.enums.ZcEnum;
import com.xunw.zjxx.module.enums.ZwEnum;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * 学员信息表
 */
@TableName(value ="sys_student_info")
public class StudentInfo implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;
    
    /**
     * 学员用户的状态
     */
    @TableField(value = "status")
    private Status status;

    /**
     * 证件类型
     */
    @TableField(value = "certi_type")
    private CertiType certiType;

    /**
     * 身份证号(证件号)
     */
    @TableField(value = "sfzh")
    private String sfzh;

    /**
     * 姓名
     */
    @TableField(value = "name")
    private String name;

    /**
     * 性别
     */
    @TableField(value = "gender")
    private Gender gender;

    /**
     * 政治面貌
     */
    @TableField(value = "political_type")
    private PoliticalType politicalType;

    /**
     * 手机号
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 居住地
     */
    @TableField(value = "address")
    private String address;

    /**
     * 学历 枚举类型 ：
     */
    @TableField(value = "education")
    private Education education;

    /**
     * 身份证相片正面
     */
    @TableField(value = "sfzzm")
    private String sfzzm;

    /**
     * 身份证相片反面
     */
    @TableField(value = "sfzfm")
    private String sfzfm;

    /**
     * 学员登记照
     */
    @TableField(value = "student_photo")
    private String studentPhoto;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 职务级别
     */
    @TableField(value = "zw")
    private ZwEnum zw;

    /**
     * 职称
     */
    @TableField(value = "zc")
    private ZcEnum zc;

    /**
     * 电话 座机
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * QQ
     */
    @TableField(value = "qq")
    private String qq;

    /**
     *
     */
    @TableField(value = "birthday")
    private String birthday;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 发票抬头
     */
    @TableField(value = "invoice_title")
    private String invoiceTitle;

    /**
     * 纳税人识别号
     */
    @TableField(value = "invoice_code")
    private String invoiceCode;

    /**
     * 发票-开户行
     */
    @TableField(value = "invoice_bank")
    private String invoiceBank;

    /**
     * 发票-银行账号
     */
    @TableField(value = "invoice_bank_account")
    private String invoiceBankAccount;

    /**
     * 发票-单位地址
     */
    @TableField(value = "invoice_org_address")
    private String invoiceOrgAddress;

    /**
     * 发票-单位电话
     */
    @TableField(value = "invoice_org_phone")
    private String invoiceOrgPhone;
    
    /**
     * 职称系列 取值为专业课的课程分类
     */
    @TableField(value = "title_series")
    private String titleSeries;

    /**
     * 工作单位
     */
    @TableField(value = "work_unit")
    private String workUnit;

    /**
     * 主办单位ID
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    /**
     * 所属组织机构id
     */
    @TableField(value = "org_id")
    private String orgId;

	/**
	 * 密码
	 */
	@TableField(value = "password")
	private String password;

	/**
	 * openid
	 */
	@TableField(value = "open_id")
	private String openId;

    /**
     * 所属组织机构名字
     */
    @TableField(exist = false)
    private String orgName;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSfzh() {
		return sfzh;
	}

	public void setSfzh(String sfzh) {
		this.sfzh = sfzh;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	public CertiType getCertiType() {
		return certiType;
	}

	public void setCertiType(CertiType certiType) {
		this.certiType = certiType;
	}

	public PoliticalType getPoliticalType() {
		return politicalType;
	}

	public void setPoliticalType(PoliticalType politicalType) {
		this.politicalType = politicalType;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Education getEducation() {
		return education;
	}

	public void setEducation(Education education) {
		this.education = education;
	}

	public String getSfzzm() {
		return sfzzm;
	}

	public void setSfzzm(String sfzzm) {
		this.sfzzm = sfzzm;
	}

	public String getSfzfm() {
		return sfzfm;
	}

	public void setSfzfm(String sfzfm) {
		this.sfzfm = sfzfm;
	}

	public String getStudentPhoto() {
		return studentPhoto;
	}

	public void setStudentPhoto(String studentPhoto) {
		this.studentPhoto = studentPhoto;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public ZwEnum getZw() {
		return zw;
	}

	public void setZw(ZwEnum zw) {
		this.zw = zw;
	}

	public ZcEnum getZc() {
		return zc;
	}

	public void setZc(ZcEnum zc) {
		this.zc = zc;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getQq() {
		return qq;
	}

	public void setQq(String qq) {
		this.qq = qq;
	}

	public String getBirthday() {
		return birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getInvoiceCode() {
		return invoiceCode;
	}

	public void setInvoiceCode(String invoiceCode) {
		this.invoiceCode = invoiceCode;
	}

	public String getInvoiceBank() {
		return invoiceBank;
	}

	public void setInvoiceBank(String invoiceBank) {
		this.invoiceBank = invoiceBank;
	}

	public String getInvoiceBankAccount() {
		return invoiceBankAccount;
	}

	public void setInvoiceBankAccount(String invoiceBankAccount) {
		this.invoiceBankAccount = invoiceBankAccount;
	}

	public String getInvoiceOrgAddress() {
		return invoiceOrgAddress;
	}

	public void setInvoiceOrgAddress(String invoiceOrgAddress) {
		this.invoiceOrgAddress = invoiceOrgAddress;
	}

	public String getInvoiceOrgPhone() {
		return invoiceOrgPhone;
	}

	public void setInvoiceOrgPhone(String invoiceOrgPhone) {
		this.invoiceOrgPhone = invoiceOrgPhone;
	}

	public String getTitleSeries() {
		return titleSeries;
	}

	public void setTitleSeries(String titleSeries) {
		this.titleSeries = titleSeries;
	}

	public String getWorkUnit() {
		return workUnit;
	}

	public void setWorkUnit(String workUnit) {
		this.workUnit = workUnit;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}
}
