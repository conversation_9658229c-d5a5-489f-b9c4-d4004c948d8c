package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统设置表
 * @TableName sys_setting
 */
@TableName(value ="sys_setting")
public class Setting implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 值
     */
    @TableField(value = "content")
    private String content;

    /**
     * 设置时间
     */
    @TableField(value = "time")
    private Date time;

    /**
     * 设置用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 主办单位id
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 值
     */
    public String getContent() {
        return content;
    }

    /**
     * 值
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 设置时间
     */
    public Date getTime() {
        return time;
    }

    /**
     * 设置时间
     */
    public void setTime(Date time) {
        this.time = time;
    }

    /**
     * 设置用户id
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 主办单位id
     */
    public String getHostOrgId() {
        return hostOrgId;
    }

    /**
     * 主办单位id
     */
    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }
}