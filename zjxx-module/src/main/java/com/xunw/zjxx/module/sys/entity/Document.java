package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 存储平台的文档，操作手册等等一些内容
 * @TableName sys_document
 */
@TableName(value ="sys_document")
public class Document implements Serializable {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 文档名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 文档大小,单位字节
     */
    @TableField(value = "file_size")
    private Integer fileSize;

    /**
     * 文档下载地址
     */
    @TableField(value = "url")
    private String url;

    /**
     * 状态枚举，OK ，BLOCK
     */
    @TableField(value = "status")
    private String status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建用户id
     */
    @TableField(value = "creator_id")
    private String creatorId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    public String getId() {
        return id;
    }

    /**
     * 主键id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 文档名称
     */
    public String getName() {
        return name;
    }

    /**
     * 文档名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 文档大小,单位字节
     */
    public Integer getFileSize() {
        return fileSize;
    }

    /**
     * 文档大小,单位字节
     */
    public void setFileSize(Integer fileSize) {
        this.fileSize = fileSize;
    }

    /**
     * 文档下载地址
     */
    public String getUrl() {
        return url;
    }

    /**
     * 文档下载地址
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 状态枚举，OK ，BLOCK
     */
    public String getStatus() {
        return status;
    }

    /**
     * 状态枚举，OK ，BLOCK
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 创建用户id
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     * 创建用户id
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }
}