package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;

/**
 * 角色
 *
 */
@TableName("sys_role")
public class Role implements Serializable {

	/**
	 * 角色ID
	*/
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;
	
	/**
	 * 角色编码
	*/
	@TableField("code")
	private String code;
	
	/**
	 * 角色名称
	*/
	@TableField("name")
	private String name;
	
	/**
	 * 描述
	*/
	@TableField("remark")
	private String remark;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

}
