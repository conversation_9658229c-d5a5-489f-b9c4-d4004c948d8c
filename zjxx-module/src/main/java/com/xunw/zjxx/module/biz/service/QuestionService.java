package com.xunw.zjxx.module.biz.service;

import com.alibaba.fastjson.JSON;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class QuestionService {

    @Autowired
    private ExamServiceApi examServiceApi;

    /**
     * 根据题库ids抽取一道客观题用于课件弹题
     */
    @GlobalTransactional
    @Transactional
    public Object getRandomObjectiveQuestion(String quesDbIds, String token) {
        return JSON.parseObject(JSON.toJSONString(examServiceApi.getQuestionDbGetRandomObjectiveQuestion(quesDbIds))).getJSONObject("data");
    }
}
