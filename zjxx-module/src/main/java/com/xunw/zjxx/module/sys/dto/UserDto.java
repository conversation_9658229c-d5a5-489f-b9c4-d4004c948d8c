package com.xunw.zjxx.module.sys.dto;

import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.module.enums.Status;

public class UserDto {

    private String id;

    private String username;

    private String name;

    private String sfzh;

    private String password;

    private String mobile;

    private Status status;

    private UserTypeEnum userType;

    private String remark;


    private String orgId;

    private String appId;

    private String hostOrgId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSfzh() {
        return sfzh;
    }

    public void setSfzh(String sfzh) {
        this.sfzh = sfzh;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public UserTypeEnum getUserType() {
        return userType;
    }

    public void setUserType(UserTypeEnum userType) {
        this.userType = userType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }
}
