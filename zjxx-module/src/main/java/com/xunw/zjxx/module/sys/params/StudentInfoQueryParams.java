package com.xunw.zjxx.module.sys.params;

import com.xunw.zjxx.common.base.BaseQueryParams;

import java.util.Date;

public class StudentInfoQueryParams extends BaseQueryParams {

    /**
     * 主键
     */
    private String id;

    /**
     * 学员用户表主键
     */
    private String studentId;

    /**
     * 身份证号
     */
    private String sfzh;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String gender;

    /**
     * 枚举类型  56个民族
     */
    private String nation;

    /**
     * 枚举类型
     */
    private String politicalType;

    /**
     * 枚举类型
     */
    private String familyType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 身份证地址
     */
    private String certiAddress;

    /**
     * 籍贯省
     */
    private String certiProvince;

    /**
     * 籍贯-市
     */
    private String certiCity;

    /**
     * 居住地
     */
    private String address;

    /**
     *
     */
    private String postCode;

    /**
     * 职业
     */
    private String profession;

    /**
     * 学历 枚举类型 ：
     */
    private String education;

    /**
     * 是否毕业
     */
    private String isGraduated;

    /**
     * 毕业证书号
     */
    private String graduateNum;

    /**
     * 毕业学校
     */
    private String graduateSchool;

    /**
     * 入学时间
     */
    private Date schoolTime;

    /**
     * 身份证相片正面
     */
    private String sfzzm;

    /**
     * 身份证相片反面
     */
    private String sfzfm;

    /**
     * 学号
     */
    private String studentNum;

    /**
     * 学员登记照
     */
    private String studentPhoto;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 专业
     */
    private String specialty;

    /**
     * 毕业时间
     */
    private Date graduateTime;

    /**
     * 职务
     */
    private String zw;

    /**
     * 职称
     */
    private String zc;

    /**
     * 学员用户来源
     */
    private String ksly;

    /**
     * 电话 座机
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ
     */
    private String qq;

    /**
     * 微信号
     */
    private String wxh;

    /**
     *
     */
    private String birthday;

    /**
     * 现职业等级
     */
    private String currentTechLevel;

    /**
     * 岗位
     */
    private String gw;

    /**
     * 工种
     */
    private String gz;

    /**
     * 学徒性质
     */
    private String xtxz;

    /**
     * 到职日期
     */
    private String dzrq;

    /**
     * 培养目标
     */
    private String pymb;

    /**
     * 备注
     */
    private String remark;

    /**
     * 导师姓名
     */
    private String teacherName;

    /**
     * 导师岗位
     */
    private String teacherGw;

    /**
     * 导师身份证号
     */
    private String teacherCertino;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 纳税人识别号
     */
    private String invoiceCode;

    /**
     * 发票-开户行
     */
    private String invoiceBank;

    /**
     * 发票-银行账号
     */
    private String invoiceBankAccount;

    /**
     * 发票-单位地址
     */
    private String invoiceOrgAddress;

    /**
     * 发票-单位电话
     */
    private String invoiceOrgPhone;

    /**
     * 所在院系
     */
    private String college;

    /**
     * 所在班级
     */
    private String classz;

    /**
     * 单位所在地-省份
     */
    private String companyProvinceCode;

    /**
     * 单位所在地-市
     */
    private String companyCityCode;

    /**
     * 单位所在地-区
     */
    private String companyDistrictCode;

    /**
     * 学历证书图片
     */
    private String eduCertiPhoto;

    /**
     * 证件类型
     */
    private String idType;
    /**
     * 证件号
     */
    private String idNumber;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 验证码
     */
    private String code;
    /**
     * 密码
     */
    private String password;
    /**
     * 所属组织机构
     */
    private String orgId;

    private String sex;
    /**
     * 职务级别
     */
    private String jobLevel;
    /**
     * 政治面貌
     */
    private String politicsStatus;

    /**
     * 工作单位
     */
    private String workUnit;
    /**
     * 专业技术职称
     */
    private String professionalTitles;
    /**
     * 职称系列
     */
    private String titleSeries;

    private String loginName;

    private String status;

    private String userType;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public String getSfzh() {
        return sfzh;
    }

    public void setSfzh(String sfzh) {
        this.sfzh = sfzh;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getPoliticalType() {
        return politicalType;
    }

    public void setPoliticalType(String politicalType) {
        this.politicalType = politicalType;
    }

    public String getFamilyType() {
        return familyType;
    }

    public void setFamilyType(String familyType) {
        this.familyType = familyType;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCertiAddress() {
        return certiAddress;
    }

    public void setCertiAddress(String certiAddress) {
        this.certiAddress = certiAddress;
    }

    public String getCertiProvince() {
        return certiProvince;
    }

    public void setCertiProvince(String certiProvince) {
        this.certiProvince = certiProvince;
    }

    public String getCertiCity() {
        return certiCity;
    }

    public void setCertiCity(String certiCity) {
        this.certiCity = certiCity;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getProfession() {
        return profession;
    }

    public void setProfession(String profession) {
        this.profession = profession;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getIsGraduated() {
        return isGraduated;
    }

    public void setIsGraduated(String isGraduated) {
        this.isGraduated = isGraduated;
    }

    public String getGraduateNum() {
        return graduateNum;
    }

    public void setGraduateNum(String graduateNum) {
        this.graduateNum = graduateNum;
    }

    public String getGraduateSchool() {
        return graduateSchool;
    }

    public void setGraduateSchool(String graduateSchool) {
        this.graduateSchool = graduateSchool;
    }

    public Date getSchoolTime() {
        return schoolTime;
    }

    public void setSchoolTime(Date schoolTime) {
        this.schoolTime = schoolTime;
    }

    public String getSfzzm() {
        return sfzzm;
    }

    public void setSfzzm(String sfzzm) {
        this.sfzzm = sfzzm;
    }

    public String getSfzfm() {
        return sfzfm;
    }

    public void setSfzfm(String sfzfm) {
        this.sfzfm = sfzfm;
    }

    public String getStudentNum() {
        return studentNum;
    }

    public void setStudentNum(String studentNum) {
        this.studentNum = studentNum;
    }

    public String getStudentPhoto() {
        return studentPhoto;
    }

    public void setStudentPhoto(String studentPhoto) {
        this.studentPhoto = studentPhoto;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSpecialty() {
        return specialty;
    }

    public void setSpecialty(String specialty) {
        this.specialty = specialty;
    }

    public Date getGraduateTime() {
        return graduateTime;
    }

    public void setGraduateTime(Date graduateTime) {
        this.graduateTime = graduateTime;
    }

    public String getZw() {
        return zw;
    }

    public void setZw(String zw) {
        this.zw = zw;
    }

    public String getZc() {
        return zc;
    }

    public void setZc(String zc) {
        this.zc = zc;
    }

    public String getKsly() {
        return ksly;
    }

    public void setKsly(String ksly) {
        this.ksly = ksly;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getWxh() {
        return wxh;
    }

    public void setWxh(String wxh) {
        this.wxh = wxh;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getCurrentTechLevel() {
        return currentTechLevel;
    }

    public void setCurrentTechLevel(String currentTechLevel) {
        this.currentTechLevel = currentTechLevel;
    }

    public String getGw() {
        return gw;
    }

    public void setGw(String gw) {
        this.gw = gw;
    }

    public String getGz() {
        return gz;
    }

    public void setGz(String gz) {
        this.gz = gz;
    }

    public String getXtxz() {
        return xtxz;
    }

    public void setXtxz(String xtxz) {
        this.xtxz = xtxz;
    }

    public String getDzrq() {
        return dzrq;
    }

    public void setDzrq(String dzrq) {
        this.dzrq = dzrq;
    }

    public String getPymb() {
        return pymb;
    }

    public void setPymb(String pymb) {
        this.pymb = pymb;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getTeacherGw() {
        return teacherGw;
    }

    public void setTeacherGw(String teacherGw) {
        this.teacherGw = teacherGw;
    }

    public String getTeacherCertino() {
        return teacherCertino;
    }

    public void setTeacherCertino(String teacherCertino) {
        this.teacherCertino = teacherCertino;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoiceBank() {
        return invoiceBank;
    }

    public void setInvoiceBank(String invoiceBank) {
        this.invoiceBank = invoiceBank;
    }

    public String getInvoiceBankAccount() {
        return invoiceBankAccount;
    }

    public void setInvoiceBankAccount(String invoiceBankAccount) {
        this.invoiceBankAccount = invoiceBankAccount;
    }

    public String getInvoiceOrgAddress() {
        return invoiceOrgAddress;
    }

    public void setInvoiceOrgAddress(String invoiceOrgAddress) {
        this.invoiceOrgAddress = invoiceOrgAddress;
    }

    public String getInvoiceOrgPhone() {
        return invoiceOrgPhone;
    }

    public void setInvoiceOrgPhone(String invoiceOrgPhone) {
        this.invoiceOrgPhone = invoiceOrgPhone;
    }

    public String getCollege() {
        return college;
    }

    public void setCollege(String college) {
        this.college = college;
    }

    public String getClassz() {
        return classz;
    }

    public void setClassz(String classz) {
        this.classz = classz;
    }

    public String getCompanyProvinceCode() {
        return companyProvinceCode;
    }

    public void setCompanyProvinceCode(String companyProvinceCode) {
        this.companyProvinceCode = companyProvinceCode;
    }

    public String getCompanyCityCode() {
        return companyCityCode;
    }

    public void setCompanyCityCode(String companyCityCode) {
        this.companyCityCode = companyCityCode;
    }

    public String getCompanyDistrictCode() {
        return companyDistrictCode;
    }

    public void setCompanyDistrictCode(String companyDistrictCode) {
        this.companyDistrictCode = companyDistrictCode;
    }

    public String getEduCertiPhoto() {
        return eduCertiPhoto;
    }

    public void setEduCertiPhoto(String eduCertiPhoto) {
        this.eduCertiPhoto = eduCertiPhoto;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getJobLevel() {
        return jobLevel;
    }

    public void setJobLevel(String jobLevel) {
        this.jobLevel = jobLevel;
    }

    public String getPoliticsStatus() {
        return politicsStatus;
    }

    public void setPoliticsStatus(String politicsStatus) {
        this.politicsStatus = politicsStatus;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public String getProfessionalTitles() {
        return professionalTitles;
    }

    public void setProfessionalTitles(String professionalTitles) {
        this.professionalTitles = professionalTitles;
    }

    public String getTitleSeries() {
        return titleSeries;
    }

    public void setTitleSeries(String titleSeries) {
        this.titleSeries = titleSeries;
    }
}
