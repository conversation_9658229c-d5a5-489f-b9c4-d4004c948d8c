package com.xunw.zjxx.module.dto;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.xunw.zjxx.module.enums.BizType;
import com.xunw.zjxx.module.enums.PaperCategory;
import com.xunw.zjxx.module.rpc.ExamService.enums.Status;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RemoteExamPaperDto {

    /**
     * 考试试卷id
     */
    @TableId
    private String id;

    /**
     * 试卷名称
     */
    private String name;

    /**
     * 考试开始时间
     */
    private Date startTime;

    /**
     * 考试结束时间
     */
    private Date endTime;

    /**
     * 考试时长（分钟）
     */
    private Integer duration;

    /**
     * 试卷总分
     */
    private Integer totalScore;

    /**
     * 及格分数
     */
    private Integer passScore;

    /**
     * 是否主客观 0否,1是
     */
    private String isObject;

    /**
     * 说明
     */
    private String remark;

    /**
     * 试卷状态：DRAFT("草稿"),
     DISABLED("禁用"),
     ENABLE("启用");
     */
    private Status status;

    /**
     * 试卷分类
     */
    private PaperCategory category;

    /**
     * 是否开放答案和解析
     */
    private Integer answerExpose;

    /**
     * 是否开启抓拍
     */
    private Integer needPhoto;

    /**
     * 抓拍间隔时间（秒）
     */
    private Integer photoInterval;

    /**
     * 试题排序方式,0正常,1随机
     */
    private Integer orderType;

    /**
     * 试卷类型,0普通试卷,1随机试卷，2模板试卷
     */
    private Integer paperType;

    /**
     * 是否启用人脸比对
     */
    private Integer faceComparison;

    /**
     * 人脸比对阈值
     */
    private String threshold;

    /**
     * 是否整卷展示 0不是 1是
     */
    private String showMode;

    /**
     * 系统标识
     */
    private String appId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * 课程id
     */
    private String courseId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    private Date lastModifiedTime;

    /**
     * 验证码
     */
    private String verify;

    /**
     * 是否允许手机考试
     */
    private String isAllowPhone;

    /**
     * 面授MIANSHOU、在线课堂ZXKT、课件学习KJXX、实践技能SJJN、成教ADULT、培训TRAIN
     */
    private String bizType;

    private String showAnswer;

    private Date showTime;

    private String  ipStage;

    //是否是模拟考试
    private String isSimulate;

    @TableField(exist = false)
    private List<RemoteExamPaperSectionDto> sections = new ArrayList<RemoteExamPaperSectionDto>();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getPassScore() {
        return passScore;
    }

    public void setPassScore(Integer passScore) {
        this.passScore = passScore;
    }

    public String getIsObject() {
        return isObject;
    }

    public void setIsObject(String isObject) {
        this.isObject = isObject;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public PaperCategory getCategory() {
        return category;
    }

    public void setCategory(PaperCategory category) {
        this.category = category;
    }

    public Integer getAnswerExpose() {
        return answerExpose;
    }

    public void setAnswerExpose(Integer answerExpose) {
        this.answerExpose = answerExpose;
    }

    public Integer getNeedPhoto() {
        return needPhoto;
    }

    public void setNeedPhoto(Integer needPhoto) {
        this.needPhoto = needPhoto;
    }

    public Integer getPhotoInterval() {
        return photoInterval;
    }

    public void setPhotoInterval(Integer photoInterval) {
        this.photoInterval = photoInterval;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getPaperType() {
        return paperType;
    }

    public void setPaperType(Integer paperType) {
        this.paperType = paperType;
    }

    public Integer getFaceComparison() {
        return faceComparison;
    }

    public void setFaceComparison(Integer faceComparison) {
        this.faceComparison = faceComparison;
    }

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public String getShowMode() {
        return showMode;
    }

    public void setShowMode(String showMode) {
        this.showMode = showMode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public Date getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(Date lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public String getVerify() {
        return verify;
    }

    public void setVerify(String verify) {
        this.verify = verify;
    }

    public String getIsAllowPhone() {
        return isAllowPhone;
    }

    public void setIsAllowPhone(String isAllowPhone) {
        this.isAllowPhone = isAllowPhone;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getShowAnswer() {
        return showAnswer;
    }

    public void setShowAnswer(String showAnswer) {
        this.showAnswer = showAnswer;
    }

    public Date getShowTime() {
        return showTime;
    }

    public void setShowTime(Date showTime) {
        this.showTime = showTime;
    }

    public String getIpStage() {
        return ipStage;
    }

    public void setIpStage(String ipStage) {
        this.ipStage = ipStage;
    }

    public String getIsSimulate() {
        return isSimulate;
    }

    public void setIsSimulate(String isSimulate) {
        this.isSimulate = isSimulate;
    }

    public List<RemoteExamPaperSectionDto> getSections() {
        return sections;
    }

    public void setSections(List<RemoteExamPaperSectionDto> sections) {
        this.sections = sections;
    }
}
