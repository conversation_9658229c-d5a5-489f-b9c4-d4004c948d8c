package com.xunw.zjxx.module.base;

import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.CacheHelper;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.service.OrgService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.servlet.http.HttpServletRequest;

/**
 * 主办单位解析服务
 * <AUTHOR>
 */
@Service
public class HostOrgResolveService {

	private static final Logger LOGGER = LoggerFactory.getLogger(HostOrgResolveService.class);
	private final OrgService orgService;

	public HostOrgResolveService(OrgService orgService) {
		this.orgService = orgService;
	}

	/**
	 * 获取token
	 */
	protected String getAccessToken(HttpServletRequest request) {
		return request.getHeader(Constants.AUTH.AUTHORIZATION);
	}

	/**
	 * 1、已登录则取当前用户主办单位
	 * 2、未登录则解析请求中的域名部分，并通过域名检索系统中配置的主办单位
	 */
	public Org _CURRENT_HOST_ORG(HttpServletRequest request) {
		LoginUser loginUser = CacheHelper.getCache(Constants.AUTH.USER_TOKEN_CACHE, this.getAccessToken(request));
		if(loginUser != null) {
			if (loginUser.getHostOrg()!=null) {
				return loginUser.getHostOrg();
			}
			else {
				return getHostOrgByLoginUser(loginUser);
			}
		}
		else {
			String url = request.getRequestURL().toString();
			String protocol = request.getScheme();
			url = url.replace(protocol, "").replace("://", "");
			String[] array = StringUtils.split(url, "/");
			if (array == null || array.length == 0) {
				throw BizException.withMessage("解析主办单位失败，请求URL地址格式错误，请重新输入请求地址");
			}
			String subdomain = array[0];//取出请求地址的域名
			LOGGER.info("已自动注入主办单位[subdomain]:" + subdomain);
			Object object = CacheHelper.getCache(Constants.HOST_ORG_DOMAIN_CACHE, subdomain);
			if (object != null) {
				Org hostOrg = (Org)object;
				LOGGER.info("已自动注入主办单位[已读取缓存]:" + hostOrg.getName());
				return hostOrg;
			}
			else {
				Org hostOrg = orgService.getHostOrgByDomain(subdomain);
				hostOrg = hostOrg !=null ? hostOrg : orgService.getHostOrgByDomain(Constants.DEFAULT_DOMAIN);
				if (hostOrg == null) {
					throw BizException.withMessage("系统尚未配置主办单位，请先配置");
				}
				CacheHelper.setCache(Constants.HOST_ORG_DOMAIN_CACHE, subdomain, hostOrg);
				LOGGER.info("已自动注入主办单位[已写入缓存]:"+hostOrg.getName());
				return hostOrg;
			}
		}
	}

	/**
	 * 获取主办单位[根据域名解析出的主办单位]门户的访问地址
	 */
	public String _CURRENT_HOST_ORG_PORTAL_WEB_URL(HttpServletRequest request) {
		Org hostOrg = _CURRENT_HOST_ORG(request);
		String protocol = request.getScheme();
		return protocol + "://" + hostOrg.getPortalDomain() +"/";
	}

	/**
	 * 获取主办单位[根据域名解析出的主办单位]管理端的访问地址
	 */
	public String _CURRENT_HOST_ORG_ADMIN_WEB_URL(HttpServletRequest request) {
		Org hostOrg = _CURRENT_HOST_ORG(request);
		String protocol = request.getScheme();
		return protocol + "://" + hostOrg.getAdminDomain() +"/";
	}

	/**
     * 获取当前登录用户的主办单位
    */
    private Org getHostOrgByLoginUser(LoginUser loginUser) {
    	Org org = loginUser.getOrg();
    	if(org == null) {
    		return null;
    	}
    	else {
			Org topOrg = this.getTopOrg(loginUser.getOrg().getId());
			return topOrg;
    	}
    }

	/**
	 * 获取顶层机构
	 */
	private Org getTopOrg(String orgId) {
		return orgService.getTopOrg(orgId);
	}

}
