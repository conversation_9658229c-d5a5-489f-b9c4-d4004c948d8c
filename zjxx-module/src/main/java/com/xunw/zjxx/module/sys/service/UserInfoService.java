package com.xunw.zjxx.module.sys.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.DateUtils;
import com.xunw.zjxx.module.biz.service.CheckService;
import com.xunw.zjxx.module.enums.Education;
import com.xunw.zjxx.module.enums.RoleEnum;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.dto.UserAddReqParams;
import com.xunw.zjxx.module.sys.entity.*;
import com.xunw.zjxx.module.sys.mapper.UserInfoMapper;
import com.xunw.zjxx.module.sys.mapper.UserMapper;
import com.xunw.zjxx.module.sys.mapper.UserRoleMapper;
import com.xunw.zjxx.module.sys.params.UserQueryParams;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createDate 2023-08-08 13:32:10
 */
@Service
public class UserInfoService extends BaseCRUDService<UserInfoMapper, UserInfo> {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private CheckService checkService;
    @Autowired
    private UserRoleMapper userRoleMapper;
    @Autowired
    private RoleService roleService;
    @Autowired
    private OrgService orgService;

    @Transactional
    public Object pageQuery(UserQueryParams params) throws Exception {
        params.setIsDeep(Constants.YES);
        return this.list(params);
    }

    @Transactional
    public User add(UserAddReqParams params, String hostOrgId,  String loginUserId) throws Exception {
        //校验是否是弱密码
        checkService.isRawPassword(params.getPassword());
        if (org.apache.commons.lang.StringUtils.isEmpty(params.getUsername())) {
            throw new BizException("用户名不能为空");
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(params.getName())) {
            throw new BizException("姓名不能为空");
        }
        if (org.apache.commons.lang.StringUtils.isEmpty(params.getPassword())) {
            throw new BizException("密码不能为空");
        }
        if (this.isLoginNameExists(params.getUsername())) {
            throw new BizException("用户名已经存在，请更换用户名");
        }
        User user = new User();
        user.setId(UUID.randomUUID().toString());
        user.setUsername(params.getUsername());
        user.setName(params.getName());
        user.setPassword(params.getPassword());
        user.setSfzh(params.getSfzh());
        user.setMobile(params.getMobile());
        user.setUserType(UserTypeEnum.ADMIN);
        user.setStatus(Status.OK);
        user.setPassword(DigestUtils.md5Hex(user.getPassword()));
        user.setCreateTime(DateUtils.now());
        user.setOrgId(params.getOrgId());
        user.setHostOrgId(params.getOrgId());
        user.setOpenId(params.getOpenId());
        user.setRemark(params.getRemark());
        userMapper.insert(user);

        UserInfo userInfo = new UserInfo();
        userInfo.setId(BaseUtil.generateId());
        userInfo.setUserId(user.getId());
        userInfo.setEducation(params.getEducation());
        userInfo.setGw(params.getGw());
        userInfo.setZw(params.getZw());
        userInfo.setZc(params.getZc());
        userInfo.setIsOut(params.getIsOut());
        userInfo.setBrief(params.getBrief());
        userInfo.setPhoto(params.getWorkPhoto());
        userInfo.setCreatorId(loginUserId);
        userInfo.setCreateTime(new Date());
        userInfo.setWorkUnit(params.getWorkUnit());
        mapper.insert(userInfo);
        //设置用户角色
        EntityWrapper<UserRole> wrapper = new EntityWrapper<>();
        wrapper.eq("user_id", user.getId());
        userRoleMapper.delete(wrapper);
        if (StringUtils.isNotEmpty(params.getRoleId())) {
            RoleEnum roleEnum;
            if ("admin".equals(params.getRoleId())) {
                roleEnum = RoleEnum.ADMIN;
            } else {
                roleEnum = RoleEnum.HOST_ORG;
            }
            UserRole userRole = new UserRole();
            userRole.setId(BaseUtil.generateId2());
            String roleId = roleService.getRoleId(roleEnum);
            if(roleId == null){
                throw BizException.withMessage("角色不存在:"+roleEnum);
            }
            userRole.setRoleId(roleId);
            userRole.setUserId(user.getId());
            userRoleMapper.insert(userRole);
        } else if (StringUtils.isNotEmpty(params.getRole())) {
            for (String s : params.getRole().split(",")) {
                RoleEnum roleEnum = RoleEnum.valueOf(s);
                List<UserRole> list = new ArrayList<>();
                UserRole userRole = new UserRole();
                userRole.setId(BaseUtil.generateId2());
                String roleId = roleService.getRoleId(roleEnum);
                if(roleId == null){
                    throw BizException.withMessage("角色不存在:"+roleEnum);
                }
                userRole.setRoleId(roleId);
                userRole.setUserId(user.getId());
                userRoleMapper.insert(userRole);
            }
        } else {
            throw BizException.withMessage("角色不存在");
        }
        return user;
    }

    @Transactional
    public void edit(String id, String name, String mobile, String role,
                     String sfzh, Education education, String isOut, String workUnit,
                     String gw, String zw, String zc, String workPhoto, String brief,
                     String loginUserId) throws Exception {
        if (org.apache.commons.lang.StringUtils.isEmpty(id)) {
            throw new BizException("id不能为空");
        }
        User updateUser = userMapper.selectById(id);
        if (null == updateUser) {
            throw BizException.withMessage("用户不存在");
        }
        updateUser.setMobile(mobile);
        updateUser.setSfzh(sfzh);
        updateUser.setName(name);
        updateUser.setUpdateTime(DateUtils.now());
        userMapper.updateById(updateUser);

        Optional<UserInfo> userInfo = mapper.selectList((EntityWrapper<UserInfo>) new EntityWrapper<UserInfo>()
                .eq("user_id", id)).stream().findFirst();
        UserInfo info;
        if (userInfo.isPresent()) {
            info = userInfo.get();
            info.setEducation(education);
            info.setWorkUnit(workUnit);
            info.setGw(gw);
            info.setZw(zw);
            info.setZc(zc);
            info.setIsOut(isOut);
            info.setPhoto(workPhoto);
            info.setBrief(brief);
            info.setUpdateTime(new Date());
            info.setUpdatorId(loginUserId);
            mapper.updateById(info);
        } else {
            info = new UserInfo();
            info.setId(BaseUtil.generateId());
            info.setUserId(id);
            info.setEducation(education);
            info.setGw(gw);
            info.setZw(zw);
            info.setZc(zc);
            info.setIsOut(isOut);
            info.setBrief(brief);
            info.setPhoto(workPhoto);
            info.setCreatorId(loginUserId);
            info.setCreateTime(new Date());
            info.setWorkUnit(workUnit);
            mapper.insert(info);
        }
        //设置新的角色
        EntityWrapper<UserRole> wrapper = new EntityWrapper<>();
        wrapper.eq("user_id", id);
        userRoleMapper.delete(wrapper);
        for (String s : role.split(",")) {
            RoleEnum roleEnum = RoleEnum.valueOf(s);
            List<UserRole> list = new ArrayList<>();
            UserRole userRole = new UserRole();
            userRole.setId(BaseUtil.generateId2());
            String roleId = roleService.getRoleId(roleEnum);
            if(roleId == null){
                throw BizException.withMessage("角色不存在:"+roleEnum);
            }
            userRole.setRoleId(roleId);
            userRole.setUserId(id);
            userRoleMapper.insert(userRole);
        }
    }

    public User getUserByOpenId(String openId){
        EntityWrapper<User> wrapper = new EntityWrapper();
        wrapper.eq("open_id", openId);
        List<User> list = userMapper.selectList(wrapper);
        return list.size() > 0 ? list.get(0) : null;
    }


    @Transactional
    public void updateUser(User user) {
        userMapper.updateAllColumnById(user);
    }


    @Transactional
    public Object getById(String id) throws Exception {
        User user = userMapper.selectById(id);
        if (null == user) {
            throw BizException.withMessage("用户不存在");
        }
        EntityWrapper<UserRole> wrapper = new EntityWrapper<>();
        wrapper.eq("user_id", id);
        List<UserRole> userRoles = userRoleMapper.selectList(wrapper);
        List<Role> roles = Collections.EMPTY_LIST;
        if (userRoles.size() > 0) {
            roles = roleService.selectBatchIds(userRoles.stream().map(x->x.getRoleId()).collect(Collectors.toList()));
        }
        Org org = orgService.selectById(user.getHostOrgId());
        Map<String, Object> result = new HashMap<>();
        UserInfo userInfo = mapper.getByUserId(id);
        if (userInfo != null) {
            result.put("id", id);
            result.put("username", user.getUsername());
            result.put("name", user.getName());
            result.put("mobile", user.getMobile());
            result.put("orgName", org == null? "" : org.getName());
            result.put("status", user.getStatus());
            result.put("role",roles);
            result.put("sfzh", user.getSfzh());
            result.put("education", userInfo.getEducation());
            result.put("isOut", userInfo.getIsOut());
            result.put("workUnit", userInfo.getWorkUnit());
            result.put("gw", userInfo.getGw());
            result.put("zw", userInfo.getZw());
            result.put("zc", userInfo.getZc());
            result.put("workPhoto", userInfo.getPhoto());
            result.put("brief", userInfo.getBrief());
        }
        return result;
    }

    @Transactional
    public void enable(String id, Status status) throws Exception {
        User user = userMapper.selectById(id);
        if (null == user) {
            throw BizException.withMessage("用户不存在");
        }
        user.setStatus(status);
        userMapper.updateById(user);
    }

    @Transactional
    public void resetPassword(String id) throws Exception {
        if (BaseUtil.isEmpty(id)) {
            throw new BizException("用户id不能为空");
        }
        User updateUser = userMapper.selectById(id);
        updateUser.setPassword(DigestUtils.md5Hex(Constants.DEFAULT_PASSWORD));
        userMapper.updateById(updateUser);
    }

    @Transactional
    public void delete(String id) throws Exception {
        userMapper.deleteById(id);
        List<UserInfo> userInfos = mapper.selectList((EntityWrapper<UserInfo>) new EntityWrapper<UserInfo>()
                .eq("user_id", id));
        if (CollectionUtils.isNotEmpty(userInfos)) {
            UserInfo userInfo = userInfos.get(0);
            mapper.deleteById(userInfo.getId());
        }
    }

    public UserInfo getByUserId(String id) {
        return mapper.getByUserId(id);
    }

    public Page list(UserQueryParams params) {
        List<Map<String, Object>> records = userMapper.list(params.getCondition(), params);
        params.setRecords(records);
        return params;
    }

    /**
     * 用户名是否存在
     */
    public boolean isLoginNameExists(String loginName) {
        EntityWrapper<User> wrapper = new EntityWrapper();
        wrapper.eq("username", loginName);
        return userMapper.selectCount(wrapper) > 0;
    }

    public Map<String, Object> getUserByUserName(String username, String hostOrgId){
        return mapper.getUserByUserName(username, hostOrgId);
    };

    public List<Role> getRolesByUserId(String userId){
        return mapper.getRolesByUserId(userId);
    }


}
