package com.xunw.zjxx.module.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.biz.entity.StudentCerti;
import com.xunw.zjxx.module.biz.entity.Xm;
import com.xunw.zjxx.module.biz.mapper.XmMapper;
import com.xunw.zjxx.module.biz.params.LearningRecordQueryParams;
import com.xunw.zjxx.module.biz.params.XmQueryParams;
import com.xunw.zjxx.module.dto.ChapterDto;
import com.xunw.zjxx.module.dto.LessonDto;
import com.xunw.zjxx.module.dto.TreeNode;
import com.xunw.zjxx.module.enums.Category;
import com.xunw.zjxx.module.enums.CheckStatus;
import com.xunw.zjxx.module.enums.OrderStatus;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.mapper.CourseMapper;
import com.xunw.zjxx.module.inf.service.CourseService;
import com.xunw.zjxx.module.rpc.FaceServiceApi;
import com.xunw.zjxx.module.rpc.LearningCoursewareServiceApi;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.entity.User;
import com.xunw.zjxx.module.sys.mapper.StudentInfoMapper;
import com.xunw.zjxx.module.sys.service.UserService;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @description 针对表【biz_xm】的数据库操作Service
 * @createDate 2023-08-08 13:32:09
 */
@Service
public class XmService extends BaseCRUDService<XmMapper, Xm> {

    @Autowired
    private CourseMapper courseMapper;
    @Autowired
    private StudentInfoMapper studentInfoMapper;
    @Autowired
    private StudentCertiService studentCertiService;
    @Autowired
    private CourseService courseService;
    @Autowired
    private ScoreService scoreService;
    @Autowired
    private LearningCoursewareServiceApi learningCoursewareServiceApi;
    @Autowired
    private FaceServiceApi faceServiceApi;
    @Autowired
    private UserService userService;

    @GlobalTransactional
    @Transactional
    public Page pageQuery(XmQueryParams params) {
        List<Map<String, Object>> list = mapper.pageQuery(params.getCondition(), params);
        params.setRecords(list);
        return params;
    }

    /**
     * 学习记录查询
     */
    public Page getStudentLearningRecordList(LearningRecordQueryParams params, String token) throws Exception {
        List<Map<String, Object>> studentLearningRecoredList = mapper.getStudentLearningRecoredList(params.getCondition(), params);
//        for (Map<String, Object> map : studentLearningRecoredList) {
//            map.put("progress", this.getStudyProgress(map.get("courseId").toString(), map.get("studentId").toString(), token));
//        }
        params.setRecords(studentLearningRecoredList);
        return params;
    }

    /**
     * 计算课程学习进度
     */
    @GlobalTransactional
    @Transactional
    public String getStudyProgress(String courseId, String studentId, String token) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("courseId", courseId);
        map.put("status", Status.OK);
        map.put("size", Integer.MAX_VALUE);
        map.put("current", 1);
        String s = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareList(null,courseId,Status.OK.name(),null,null,null,1,Integer.MAX_VALUE));
        //获取课件列表
        List<Map> records = JSON.parseArray(JSONObject.parseObject(s).getJSONObject("data").getString("records"), Map.class);
        List<String> coursewareIds = records.stream().map(x -> x.get("id").toString()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(coursewareIds)) {
            return "0";
        }
        Map<String, Object> getLearnMap = new HashMap<>();
        getLearnMap.put("coursewareId", coursewareIds.get(0));
        getLearnMap.put("studentId", studentId);
        getLearnMap.put("batchId", Constants.UPDATE_STUDY_TIME_BATCH_ID);
        String learn = JSONObject.toJSONString(learningCoursewareServiceApi.studyGetLearn(studentId,Constants.UPDATE_STUDY_TIME_BATCH_ID,coursewareIds.get(0)));
        List<ChapterDto> chapterDtoList = JSONObject.parseArray(JSONObject.parseObject(learn).getJSONObject("data").getString("chapters"), ChapterDto.class);
        double totalMinutes = chapterDtoList.stream().flatMap(x -> x.getLessons().stream()).mapToInt(x -> Integer.parseInt(x.getMinutes())).sum();
        if (totalMinutes == 0) {
            return "0";
        }
        double duration = chapterDtoList.stream().flatMap(x -> x.getLessons().stream()).mapToInt(x -> Integer.parseInt(x.getDuration())).sum();
        DecimalFormat decimalFormat = new DecimalFormat("#.00");
        return decimalFormat.format(duration / totalMinutes * 100);
    }

    @Transactional
    public Object details(String courseId, String coursewareId, String studentId) throws Exception {
        Course course = courseMapper.selectById(courseId);
        Object s = learningCoursewareServiceApi.coursewareGetById(coursewareId, null).getData();
        //获取课件列表
        Map courseware = JSON.parseObject(JSON.toJSONString(s), Map.class);
        if (courseware == null) {
            throw BizException.withMessage("课件不存在");
        }
        Object learn = learningCoursewareServiceApi.studyGetLearn(studentId, Constants.UPDATE_STUDY_TIME_BATCH_ID, courseware.get("id").toString()).getData();
        List<ChapterDto> chapterDtoList = JSONObject.parseArray(JSONObject.parseObject(JSON.toJSONString(learn)).getString("chapters"), ChapterDto.class);
        Map<String, Object> result = new HashMap<>();
        //处理课程数据
        Map<String, Object> courseMap = new HashMap<>();
        courseMap.put("id", course.getId());
        courseMap.put("name", course.getName());
        courseMap.put("totalMinutes", chapterDtoList.stream().flatMap(x -> x.getLessons().stream()).mapToInt(x -> Integer.parseInt(x.getMinutes())).sum());
        courseMap.put("finishMinutes", chapterDtoList.stream().flatMap(x -> x.getLessons().stream()).mapToInt(x -> Integer.parseInt(x.getDuration())).sum());
        result.put("course", courseMap);
        //处理列表数据
        List<Map<String, Object>> chapterList = new ArrayList<>();
        for (ChapterDto chapterDto : chapterDtoList) {
            for (LessonDto lesson : chapterDto.getLessons()) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("chapterId", chapterDto.getId());
                hashMap.put("chapterName", chapterDto.getName());
                hashMap.put("times", lesson.getMinutes());
                hashMap.put("completedTimes", lesson.getDuration());
                hashMap.put("lessonId", lesson.getId());
                hashMap.put("lessonName", lesson.getName());
                chapterList.add(hashMap);
            }
        }
        result.put("chapterList", chapterList);
        return result;
    }

    /**
     * 获取所有学习抓拍记录
     */
    @GlobalTransactional
    @Transactional
    public Object getAllPhoto(String coursewareId, String studentId) throws Exception {
        Object resp = faceServiceApi.learnCaptureLog(Constants.UPDATE_STUDY_TIME_BATCH_ID, coursewareId, null,
                null, null, "NONE", null, null, null, 1, Integer.MAX_VALUE).getData();
        return this.parsePhoto(resp, studentId);
    }

    /**
     * 获取课时学习抓拍记录
     */
    @GlobalTransactional
    @Transactional
    public Object getPhoto(String coursewareId, String chapterId, String lessonId, String studentId) throws Exception {
        Object resp = faceServiceApi.learnCaptureLog(Constants.UPDATE_STUDY_TIME_BATCH_ID, coursewareId, chapterId,
                lessonId, studentId, "NONE", null, null,null, 1, Integer.MAX_VALUE).getData();
        return this.parsePhoto(resp, studentId);
    }

    public Map<String, Object> parsePhoto(Object resp, String studentId) {
        Map<String, Object> result = new HashMap<>();
        List<StudentInfo> studentInfos = studentInfoMapper.selectList(new EntityWrapper<StudentInfo>()
                .eq("id", studentId));
        result.put("studentPhoto", studentInfos.get(0).getStudentPhoto());
        List<String> photos = JSON.parseObject(JSON.toJSONString(resp)).getJSONArray("records").stream()
                .map(x -> JSON.parseObject(x.toString()).getString("photoPath")).collect(Collectors.toList());
        result.put("photoList", photos);
        return result;
    }

    public Object portalDetails(String id, String loginUserId) {
        Xm xm = this.selectById(id);
        if(xm == null){
            throw BizException.withMessage("选择的项目无效、不存在");
        }
        Map<String,Object>result =new HashMap<>();
        result.put("id",xm.getId());
        result.put("name",xm.getName());
        result.put("logo",xm.getLogo());
        result.put("hour",xm.getHours());
        result.put("publicHours",xm.getGxkHours());
        result.put("specialtyHours",xm.getZykHours());
        result.put("startTime",xm.getStartTime());
        result.put("endTime",xm.getEndTime());
        result.put("state",xm.getStatus());
        //获取xm下面课程
        List<Map<String,Object>>xmCourse=mapper.getAllCourse(id);
        //获取学生以购买的课程
        List<String>studentBuyCourse =  mapper.getStudentBuyCourse(loginUserId,id, OrderStatus.PAID);
        xmCourse.stream().forEach(x->{
            if(studentBuyCourse.contains(String.valueOf(x.get("id")))){
                x.put("isBuy",1);
            }else {
                x.put("isBuy",0);
            }
        });
        Map<String, List<Map<String, Object>>> category = xmCourse.stream().collect(groupingBy(x ->String.valueOf( x.get("category"))));
        result.put("gxList",category.get(Category.GXK.name()));
        result.put("zyList",category.get(Category.ZYK.name()));
        return result;
    }


    public Object recordList(String studentId,String year){
        return mapper.recordList(studentId, year);
    }

    public Object recordYeas(String studentId){
    	return mapper.recordYears(studentId);
    }

    public Object certificateList(LoginUser loginUser, String accessToken) {
        //查询所有的报名的项目
        String userId = loginUser.getId();
        List<String> bmXmIdList = mapper.getBmCourseXmIdByStudentId(userId);
        List<Map<String,Object>> result =new ArrayList<>();
        if(bmXmIdList != null && bmXmIdList.size() > 0){
            result= mapper.getStudentCertiByXmId(bmXmIdList,userId);
        }
        return  result;
    }


    public Object getPlanTree(String hostOrgId) {
        List<Xm> xms = mapper.selectList(new EntityWrapper<Xm>()
                .eq("status", Status.OK)
                .eq("host_org_id", hostOrgId));
        Map<String, List<Xm>> map = xms.stream().collect(groupingBy(Xm::getYears));
        List<TreeNode> treeNodes = map.keySet().stream().map(x -> {
            TreeNode treeNode = new TreeNode();
            treeNode.setId(x);
            treeNode.setName(x);
            treeNode.setChildren(new ArrayList<>());
            return treeNode;
        }).collect(Collectors.toList());
        for (TreeNode treeNode : treeNodes) {
            treeNode.getChildren().addAll(map.get(treeNode.getId()).stream().map(x->{
                TreeNode node = new TreeNode();
                node.setId(x.getId());
                node.setName(x.getName());
                node.setChildren(new ArrayList<>());
                return node;
            }).collect(Collectors.toList()));
        }
        return treeNodes;
    }

    public Object certificateApply(String xmId, String loginUserId) {
        List<Map<String, Object>> list = scoreService.xmScore(loginUserId, xmId);
        if (CollectionUtils.isEmpty(list)) {
            throw BizException.withMessage("不满足申请条件");
        }
        Map<String, Object> map = list.get(0);
        if (BaseUtil.getDoubleValueFromMap(map, "gxkFinishHours") < BaseUtil.getDoubleValueFromMap(map, "gxkHours")
                || BaseUtil.getDoubleValueFromMap(map, "zykFinishHours") < BaseUtil.getDoubleValueFromMap(map, "zykHours")) {
            throw BizException.withMessage("不满足申请条件");
        }
        EntityWrapper<StudentCerti> wrapper = new EntityWrapper<StudentCerti>();
        wrapper.eq("xm_id", xmId);
        wrapper.eq("student_id", loginUserId);
        List<StudentCerti> studentCertiList = studentCertiService.selectList(wrapper);
        if(studentCertiList!=null && studentCertiList.size()==0){//证书列表信息不存在，则新增一条数据，状态为待审核
            StudentCerti studentCerti = new StudentCerti();
            studentCerti.setId(BaseUtil.generateId2());
            studentCerti.setXmId(xmId);
            studentCerti.setStudentId(loginUserId);
            studentCerti.setApplyTime(new Date());
            studentCerti.setCreateTime(new Date());
            studentCerti.setCreatorId(loginUserId);
            studentCerti.setCheckResult(CheckStatus.AUDIT.name());//待审核
            studentCertiService.insert(studentCerti);
        }else if(!StringUtils.equals(studentCertiList.get(0).getCheckResult(),CheckStatus.AUDIT.name()) && !StringUtils.equals(studentCertiList.get(0).getCheckResult(),CheckStatus.PASS.name())){
            StudentCerti studentCerti = new StudentCerti();
            studentCerti.setId(studentCertiList.get(0).getId());
            studentCerti.setCheckResult(CheckStatus.AUDIT.name());//修改为待审核
            studentCerti.setApplyTime(new Date());
            studentCertiService.updateById(studentCerti);
        }else{
           throw BizException.withMessage("已申请或已通过审核");
        }
        return true;
    }

    @GlobalTransactional
    @Transactional
    public Object planningDetails(String id, String loginUserId, String token) throws Exception {
        Xm xm = this.selectById(id);
        if(xm == null){
            throw BizException.withMessage("选择的项目无效、不存在");
        }
        Map<String,Object>result =new HashMap<>();
        result.put("id",xm.getId());
        result.put("name",xm.getName());
        result.put("logo",xm.getLogo());
        result.put("hour",xm.getHours());
        result.put("publicHours",xm.getGxkHours());
        result.put("specialtyHours",xm.getZykHours());
        result.put("startTime",xm.getStartTime());
        result.put("endTime",xm.getEndTime());
        result.put("state",xm.getStatus());
        //获取该培训计划下面课程
        List<Map<String,Object>> xmCourse = mapper.getCourseListByXmId(id, loginUserId);
        for (Map<String, Object> map : xmCourse) {
            //通过课程id查询课件 获取课件id
            map.put("coursewareId", JSON.parseObject(JSON.toJSONString(learningCoursewareServiceApi.coursewareGetById(null, map.get("id").toString()))).getJSONObject("data").getString("id"));
        }
        for (Map<String, Object> map : xmCourse) {
            map.put("minutes", courseService.getCourseMinutes(map.get("id").toString(), token));
        }
        Map<String, List<Map<String, Object>>> category = xmCourse.stream().collect(groupingBy(x ->String.valueOf( x.get("category"))));
        result.put("gxList",category.get(Category.GXK.name()));
        result.put("zyList",category.get(Category.ZYK.name()));
        return result;
    }

    /**
     * 获取学习计划的学习情况
     */
    public List<Map<String, Object>> getXmStudyRecords(String studentId, String hostOrgId) {
        return mapper.getXmStudyRecords(studentId, hostOrgId);
    }
}
