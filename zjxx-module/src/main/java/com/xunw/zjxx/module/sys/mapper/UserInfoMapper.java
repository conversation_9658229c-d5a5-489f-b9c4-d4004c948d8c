package com.xunw.zjxx.module.sys.mapper;


import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.xunw.zjxx.module.sys.entity.Role;
import com.xunw.zjxx.module.sys.entity.UserInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @createDate 2023-08-08 13:32:10
*/
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    UserInfo getByUserId(@Param("id") String id);


    Map<String, Object> getUserByUserName(@Param("username") String username, @Param("hostOrgId") String hostOrgId);

    List<Role> getRolesByUserId(@Param("userId") String userId);


}




