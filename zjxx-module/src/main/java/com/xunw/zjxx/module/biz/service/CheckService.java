package com.xunw.zjxx.module.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.biz.entity.*;
import com.xunw.zjxx.module.biz.mapper.*;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.rpc.LearningCoursewareServiceApi;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class CheckService {

    @Autowired
    private StudentBmCourseMapper studentBmCourseMapper;
    @Autowired
    private CoursewareProgressMapper coursewareProgressMapper;
    @Autowired
    private ExamDataMapper examDataMapper;
    @Autowired
    private ExamPaperMapper examPaperMapper;
    @Autowired
    private StudentCertiMapper studentCertiMapper;
    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private LearningCoursewareServiceApi learningCoursewareServiceApi;

    /**
     * 弱密码检测
     * 密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$%^&*-.）的8位-32位的组合，否则即为弱密码
     * 弱密码返回true  非弱密码 返回false
     */
    public void isRawPassword(String password) {
        if (StringUtils.isEmpty(password)) {
            throw BizException.withMessage("密码不能为空");
        }
        else {
            String patternStr ="^(?![A-Za-z0-9]+$)(?![a-z0-9#?!@$^&*-.]+$)(?![A-Za-z#?!@$^&*-.]+$)(?![A-Z0-9#?!@$^&*-.]+$)[a-zA-Z0-9#?!@$^&*-.]{8,32}$";
            if (!password.matches(patternStr)) {
                throw BizException.withMessage("密码必须是包含大写字母、小写字母、数字、特殊符号（#?!@$^&*-.）的8位-32位的组合");
            }
        }
    }

    /**
     * 课程删除校验
     */
    public void isDeleteCourse(String courseId,String token) {
        //报名校验
        Integer bmCount = studentBmCourseMapper.selectCount(new EntityWrapper<StudentBmCourse>()
                .eq("course_id", courseId));
        if (bmCount > 0) {
            throw BizException.withMessage("课程/课件已有学员报名,不能删除");
        }
        //学习校验
        Integer studyCount = coursewareProgressMapper.selectCount(new EntityWrapper<CoursewareProgress>()
                .eq("course_id", courseId));
        if (studyCount > 0) {
            throw BizException.withMessage("课程/课件已有学员学习,不能删除");
        }
        //考试校验
        Optional<ExamPaper> paper = examPaperMapper.selectList(new EntityWrapper<ExamPaper>()
                .eq("course_id", courseId)).stream().findFirst();
        if (paper.isPresent()) {
            Integer count = examDataMapper.selectCount(new EntityWrapper<ExamData>()
                    .eq("paper_id", paper.get().getId()));
            if (count > 0) {
                throw BizException.withMessage("课程/课件已有学员考试,不能删除");
            }
        }
        //题库校验
        Object resp = examServiceApi.questionDbList(null, null, null, courseId, null, 
                null, null, 1, Integer.MAX_VALUE).getData();
        JSONArray jsonArray = JSON.parseObject(JSON.toJSONString(resp)).getJSONArray("records");
        if (jsonArray != null && !jsonArray.isEmpty()) {
            throw BizException.withMessage("课程已有题库,不能删除");
        }
        //课件校验
        Object respCourseware = learningCoursewareServiceApi.coursewareList(null, courseId, null, null, 
                null, null, 1, Integer.MAX_VALUE).getData();
        JSONArray jsonArrayCourseware = JSON.parseObject(JSON.toJSONString(respCourseware)).getJSONArray("records");
        if (jsonArrayCourseware != null && !jsonArrayCourseware.isEmpty()) {
            throw BizException.withMessage("课程已有课件,不能删除");
        }
    }

    /**
     * 学习计划删除校验
     */
    public void isDeleteXm(String xmId) {
        Integer count = studentCertiMapper.selectCount(new EntityWrapper<StudentCerti>()
                .eq("xm_id", xmId));
        if (count > 0) {
            throw BizException.withMessage("学习计划已有学员申请发证,不能删除");
        }
    }

    /**
     * 课件删除校验
     */
    public void isDeleteKj(String coursewareId, String token) {
        //查询课件
        Object resp = learningCoursewareServiceApi.coursewareGetById(coursewareId, null).getData();
        String courseId = JSON.parseObject(JSON.toJSONString(resp)).getString("courseId");
        //报名校验
        Integer bmCount = studentBmCourseMapper.selectCount(new EntityWrapper<StudentBmCourse>()
                .eq("course_id", courseId));
        if (bmCount > 0) {
            throw BizException.withMessage("课程/课件已有学员报名,不能删除");
        }
        //学习校验
        Integer studyCount = coursewareProgressMapper.selectCount(new EntityWrapper<CoursewareProgress>()
                .eq("course_id", courseId));
        if (studyCount > 0) {
            throw BizException.withMessage("课程/课件已有学员学习,不能删除");
        }
        //考试校验
        Optional<ExamPaper> paper = examPaperMapper.selectList(new EntityWrapper<ExamPaper>()
                .eq("course_id", courseId)).stream().findFirst();
        if (paper.isPresent()) {
            Integer count = examDataMapper.selectCount(new EntityWrapper<ExamData>()
                    .eq("paper_id", paper.get().getId()));
            if (count > 0) {
                throw BizException.withMessage("课程/课件已有学员考试,不能删除");
            }
        }
        //题库校验
        Object respDb = examServiceApi.questionDbList(null, null, null, courseId, null,
                null, null, 1, Integer.MAX_VALUE).getData();
        JSONArray jsonArray = JSON.parseObject(JSON.toJSONString(respDb)).getJSONArray("records");
        if (jsonArray != null && !jsonArray.isEmpty()) {
            throw BizException.withMessage("课程已有题库,不能删除");
        }
    }

    /**
     * 课件更新判断
     */
    public Boolean isAllowUpdateKj(String coursewareId, String token) {
        //查询课件
        Object resp = learningCoursewareServiceApi.coursewareGetById(coursewareId, null).getData();
        String courseId = JSON.parseObject(JSON.toJSONString(resp)).getString("courseId");
        //报名校验
        Integer bmCount = studentBmCourseMapper.selectCount(new EntityWrapper<StudentBmCourse>()
                .eq("course_id", courseId));
        if (bmCount > 0) {
            return false;
        }
        //学习校验
        Integer studyCount = coursewareProgressMapper.selectCount(new EntityWrapper<CoursewareProgress>()
                .eq("course_id", courseId));
        if (studyCount > 0) {
            return false;
        }
        //考试校验
        Optional<ExamPaper> paper = examPaperMapper.selectList(new EntityWrapper<ExamPaper>()
                .eq("course_id", courseId)).stream().findFirst();
        if (paper.isPresent()) {
            Integer count = examDataMapper.selectCount(new EntityWrapper<ExamData>()
                    .eq("paper_id", paper.get().getId()));
            if (count > 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 试卷删除校验
     */
    public void isDeletePaper(String paperId) {
        Integer count = examDataMapper.selectCount(new EntityWrapper<ExamData>()
                .eq("paper_id", paperId));
        if (count > 0) {
            throw BizException.withMessage("试卷已经有学员考试，不能删除");
        }
    }
}
