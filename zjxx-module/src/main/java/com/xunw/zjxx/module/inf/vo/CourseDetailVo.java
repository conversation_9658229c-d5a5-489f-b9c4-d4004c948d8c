package com.xunw.zjxx.module.inf.vo;

import java.util.List;

public class CourseDetailVo {

    private String id;//课程ID
    private String coursewareId;//课件ID
    private String name;//课程名称
    private String typeName;//课程分类名称
    private String url;//课程缩略图地址
    private Double hour;//学时
    private Double money;//金额
    private String isBuy;//是否已购买
    private String isBm;//是否已报名
    private List<ChapterVo> chapters;
    private List<PracticeVo> practices;

    public CourseDetailVo() {
    }

    public CourseDetailVo(String id, String name, String typeName, String url, Double hour, Double money, String isBuy, String isBm, List<ChapterVo> chapters, List<PracticeVo> practices) {
        this.id = id;
        this.name = name;
        this.typeName = typeName;
        this.url = url;
        this.hour = hour;
        this.money = money;
        this.isBuy = isBuy;
        this.isBm = isBm;
        this.chapters = chapters;
        this.practices = practices;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Double getHour() {
        return hour;
    }

    public void setHour(Double hour) {
        this.hour = hour;
    }
    public Double getMoney() {
		return money;
	}

	public void setMoney(Double money) {
		this.money = money;
	}

	public String getIsBuy() {
        return isBuy;
    }

    public void setIsBuy(String isBuy) {
        this.isBuy = isBuy;
    }

    public List<ChapterVo> getChapters() {
        return chapters;
    }

    public void setChapters(List<ChapterVo> chapters) {
        this.chapters = chapters;
    }

    public List<PracticeVo> getPractices() {
        return practices;
    }

    public void setPractices(List<PracticeVo> practices) {
        this.practices = practices;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getIsBm() {
        return isBm;
    }

    public void setIsBm(String isBm) {
        this.isBm = isBm;
    }

	public String getCoursewareId() {
		return coursewareId;
	}

	public void setCoursewareId(String coursewareId) {
		this.coursewareId = coursewareId;
	}
}
