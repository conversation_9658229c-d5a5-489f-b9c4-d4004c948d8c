package com.xunw.zjxx.module.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.utils.*;
import com.xunw.zjxx.module.biz.entity.StudentBmCourse;
import com.xunw.zjxx.module.biz.mapper.StudentBmCourseMapper;
import com.xunw.zjxx.module.biz.params.MonitorQueryParams;
import com.xunw.zjxx.module.biz.params.StudentBmCourseQueryParams;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.mapper.CourseMapper;
import com.xunw.zjxx.module.rpc.FaceServiceApi;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.entity.User;
import com.xunw.zjxx.module.sys.mapper.StudentInfoMapper;
import io.seata.spring.annotation.GlobalTransactional;
import jxl.read.biff.BiffException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【biz_student_bm_course】的数据库操作Service
 * @createDate 2023-08-08 13:32:09
 */
@Service
public class StudentBmCourseService extends BaseCRUDService<StudentBmCourseMapper, StudentBmCourse> {

	@Autowired
	private CourseMapper courseMapper;
	@Autowired
	private StudentInfoMapper studentInfoMapper;
	@Autowired
	private FaceServiceApi faceServiceApi;

	/**
	 * 学习进度统计
	 */
	public Object progress(MonitorQueryParams params, String token) throws Exception {
		List<Map<String, Object>> monitorList = mapper.progress(params.getCondition(), params);
//		for (Map<String, Object> map : monitorList) {
//			String studyProgress = xmService.getStudyProgress(map.get("courseId").toString(), map.get("studentId").toString(), token);
//			map.put("progress", studyProgress);
//		}
		params.setRecords(monitorList);
		return params;
	}

	@Transactional
	public Object monitorList(String courseId, String keyword, String xsdfw, Integer isVerify, Integer current,
			Integer size, String hostOrgId) {
		Object data = faceServiceApi.learnVerifyLog(null, null, courseId, null, null, Double.parseDouble(xsdfw.split("-")[0]),
				Double.parseDouble(xsdfw.split("-")[1]), null, isVerify, null,
				null, keyword, hostOrgId, current, size).getData();
		JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
		List<JSONObject> records = jsonObject.getJSONArray("records").stream().map(x -> {
			JSONObject map = JSON.parseObject(JSON.toJSONString(x));
			String studentId = map.getString("studentId");
			String cid = map.getString("courseId");
			StudentInfo studentInfo = studentInfoMapper.selectById(studentId);
			if(studentInfo != null){
				map.put("studentName", studentInfo.getName());
				map.put("mobile", studentInfo.getMobile());
				map.put("sfzh", studentInfo.getSfzh());
			}
			Course course = courseMapper.selectById(cid);
			if(course!=null){
				map.put("courseName", course.getName());
			}
			return map;
		}).collect(Collectors.toList());
		jsonObject.put("records", records);
		return jsonObject;
	}

	public List<Map<String, Object>> study(String studentId, String xmId) {
		return mapper.study(studentId, xmId);
	}

	/**
	 * 档案详情--学员培训总体情况统计
	 */
	public Map<String, Object> statistics(String studentId, String xmId) {
		Map<String, Object> result = new HashMap<>();
		//课程学习已完成/未完成统计
		Map<String, Object> courseStudy =  mapper.statisticCourseStudy(studentId, xmId);
		result.put("statistics", courseStudy);
		//公需课统计
		result.put("gxk", mapper.statisticGXKCourseStudy(studentId, xmId));
		//专业课统计
		result.put("zyk", mapper.statisticZYKCourseStudy(studentId, xmId));
		return result;
	}

	/**
	 * 课程报名
	 */
	@Transactional
	public List<StudentBmCourse> bmCourse(String studentId, String courseIds) {
		List<Course> courses = courseMapper.selectBatchIds(Arrays.asList(StringUtils.split(courseIds, ",")));
		List<StudentBmCourse> list = new ArrayList<>();
		for (Course course : courses) {
			StudentBmCourse studentBmCourse = new StudentBmCourse();
			if (checkCourseIsAlreadyBm(studentId, course.getId()) != null) {
				continue;
			}
			studentBmCourse.setId(BaseUtil.generateId2());
			studentBmCourse.setCourseId(course.getId());
			studentBmCourse.setStudentId(studentId);
			studentBmCourse
					.setIsPayed(course.getAmount() != null && course.getAmount() > 0 ? Constants.NO : Constants.YES);
			studentBmCourse.setCreateTime(DateUtils.now());
			studentBmCourse.setCreatorId(studentId);
			list.add(studentBmCourse);
		}
		DBUtils.insertBatch(list, StudentBmCourse.class);
		return list;
	}

	/**
	 * 检查学员是否已经报名某课程
	 */
	public StudentBmCourse checkCourseIsAlreadyBm(String studentId, String courseId) {
		EntityWrapper<StudentBmCourse> wrapper = new EntityWrapper<>();
		wrapper.eq("course_id", courseId).eq("student_id", studentId);
		List<StudentBmCourse> studentBmCourseList = mapper.selectList(wrapper);
		return studentBmCourseList.size() > 0 ? studentBmCourseList.get(0) : null;
	}

	@Transactional
	public void batchPay(String ids) {
		for (String pid : ids.split(",")) {
			StudentBmCourse studentBmCourse = this.selectById(pid);
			studentBmCourse.setIsPayed(Constants.YES);
			mapper.updateById(studentBmCourse);
		}
	}

	public Object pageQuery(StudentBmCourseQueryParams params) {
		params.setRecords(mapper.getStudentBmCourseList(params.getCondition(), params));
		return params;
	}

	public Map<String, Object> importStudentBmCourse(MultipartFile file, String courseId, String loginUserId)
			throws IOException, BiffException {
		File tmpFile = null;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			tmpFile = File.createTempFile("TEMPFILE" + BaseUtil.generateId(), ".tmp");
			file.transferTo(tmpFile);

			List<Map<String, String>> list = OfficeToolExcel.readExcel(tmpFile, new String[] { "A", "B" });

			if (list == null || list.size() < 1) {
				tmpFile.delete();
				resultMap.put("flag", false);
				resultMap.put("log", "导入失败，原因：导入文件无数据");
				return resultMap;
			}
			StringBuffer log = new StringBuffer();
			int failureNum = 0;
			int skipNum = 0;
			for (int i = 1; i < list.size(); i++) {
				if (BaseUtil.isEmpty(list.get(i).get("A"))) {
					String msg = "第" + (i) + "行的身份证号数据为空。\n";
					System.out.print(msg);
					log.append(msg).append("<br/>");
					failureNum++;
					continue;
				}
				if (BaseUtil.isEmpty(list.get(i).get("B"))) {
					String msg = "第" + (i) + "行数据的姓名为空。\n";
					System.out.print(msg);
					log.append(msg).append("<br/>");
					failureNum++;
					continue;
				}
				System.out.println(list.get(i).get("A") + "  " + list.get(i).get("B"));

				StudentInfo studentInfo = new StudentInfo();
				studentInfo.setName(list.get(i).get("B"));
				studentInfo.setSfzh(list.get(i).get("A"));
				studentInfo = studentInfoMapper.selectOne(studentInfo);

				if (studentInfo != null) {
					int count = mapper.selectCount(new EntityWrapper<StudentBmCourse>()
							.eq("student_id", studentInfo.getId()).eq("course_id", courseId));

					if (count > 0) {
						String msg = "第" + (i) + "行学员已经报名了您选择的课程，自动跳过。\n";
						System.out.print(msg);
						log.append(msg).append("<br/>");
						skipNum++;
						continue;
					}
					StudentBmCourse studentBmCourse = new StudentBmCourse();
					studentBmCourse.setStudentId(studentInfo.getId());
					studentBmCourse.setCourseId(courseId);
					studentBmCourse.setId(BaseUtil.generateId2());
					studentBmCourse.setIsPayed(Constants.YES);
					studentBmCourse.setCreatorId(loginUserId);
					studentBmCourse.setCreateTime(new Date());
					mapper.insert(studentBmCourse);
				} else {
					String msg = "第" + (i) + "行学员在系统中尚未注册，请先注册。\n";
					System.out.print(msg);
					log.append(msg).append("<br/>");
					failureNum++;
				}
			}

			resultMap.put("log", "导入完毕，共成功导入" + (list.size() - 1- failureNum-skipNum) + "条,忽略"+skipNum+"条,失败"+failureNum+"条<br/>" + log);

		}
		finally {
			try {
				// 删除上传的文件
				tmpFile.delete();
			} catch (Exception e) {

			}
		}
		return resultMap;
	}

}
