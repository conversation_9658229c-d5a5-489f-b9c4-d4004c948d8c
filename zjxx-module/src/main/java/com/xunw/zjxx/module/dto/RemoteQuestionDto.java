package com.xunw.zjxx.module.dto;

import com.xunw.zjxx.module.rpc.ExamService.enums.QuestionType;
import com.xunw.zjxx.module.sys.entity.StudentInfo;

import java.util.List;

public class RemoteQuestionDto {
    // 试题编号
    private String id;
    // 试题类型
    private QuestionType type;
    // 试题题干
    private String content;
    // 答案
    private String answer;
    // 试题解析
    private String analysis;
    private String realType;
    private String difficulty;
    // 分数
    private Integer score = 0;
    private String dbId;
    private String appId;

    private String source;

    private String isCorrected;

    private String status;

    private Double collectP;//错误率
    private Long collectCount;//错误人数
    private Long submitCount;//答题总人数
    private List<StudentInfo> students;//答题总人数

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public QuestionType getType() {
        return type;
    }

    public void setType(QuestionType type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getAnalysis() {
        return analysis;
    }

    public void setAnalysis(String analysis) {
        this.analysis = analysis;
    }

    public String getRealType() {
        return realType;
    }

    public void setRealType(String realType) {
        this.realType = realType;
    }

    public String getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public String getDbId() {
        return dbId;
    }

    public void setDbId(String dbId) {
        this.dbId = dbId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getIsCorrected() {
        return isCorrected;
    }

    public void setIsCorrected(String isCorrected) {
        this.isCorrected = isCorrected;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Double getCollectP() {
        return collectP;
    }

    public void setCollectP(Double collectP) {
        this.collectP = collectP;
    }

    public Long getCollectCount() {
        return collectCount;
    }

    public void setCollectCount(Long collectCount) {
        this.collectCount = collectCount;
    }

    public Long getSubmitCount() {
        return submitCount;
    }

    public void setSubmitCount(Long submitCount) {
        this.submitCount = submitCount;
    }

    public List<StudentInfo> getStudents() {
        return students;
    }

    public void setStudents(List<StudentInfo> students) {
        this.students = students;
    }
}
