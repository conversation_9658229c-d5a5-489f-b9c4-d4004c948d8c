package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

/**
 * 资源(权限)表
 *
 */
@TableName("sys_resource")
public class Resource{

	/**权限ID
	*/
	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;
	
	/**权限代码
	*/
	@TableField("code")
	private String code;
	
	/**权限名称
	*/
	@TableField("name")
	private String name;
	
	/**描述
	*/
	@TableField("remark")
	private String remark;
	
	@TableField("icon")
	private String icon;
	
	/**
	 * Constants PrivilegeType 常量定义 1,接口 2.菜单
	*/
	@TableField("type")
	private String type;

	@TableField("url")
	private String url;

	@TableField("sort")
	private Integer sort;

	/**
	 * 上级ID
	*/
	@TableField("parent_id")
	private String parentId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

}
