package com.xunw.zjxx.module.sys.params;

import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.module.enums.Status;

public class StudentUserQueryParams extends BaseQueryParams {
    private String keyword;
    private String orgId;
    private String professionalTechnical;
    private String titleSeries;
    private Status status;
    private String hostOrgId;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getProfessionalTechnical() {
        return professionalTechnical;
    }

    public void setProfessionalTechnical(String professionalTechnical) {
        this.professionalTechnical = professionalTechnical;
    }

    public String getTitleSeries() {
        return titleSeries;
    }

    public void setTitleSeries(String titleSeries) {
        this.titleSeries = titleSeries;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }
}
