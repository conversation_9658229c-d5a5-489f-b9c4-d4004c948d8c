package com.xunw.zjxx.module.inf.mapper;

import java.util.List;
import java.util.Map;

import com.xunw.zjxx.module.inf.vo.PracticeVo;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.dto.CourseDto;
import com.xunw.zjxx.module.inf.entity.Course;

/**
* <AUTHOR>
* @description 针对表【inf_course】的数据库操作Mapper
* @createDate 2023-08-08 13:32:09
*/
public interface CourseMapper extends BaseMapper<Course> {

    public List<Map<String, Object>> list(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> pageQuery(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> selectPageList(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> getBmCourseByStudentId(@Param("studentId") String studentId, @Param("isPayed") String isPayed);

    List<Map<String, Object>> myCourseList(Map<String, Object> condition, Page<?> page);

    List<PracticeVo> getCoursePartice(@Param("courseId") String courseId, @Param("studentId") String studentId);

    Integer doParticeCount(@Param("courseId") String courseId, @Param("loginUserId") String loginUserId, @Param("category") String category);

    Integer doParticeCountByPaperIds(@Param("paperIds") List<String> paperIds, @Param("loginUserId") String loginUserId, @Param("category") String category);

    List<Map<String, Object>> getDataByPaperId(@Param("paperId") String paperId);
}




