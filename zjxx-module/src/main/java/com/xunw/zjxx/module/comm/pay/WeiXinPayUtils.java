package com.xunw.zjxx.module.comm.pay;


import com.xunw.zjxx.common.utils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.HttpsURLConnection;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

/**
 * 微信支付工具类
 *
 * <AUTHOR>
 */
public class WeiXinPayUtils {

	private static final Logger LOG = LoggerFactory.getLogger(WeiXinPayUtils.class);

	// 微信统一下单的地址
	public static final String UNIFIEDORDER_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";

	/**
	 * 发送xml数据,获取返回结果
	 *
	 * @param requestUrl
	 * @param requestMethod
	 * @param xmlStr
	 */
	public static Map<String, Object> httpXmlRequest(String requestUrl, String requestMethod, String xmlStr) {
		// 将解析结果存储在HashMap中
		Map<String, Object> map = new HashMap<String, Object>();
		try {
			HttpsURLConnection urlCon = (HttpsURLConnection) (new URL(requestUrl)).openConnection();
			urlCon.setDoInput(true);
			urlCon.setDoOutput(true);
			// 设置请求方式（GET/POST）
			urlCon.setRequestMethod(requestMethod);
			if ("GET".equalsIgnoreCase(requestMethod)) {
				urlCon.connect();
			}
			urlCon.setRequestProperty("Content-Length", String.valueOf(xmlStr.getBytes().length));
			urlCon.setUseCaches(false);
			// 设置为gbk可以解决服务器接收时读取的数据中文乱码问题
			if (null != xmlStr) {
				OutputStream outputStream = urlCon.getOutputStream();
				outputStream.write(xmlStr.getBytes("UTF-8"));
				outputStream.flush();
				outputStream.close();
			}
			InputStream inputStream = urlCon.getInputStream();
			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
			// 读取输入流
			SAXReader reader = new SAXReader();
			Document document = reader.read(inputStreamReader);
			// 得到xml根元素
			Element root = document.getRootElement();
			// 得到根元素的所有子节点
			@SuppressWarnings("unchecked")
			List<Element> elementList = root.elements();
			// 遍历所有子节点
			for (Element e : elementList) {
				map.put(e.getName(), e.getText());
			}
			inputStreamReader.close();
			inputStream.close();
			inputStream = null;
			urlCon.disconnect();
		} catch (MalformedURLException e) {
			LOG.error(e.getMessage());
		} catch (IOException e) {
			LOG.error(e.getMessage());
		} catch (Exception e) {
			LOG.error(e.getMessage());
		}
		return map;
	}

	/**
	 * 生成预支付XML
	 *
	 * @param weiXinPrePay
	 * @param partnerKey
	 * @return
	 */
	public static String getPrePayXml(WeiXinPrePay weiXinPrePay, String partnerKey) {
		getPrePaySign(weiXinPrePay, partnerKey);// 生成预支付请求签名
		StringBuilder sb = new StringBuilder();
		sb.append("<xml><appid>").append(weiXinPrePay.getAppid()).append("</appid>");
		sb.append("<body>").append(weiXinPrePay.getBody()).append("</body>");
		sb.append("<device_info>").append(weiXinPrePay.getDeviceInfo()).append("</device_info>");
		sb.append("<mch_id>").append(weiXinPrePay.getMchId()).append("</mch_id>");
		sb.append("<nonce_str>").append(weiXinPrePay.getNonceStr()).append("</nonce_str>");
		sb.append("<notify_url>").append(weiXinPrePay.getNotifyUrl()).append("</notify_url>");
		if (WeiXinTradeTypeEnum.JSAPI.equals(weiXinPrePay.getTradeType())) {
			sb.append("<openid>").append(weiXinPrePay.getOpenid()).append("</openid>");
		} else {
			sb.append("<product_id>").append(weiXinPrePay.getProductId()).append("</product_id>");
		}
		sb.append("<out_trade_no>").append(weiXinPrePay.getOutTradeNo()).append("</out_trade_no>");
		sb.append("<spbill_create_ip>").append(weiXinPrePay.getSpbillCreateIp()).append("</spbill_create_ip>");
		sb.append("<time_start>").append(weiXinPrePay.getTimeStart()).append("</time_start>");
		sb.append("<time_expire>").append(weiXinPrePay.getTimeExpire()).append("</time_expire>");
		sb.append("<total_fee>").append(weiXinPrePay.getTotalFee()).append("</total_fee>");
		sb.append("<trade_type>").append(weiXinPrePay.getTradeType().name()).append("</trade_type>");
		sb.append("<sign>").append(weiXinPrePay.getSign()).append("</sign>");
		sb.append("</xml>");

		return sb.toString();
	}

	public static boolean checkNotifySign(Map<String, String> result, String sign, String partnerKey) {
		String argNotifySign = getStringByStringMap(result) + "&key=" + partnerKey;
		String notifySign = MD5Util.encode(argNotifySign).toUpperCase();
		if (notifySign.equals(sign)) {
			return true;
		} else {
			return false;
		}
	}

	public static String geWeiXinOrderQuerySign(String appid, String mch_id, String outTradeNo, String partnerKey,
			String nonce_str) {
		Map<String, Object> preParams = new HashMap<String, Object>();
		if (!StringUtils.isEmpty(appid)) {
			preParams.put("appid", appid);
		}
		if (!StringUtils.isEmpty(mch_id)) {
			preParams.put("mch_id", mch_id);
		}
		preParams.put("nonce_str", nonce_str);
		if (!StringUtils.isEmpty(outTradeNo)) {
			preParams.put("out_trade_no", outTradeNo);
		}
		String argPreSign = getStringByMap(preParams) + "&key=" + partnerKey;
		String preSign = MD5Util.encode(argPreSign).toUpperCase();
		return preSign;
	}

	/**
	 * 获取预支付请求签名
	 *
	 * @param weiXinPrePay
	 * @param partnerKey
	 *            微信商户平台设置的API 秘钥
	 * @return
	 */
	private static void getPrePaySign(WeiXinPrePay weiXinPrePay, String partnerKey) {
		Map<String, Object> prePayMap = new HashMap<String, Object>();
		prePayMap.put("appid", weiXinPrePay.getAppid());// 公众账号ID
		prePayMap.put("mch_id", weiXinPrePay.getMchId()); // 商户号
		prePayMap.put("device_info", weiXinPrePay.getDeviceInfo());
		prePayMap.put("nonce_str", weiXinPrePay.getNonceStr()); // 随机字符串
		prePayMap.put("body", weiXinPrePay.getBody()); // 商品描述
		prePayMap.put("out_trade_no", weiXinPrePay.getOutTradeNo()); // 商户订单号
		prePayMap.put("total_fee", weiXinPrePay.getTotalFee()); // 总金额
		prePayMap.put("spbill_create_ip", weiXinPrePay.getSpbillCreateIp()); // 终端IP
		prePayMap.put("time_start", weiXinPrePay.getTimeStart()); // 开始时间
		prePayMap.put("time_expire", weiXinPrePay.getTimeExpire()); // 截止时间
		prePayMap.put("notify_url", weiXinPrePay.getNotifyUrl()); // 接收财付通通知的URL
		prePayMap.put("trade_type", weiXinPrePay.getTradeType().name()); // 交易类型
		if (WeiXinTradeTypeEnum.JSAPI.equals(weiXinPrePay.getTradeType())) {
			prePayMap.put("openid", weiXinPrePay.getOpenid()); // openid
		} else {
			prePayMap.put("product_id", weiXinPrePay.getProductId()); // 商品ID
		}
		String argPreSign = getStringByMap(prePayMap) + "&key=" + partnerKey;
		String preSign = MD5Util.encode(argPreSign).toUpperCase();
		weiXinPrePay.setSign(preSign);
	}

	public static String getStringByMap(Map<String, Object> map) {
		SortedMap<String, Object> smap = new TreeMap<String, Object>(map);
		StringBuffer sb = new StringBuffer();
		for (Map.Entry<String, Object> m : smap.entrySet()) {
			sb.append(m.getKey()).append("=").append(m.getValue()).append("&");
		}
		sb.delete(sb.length() - 1, sb.length());
		return sb.toString();
	}

	public static String getStringByStringMap(Map<String, String> map) {
		SortedMap<String, Object> smap = new TreeMap<String, Object>(map);
		StringBuffer sb = new StringBuffer();
		for (Map.Entry<String, Object> m : smap.entrySet()) {
			sb.append(m.getKey()).append("=").append(m.getValue()).append("&");
		}
		sb.delete(sb.length() - 1, sb.length());
		return sb.toString();
	}

	/**
	 * 解析微信发来的请求（XML）
	 *
	 * @param inputStream
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, String> parseXml(InputStream inputStream) throws Exception {
		if (inputStream == null) {
			return null;
		}
		// XML外部实体注入漏洞(XML External Entity Injection，简称 XXE)
		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		String FEATURE = null;
		// This is the PRIMARY defense. If DTDs (doctypes) are disallowed,
		// almost all XML entity attacks are prevented
		// Xerces 2 only -
		// http://xerces.apache.org/xerces2-j/features.html#disallow-doctype-decl
		FEATURE = "http://apache.org/xml/features/disallow-doctype-decl";
		dbf.setFeature(FEATURE, true);

		// If you can't completely disable DTDs, then at least do the following:
		// Xerces 1 -
		// http://xerces.apache.org/xerces-j/features.html#external-general-entities
		// Xerces 2 -
		// http://xerces.apache.org/xerces2-j/features.html#external-general-entities
		// JDK7+ - http://xml.org/sax/features/external-general-entities
		FEATURE = "http://xml.org/sax/features/external-general-entities";
		dbf.setFeature(FEATURE, false);

		// Xerces 1 -
		// http://xerces.apache.org/xerces-j/features.html#external-parameter-entities
		// Xerces 2 -
		// http://xerces.apache.org/xerces2-j/features.html#external-parameter-entities
		// JDK7+ - http://xml.org/sax/features/external-parameter-entities
		FEATURE = "http://xml.org/sax/features/external-parameter-entities";
		dbf.setFeature(FEATURE, false);

		// Disable external DTDs as well
		FEATURE = "http://apache.org/xml/features/nonvalidating/load-external-dtd";
		dbf.setFeature(FEATURE, false);

		// and these as well, per Timothy Morgan's 2014 paper: "XML Schema, DTD,
		// and Entity Attacks"
		dbf.setXIncludeAware(false);
		dbf.setExpandEntityReferences(false);
		// And, per Timothy Morgan: "If for some reason support for inline
		// DOCTYPEs are a requirement, then
		// ensure the entity settings are disabled (as shown above) and beware
		// that SSRF attacks
		// (http://cwe.mitre.org/data/definitions/918.html) and denial
		// of service attacks (such as billion laughs or decompression bombs via
		// "jar:") are a risk."

		// remaining parser logic
		DocumentBuilder safebuilder = dbf.newDocumentBuilder();
		org.w3c.dom.Document documentSrc = safebuilder.parse(inputStream);
		org.dom4j.io.DOMReader xmlReader = new org.dom4j.io.DOMReader();
		Document document = (xmlReader.read(documentSrc));
		Map<String, String> map = new HashMap<String, String>();// 将解析结果存储在HashMap中
		Element root = document.getRootElement();// 得到xml根元素
		List<Element> elementList = root.elements();// 得到根元素的所有子节点
		for (Element e : elementList) { // 遍历所有子节点
			map.put(e.getName(), e.getText());
		}
		inputStream.close(); // 释放资源
		inputStream = null;
		return map;
	}

	public static Map<String, String> parseXmlOld(InputStream inputStream) throws Exception {
		if (inputStream == null) {
			return null;
		}
		Map<String, String> map = new HashMap<String, String>();// 将解析结果存储在HashMap中
		SAXReader reader = new SAXReader();// 读取输入流
		Document document = reader.read(inputStream);
		Element root = document.getRootElement();// 得到xml根元素
		List<Element> elementList = root.elements();// 得到根元素的所有子节点
		for (Element e : elementList) { // 遍历所有子节点
			map.put(e.getName(), e.getText());
		}
		inputStream.close(); // 释放资源
		inputStream = null;
		return map;
	}
}
