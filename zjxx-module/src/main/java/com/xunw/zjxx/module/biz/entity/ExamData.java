package com.xunw.zjxx.module.biz.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;
import java.util.Date;

/**
 * 已做考试练习表
 * @TableName biz_exam_data
 */
@TableName(value ="biz_exam_data")
public class ExamData implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 试卷id
     */
    @TableField(value = "paper_id")
    private String paperId;

    /**
     * 学员id
     */
    @TableField(value = "student_id")
    private String studentId;

    /**
     * 作答开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 作答结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;

    /**
     * 是否已交卷 默认0  0未提交 1已提交
     */
    @TableField(value = "is_submit")
    private Integer isSubmit;

    /**
     * 考试得分
     */
    @TableField(value = "score")
    private Integer score;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 试卷id
     */
    public String getPaperId() {
        return paperId;
    }

    /**
     * 试卷id
     */
    public void setPaperId(String paperId) {
        this.paperId = paperId;
    }

    /**
     * 学员id
     */
    public String getStudentId() {
        return studentId;
    }

    /**
     * 学员id
     */
    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    /**
     * 作答开始时间
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * 作答开始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * 作答结束时间
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * 作答结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * 是否已交卷 默认0  0未提交 1已提交
     */
    public Integer getIsSubmit() {
        return isSubmit;
    }

    /**
     * 是否已交卷 默认0  0未提交 1已提交
     */
    public void setIsSubmit(Integer isSubmit) {
        this.isSubmit = isSubmit;
    }

    /**
     * 考试得分
     */
    public Integer getScore() {
        return score;
    }

    /**
     * 考试得分
     */
    public void setScore(Integer score) {
        this.score = score;
    }
}