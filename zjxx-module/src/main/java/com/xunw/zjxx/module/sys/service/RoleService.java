package com.xunw.zjxx.module.sys.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.DBUtils;
import com.xunw.zjxx.module.enums.RoleEnum;
import com.xunw.zjxx.module.sys.entity.Resource;
import com.xunw.zjxx.module.sys.entity.Role;
import com.xunw.zjxx.module.sys.entity.RoleResource;
import com.xunw.zjxx.module.sys.mapper.ResourceMapper;
import com.xunw.zjxx.module.sys.mapper.RoleMapper;
import com.xunw.zjxx.module.sys.mapper.RoleResourceMapper;
import com.xunw.zjxx.module.sys.mapper.UserRoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RoleService extends BaseCRUDService<RoleMapper, Role> {
	
	@Autowired
	private RoleResourceMapper roleResourceMapper;
	@Autowired
	private UserRoleMapper userRoleMapper;
	@Autowired
	private ResourceMapper resourceMapper;

	public String getRoleId(RoleEnum roleEnum){
		EntityWrapper<Role> wrapper = new EntityWrapper();
		wrapper.eq("code", roleEnum.name());
		List<Role> list = mapper.selectList(wrapper);
		return list.size() > 0 ? list.get(0).getId() : null;
	}

	/**
	 * 查询资源是否被使用
	 */
	public boolean isResourceUsed(List<String> resourceIds) {
		EntityWrapper<RoleResource> wrapper = new EntityWrapper();
		wrapper.in("resource_id", resourceIds);
		return roleResourceMapper.selectCount(wrapper) > 0;
	}

	/**
	 *  授权
	 */
	public void setPrivilege(String roleId, String[] resourceIds) {
		EntityWrapper<RoleResource> wrapper = new EntityWrapper();
		wrapper.eq("role_id", roleId);
		roleResourceMapper.delete(wrapper);
		List<RoleResource> list = new ArrayList<>();
		for (String resourceId : resourceIds) {
			RoleResource roleResource = new RoleResource();
			roleResource.setId(BaseUtil.generateId2());
			roleResource.setRoleId(roleId);
			roleResource.setResourceId(resourceId);
			list.add(roleResource);
		}
		DBUtils.insertBatch(list, RoleResource.class);
	}
	
	/**
	 * 根据角色ID查询所有权限
	 */
	public List<Resource> findPrivilegeByRoleId(String roleId){
		EntityWrapper<RoleResource> wrapper = new EntityWrapper();
		wrapper.eq("role_id", roleId);
		List<RoleResource> list = roleResourceMapper.selectList(wrapper);
		List<Resource> resources = Collections.EMPTY_LIST;
		if (list.size() > 0) {
			EntityWrapper<Resource> resourceWrapper = new EntityWrapper();
			resourceWrapper.in("id", list.stream().map(x->x.getResourceId()).collect(Collectors.toList()));
			resources = resourceMapper.selectList(resourceWrapper);
		}
		return resources;
	}

	@Transactional
	public void setPrivilegeByCode(String roleId, String codes) {
		EntityWrapper<RoleResource> wrapper = new EntityWrapper();
		wrapper.eq("role_id", roleId);
		roleResourceMapper.delete(wrapper);
		List<RoleResource> list = new ArrayList<>();
		for (String code : codes.split(",")) {
			Resource resource = resourceMapper.getByCode(code);
			RoleResource roleResource = new RoleResource();
			roleResource.setId(BaseUtil.generateId2());
			roleResource.setRoleId(roleId);
			roleResource.setResourceId(resource.getId());
			list.add(roleResource);
		}
		DBUtils.insertBatch(list, RoleResource.class);
	}
}
