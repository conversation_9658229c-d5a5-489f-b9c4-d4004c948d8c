package com.xunw.zjxx.module.dto;

import java.io.Serializable;

public class CoursewareVo implements Serializable {
    private static final long serialVersionUID = 5881859423131390439L;

    private String id;
    private String courseId;

    private String lecturer;

    private String lecturerPhoto;

    private Integer star;

    private String status;

    private String source;

    private String remark;

    private Integer isTest;

    private String orgId;

    private Integer extNum;

    private String chapters;

    public CoursewareVo() {
    }

    public CoursewareVo(String courseId, String lecturer, String lecturerPhoto, Integer star, String status, String source, String remark, Integer isTest, String orgId, Integer extNum, String chapters) {
        this.courseId = courseId;
        this.lecturer = lecturer;
        this.lecturerPhoto = lecturerPhoto;
        this.star = star;
        this.status = status;
        this.source = source;
        this.remark = remark;
        this.isTest = isTest;
        this.orgId = orgId;
        this.extNum = extNum;
        this.chapters = chapters;
    }

    public CoursewareVo(String id, String courseId, String lecturer, String lecturerPhoto, Integer star, String status, String source, String remark, Integer isTest, String orgId, Integer extNum, String chapters) {
        this.id = id;
        this.courseId = courseId;
        this.lecturer = lecturer;
        this.lecturerPhoto = lecturerPhoto;
        this.star = star;
        this.status = status;
        this.source = source;
        this.remark = remark;
        this.isTest = isTest;
        this.orgId = orgId;
        this.extNum = extNum;
        this.chapters = chapters;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getLecturer() {
        return lecturer;
    }

    public void setLecturer(String lecturer) {
        this.lecturer = lecturer;
    }

    public String getLecturerPhoto() {
        return lecturerPhoto;
    }

    public void setLecturerPhoto(String lecturerPhoto) {
        this.lecturerPhoto = lecturerPhoto;
    }

    public Integer getStar() {
        return star;
    }

    public void setStar(Integer star) {
        this.star = star;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsTest() {
        return isTest;
    }

    public void setIsTest(Integer isTest) {
        this.isTest = isTest;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getExtNum() {
        return extNum;
    }

    public void setExtNum(Integer extNum) {
        this.extNum = extNum;
    }

    public String getChapters() {
        return chapters;
    }

    public void setChapters(String chapters) {
        this.chapters = chapters;
    }
}
