package com.xunw.zjxx.module.sys.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.OfficeToolExcel;
import com.xunw.zjxx.module.enums.*;
import com.xunw.zjxx.module.inf.entity.Type;
import com.xunw.zjxx.module.inf.mapper.TypeMapper;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.mapper.StudentInfoMapper;
import com.xunw.zjxx.module.sys.params.StudentUserQueryParams;
import io.seata.spring.annotation.GlobalTransactional;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.util.*;

/**
* <AUTHOR>
* @description 针对表【sys_student_info】的数据库操作Service
* @createDate 2023-08-08 13:32:10
*/
@Service
public class StudentInfoService extends BaseCRUDService<StudentInfoMapper, StudentInfo> {

    @Autowired
    private TypeMapper typeMapper;
    @Autowired
    private OrgService orgService;

    public Page pageQuery(StudentUserQueryParams params) {
        params.setRecords(mapper.pageQuery(params.getCondition(), params));
        return params;
    }

    @Transactional
    public Object registStudent(StudentInfo studentInfo) throws Exception{
    	if (this.isSfzhExists(studentInfo.getSfzh())) {
			throw BizException.withMessage("身份证号已经被注册，请更换身份证号");
		}
    	if (this.isMobileExists(studentInfo.getMobile())) {
    		throw BizException.withMessage("手机号已经被注册，请更换手机号");
    	}
        studentInfo.setId(BaseUtil.generateId2());
        super.insert(studentInfo);
        return studentInfo;
    }

    @Transactional
    public Object infoEdit(String studentId, String mobile, Education education, String orgId, ZwEnum zw, PoliticalType politicalType, String workUnit,ZcEnum zc, String titleSeries,String studentPhoto, String hostOrgId) throws Exception {
        Optional<StudentInfo> student = mapper.selectList(new EntityWrapper<StudentInfo>()
                .eq("mobile", mobile)
                .eq("host_org_id", hostOrgId)).stream().findFirst();
        if (student.isPresent() && !Objects.equals(student.get().getId(), studentId)) {
            throw BizException.withMessage("手机号已经被注册，请更换手机号");
        }
        StudentInfo studentInfo = mapper.selectById(studentId);
        if (studentInfo == null) {
            throw BizException.withMessage("用户不存在");
        }
        studentInfo.setMobile(mobile);
        studentInfo.setEducation(education);
        studentInfo.setZw(zw);
        studentInfo.setZc(zc);
        studentInfo.setPoliticalType(politicalType);
        studentInfo.setWorkUnit(workUnit);
        studentInfo.setTitleSeries(titleSeries);
        studentInfo.setStudentPhoto(studentPhoto);
        studentInfo.setOrgId(orgId);
        super.updateById(studentInfo);
        return studentInfo;
    }

    @Transactional
    public Object getById(String id) throws Exception {
        StudentInfo studentInfo = mapper.selectById(id);
        Map<String, Object> result = new HashMap<>();
        if (studentInfo != null) {
//            String titleSeries = studentInfo.getTitleSeries();
//            Type type = typeMapper.selectById(titleSeries);
            result.put("id", id);
            result.put("name", studentInfo.getName());
            result.put("idType", studentInfo.getCertiType());
            result.put("idstring", studentInfo.getSfzh());
            result.put("mobile", studentInfo.getMobile());
            result.put("orgId", studentInfo.getOrgId());
            Org org = orgService.selectById(studentInfo.getOrgId());
            result.put("orgName", org != null ? org.getName() : null);
            result.put("birthday", studentInfo.getBirthday());
            result.put("sex", studentInfo.getGender());
            result.put("education", studentInfo.getEducation());
            result.put("jobLevel", studentInfo.getZw());
            result.put("politicsStatus", studentInfo.getPoliticalType());
            result.put("workUnit", studentInfo.getWorkUnit());
            result.put("professionalTitles", studentInfo.getZc());
//            result.put("titleSeries", type.getName());
            result.put("createTime", studentInfo.getCreateTime());
            result.put("status", studentInfo.getStatus());
        }
        return result;
    }

    @Transactional
    public void enable(String id, Status status) throws Exception {
        StudentInfo studentInfo = mapper.selectById(id);
        if (studentInfo == null){
            throw BizException.withMessage("学员不存在");
        }
        studentInfo.setStatus(status);
        mapper.updateById(studentInfo);
    }

    @Transactional
    @GlobalTransactional
    public void resetPassword(String id) throws Exception {
        StudentInfo studentInfo = mapper.selectById(id);
        if (studentInfo == null){
            throw BizException.withMessage("学员不存在");
        }
        studentInfo.setPassword(DigestUtils.md5Hex(Constants.DEFAULT_PASSWORD));
        mapper.updateById(studentInfo);
    }

    /**
     * 校验身份证号是否存在
     */
    public boolean isSfzhExists(String sfzh) {
    	EntityWrapper<StudentInfo> wrapper = new EntityWrapper();
    	wrapper.eq("sfzh", sfzh);
    	return mapper.selectCount(wrapper) > 0;
    }

    /**
     * 校验身份证号是否存在
     */
    public boolean isMobileExists(String mobile) {
    	EntityWrapper<StudentInfo> wrapper = new EntityWrapper();
    	wrapper.eq("mobile", mobile);
    	return mapper.selectCount(wrapper) > 0;
    }

    /**
     * 校验身份证号是否存在
     */
    public StudentInfo getByOpenId(String openId) {
        EntityWrapper<StudentInfo> wrapper = new EntityWrapper();
        wrapper.eq("open_id", openId);
        List<StudentInfo> list = mapper.selectList(wrapper);
        return list.size() > 0 ? list.get(0) : null;
    }
    
    /**
     * 根据身份证号或者手机号查询学员
     */
    public StudentInfo findBySfzhOrMobile(String account) {
    	EntityWrapper<StudentInfo> wrapper = new EntityWrapper();
    	wrapper.eq("mobile", account).or().eq("sfzh", account);
    	List<StudentInfo> studentInfos = mapper.selectList(wrapper);
    	return studentInfos.size() > 0 ? studentInfos.get(0) : null;
    }
    
    /**
     * 学员用户导出
     */
    public void export(StudentUserQueryParams params, OutputStream os) throws Exception {
        params.setCurrent(1);
        params.setSize(Integer.MAX_VALUE);
        List<Map<String, Object>> query = mapper.pageQuery(params.getCondition(), params);
        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("学员信息列表", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "证件号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "手机号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "注册时间", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "状态", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
        }
        row = 1;
        int i = 1;
        for (Map<String, Object> map : query) {
            int col = 0;
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("name") != null ? map.get("name") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("idstring") != null ? map.get("idstring") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("mobile") != null ? map.get("mobile") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("createTime") != null ? map.get("createTime") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(Status.findByEnumName(map.get("status").toString()).getName()),
                    OfficeToolExcel.getNormolCell()));
            row++;
        }
        wwb.write();
        wwb.close();
        os.flush();

    }
}
