package com.xunw.zjxx.module.sys.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.entity.Bank;
import com.xunw.zjxx.module.sys.mapper.BankMapper;
import com.xunw.zjxx.module.sys.mapper.OrgMapper;
import com.xunw.zjxx.module.sys.params.BankQueryParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @createDate 2023-08-08 13:32:09
*/
@Service
public class BankService extends BaseCRUDService<BankMapper, Bank> {

    @Autowired
    private OrgMapper orgMapper;

    /**
     * 分页查询
     */
    public Page pageQuery(BankQueryParams params) {
        EntityWrapper<Bank> wrapper = new EntityWrapper<Bank>();
        if (StringUtils.isNotEmpty(params.getHostOrgId())) {
            wrapper.eq("host_org_id", params.getHostOrgId());
        }
        return this.selectPage(params, wrapper);
    }

    /**
     * 获取主办单位的默认收款方
     */
    public Bank getDefaultBank(String hostOrgId) {
        EntityWrapper<Bank> wrapper = new EntityWrapper();
        wrapper.eq("host_org_id", hostOrgId);
        wrapper.eq("is_default", Constants.YES);
        wrapper.eq("status", Status.OK);
        List<Bank> banks = mapper.selectList(wrapper);
        return banks.size() > 0 ? banks.get(0) : null;
    }

    /**
     * 获取收款方信息
     */
    public Page getBankList(String token) throws Exception {
        BankQueryParams bankQueryParams = new BankQueryParams();
        List<Map<String, Object>> bankInfo = mapper.getBankList(bankQueryParams.getCondition(), bankQueryParams);
        return bankQueryParams.setRecords(bankInfo);
    }
}
