package com.xunw.zjxx.module.inf.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.Status;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName inf_course
 */
@TableName(value ="inf_course")
public class Course implements Serializable {
	
    private static final long serialVersionUID = -936145870224449065L;

	/**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 类型id
     */
    @TableField(value = "type_id")
    private String typeId;

    /**
     * 课程名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 课程学时
     */
    @TableField(value = "hours")
    private Double hours;

    /**
     * 标签,多个值用逗号隔开，不能够重复
     */
    @TableField(value = "tag")
    private String tag;

    /**
     * 宣传图
     */
    @TableField(value = "xct")
    private String xct;

    /**
     * 主办单位id
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    /**
     * 课程logo
     */
    @TableField(value = "logo")
    private String logo;

    /**
     * 费用
     */
    @TableField(value = "amount")
    private Double amount;

    /**
     * 创建用户id
     */
    @TableField(value = "creator_id")
    private String creatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改用户id
     */
    @TableField(value = "updator_id")
    private String updatorId;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 状态
     */
    @TableField(value = "status")
    private Status status;

    /**
     * 顶层类型ID
     */
    @TableField(value = "top_type_id")
    private String topTypeId;

    /**
     * 是否上传课件
     */
    @TableField(value = "is_kj_uploaded")
    private String isKjUploaded;

    /**
     * 课件状态
     */
    @TableField(value = "kj_status")
    private Status kjStatus;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getHours() {
        return hours;
    }

    public void setHours(Double hours) {
        this.hours = hours;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getXct() {
        return xct;
    }

    public void setXct(String xct) {
        this.xct = xct;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    
    public Status getStatus() {
		return status;
	}

	public void setStatus(Status status) {
		this.status = status;
	}

	public String getTopTypeId() {
        return topTypeId;
    }

    public void setTopTypeId(String topTypeId) {
        this.topTypeId = topTypeId;
    }

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

    public String getIsKjUploaded() {
        return isKjUploaded;
    }

    public void setIsKjUploaded(String isKjUploaded) {
        this.isKjUploaded = isKjUploaded;
    }

	public Status getKjStatus() {
		return kjStatus;
	}

	public void setKjStatus(Status kjStatus) {
		this.kjStatus = kjStatus;
	}
}