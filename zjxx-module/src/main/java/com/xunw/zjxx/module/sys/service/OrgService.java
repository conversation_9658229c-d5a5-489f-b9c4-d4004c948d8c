package com.xunw.zjxx.module.sys.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.CacheHelper;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.dto.OrgTree;
import com.xunw.zjxx.module.enums.Category;
import com.xunw.zjxx.module.enums.OrgType;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.inf.entity.Type;
import com.xunw.zjxx.module.inf.mapper.TypeMapper;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.entity.OrgRelation;
import com.xunw.zjxx.module.sys.entity.User;
import com.xunw.zjxx.module.sys.mapper.OrgMapper;
import com.xunw.zjxx.module.sys.mapper.OrgRelationMapper;
import com.xunw.zjxx.module.sys.mapper.UserMapper;
import com.xunw.zjxx.module.sys.params.OrgQueryParams;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrgService extends BaseCRUDService<OrgMapper, Org> {

	@Autowired
	private UserMapper userMapper;
	@Autowired
	private TypeMapper typeMapper;
    @Autowired
    private OrgRelationMapper orgRelationMapper;

    public Page list(OrgQueryParams params) {
        List<Map<String, Object>> list = mapper.query(params.getCondition(), params);
        params.setRecords(list);
        return params;
    }

    public List<OrgTree> tree(String id, OrgType orgType) {
    	EntityWrapper<Org> wrapper = new EntityWrapper<Org>();
    	if (orgType != null) {
    		wrapper.eq("org_type", orgType);
		}
        List<Org> orgs = mapper.selectList(wrapper);
        List<OrgTree> orgTrees = orgs.stream().map(x -> {
            OrgTree orgTree = new OrgTree();
            orgTree.setId(x.getId());
            orgTree.setParentId(x.getParentId());
            orgTree.setName(x.getName());
            orgTree.setType(x.getOrgType());
            orgTree.setChildren(new ArrayList<>());
            return orgTree;
        }).collect(Collectors.toList());
        List<OrgTree> tresList = new ArrayList<>();
        if (StringUtils.isNotEmpty(id)) {
        	tresList.add(this.buildTree(id, orgTrees));//只有一个根
		}
        else {
        	for (OrgTree orgTree : orgTrees) {
        		if (StringUtils.isEmpty(orgTree.getParentId())) {
        			tresList.add(this.buildTree(orgTree.getId(), orgTrees));//多个根结点
				}
        	}
        }
        return tresList;
    }

    private OrgTree buildTree(String id, List<OrgTree> list) {
        Optional<OrgTree> parentOptional = list.stream().filter(x -> x.getId().equals(id)).findFirst();
        OrgTree result = new OrgTree();
        if (parentOptional.isPresent()) {
            result = parentOptional.get();
        } else {
            return null;
        }
        //递归查询子类
        for (OrgTree orgTree : list) {
            //如果父类主键等于传过来实体类的ID
            if (Objects.equals(orgTree.getParentId(), StringUtils.isEmpty(id) ? "" : id)) {
                // 递归调用
                result.getChildren().add(buildTree(orgTree.getId(), list));
            }
        }
        return result;
    }

    public Object select(OrgType orgType) {
        EntityWrapper<Org> wrapper = new EntityWrapper<>();
        if (orgType != null) {
            wrapper.eq("org_type", orgType);
        }
        wrapper.orderBy("create_time", false);
        return mapper.selectList(wrapper);
    }

    /**
     * 获取机构下的用户数量
     */
    public Integer countUserByOrgId(String orgId) {
        EntityWrapper<User> wrapper = new EntityWrapper<>();
        wrapper.eq("org_id", orgId);
        return userMapper.selectCount(wrapper);
    }

    /**
     * 递归获取顶层机构
     */
    public Org getTopOrg(String id) {
    	Org org = mapper.selectById(id);
		if (org != null) {
			if (StringUtils.isNotEmpty(org.getParentId())) {
				return this.getTopOrg(org.getParentId());
			}
		}
		return org;
    }

    /**
     * 保存机构关系
     */
    @Transactional
    public void saveOrgRelation(String hostOrgId,String orgId) {
		OrgRelation orgRelation = new OrgRelation();
		orgRelation.setId(BaseUtil.generateId2());
		orgRelation.setHostOrgId(hostOrgId);
		orgRelation.setOrgId(orgId);
		orgRelationMapper.insert(orgRelation);
    }

    /**
     * 合作机构树状查询,多个合作机构则有多个根节点
     */
    public List<OrgTree> relatedOrgTree(String hostOrgId){
    	EntityWrapper<OrgRelation> wrapper = new EntityWrapper<OrgRelation>();
    	wrapper.eq("host_org_id", hostOrgId);
    	List<OrgRelation> relations = orgRelationMapper.selectList(wrapper);
    	List<OrgTree> result = new ArrayList<>();
    	if (relations.size() > 0) {
    		List<String> relatedOrgIds = relations.stream().map(x->x.getOrgId()).collect(Collectors.toList());
    		EntityWrapper<Org> orgWrapper = new EntityWrapper();
    		orgWrapper.in("id", relatedOrgIds);
    		orgWrapper.eq("is_parent", Constants.YES);
            orgWrapper.eq("org_type", OrgType.ORG);
    		List<Org> relateOrgs = mapper.selectList(orgWrapper);
			for (Org org : relateOrgs) {
				result.addAll(this.tree(org.getId(), OrgType.ORG));
			}
		}
    	return result;
    }

    /**
     * 保存组织机构或者其下级部门
     * @throws Exception
     */
    @Transactional
    public void addOrg(String parentId,String name,String code, String contact, String telephone, String remark, String hostOrgId) throws Exception {
        code = StringUtils.isEmpty(code) ? "ORG_" + System.currentTimeMillis() : code;
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("机构名称不能为空");
        }
        if (StringUtils.isEmpty(code)) {
            throw BizException.withMessage("机构代码不能为空");
        }
        List<Org> orgs = mapper.selectList((EntityWrapper<Org>) new EntityWrapper<Org>()
                .eq("code", code));
        if (CollectionUtils.isNotEmpty(orgs)) {
            throw BizException.withMessage("机构代码已经被使用了，请更换机构代码");
        }
        Org org = new Org();
        String orgId = BaseUtil.generateId();
        org.setId(orgId);
        org.setCode(code);
        org.setName(name);
        org.setOrgType(OrgType.ORG);
        org.setIsParent(StringUtils.isEmpty(parentId) ? Constants.YES : Constants.NO);
        org.setParentId(parentId);
        org.setStatus(Status.OK);
        org.setCreateTime(new Date());
        org.setRemark(remark);
        org.setContact(contact);
        org.setTelephone(telephone);
        mapper.insert(org);
        if (org.getOrgType() == OrgType.ORG && Constants.YES.equals(org.getIsParent())){
            OrgRelation orgRelation = new OrgRelation();
            orgRelation.setId(BaseUtil.generateId2());
            orgRelation.setHostOrgId(hostOrgId);
            orgRelation.setOrgId(orgId);
            orgRelationMapper.insert(orgRelation);
        }
    }

    /**
     * 保存主办单位部门
     * @throws Exception
     */
    @Transactional
    public void addHostOrgTop(String name,String code,String remark, String portalDomain, String smsSign, String adminDomain,String adminSysName,String portalSysName,String adminLogo,String portalLogo,String appletQrCode) throws Exception {
        code = StringUtils.isEmpty(code) ? "ORG_"+System.currentTimeMillis() : code;
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("机构名称不能为空");
        }
        if (StringUtils.isEmpty(code)) {
            throw BizException.withMessage("机构代码不能为空");
        }
        List<Org> orgs = mapper.selectList((EntityWrapper<Org>) new EntityWrapper<Org>()
                .eq("code", code));
        if (CollectionUtils.isNotEmpty(orgs)) {
            throw BizException.withMessage("机构代码已经被使用了，请更换机构代码");
        }
        Org org = new Org();
        String orgId = BaseUtil.generateId();
        org.setId(orgId);
        org.setCode(code);
        org.setName(name);
        org.setOrgType(OrgType.HOST_ORG);
        org.setAdminDomain(adminDomain);
        org.setAdminSysName(adminSysName);
        org.setAdminLogo(adminLogo);
        org.setPortalDomain(portalDomain);
        org.setPortalSysName(portalSysName);
        org.setPortalLogo(portalLogo);
        org.setAppletQrCode(appletQrCode);
        org.setSmsSign(smsSign);
        org.setIsParent(Constants.YES);
        org.setStatus(Status.OK);
        org.setCreateTime(new Date());
        org.setRemark(remark);
        mapper.insert(org);
        //初始化默认课程分类信息
        Type type1 = new Type();
        type1.setId(BaseUtil.generateId2());
        type1.setName("公需课");
        type1.setCategory(Category.GXK);
        type1.setHostOrgId(org.getId());
        typeMapper.insert(type1);

        Type type2 = new Type();
        type2.setId(BaseUtil.generateId2());
        type2.setName("专业课");
        type2.setCategory(Category.ZYK);
        type2.setHostOrgId(org.getId());
        typeMapper.insert(type2);

    }

    /**
     * 保存主办单位下级部门
     * @throws Exception
     */
    @Transactional
    public void addHostOrg(String parentId,String name,String code, String contact, String telephone, String remark) throws Exception {
        code = StringUtils.isEmpty(code) ? "ORG_"+System.currentTimeMillis() : code;
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("机构名称不能为空");
        }
        if (StringUtils.isEmpty(code)) {
            throw BizException.withMessage("机构代码不能为空");
        }
        List<Org> orgs = mapper.selectList((EntityWrapper<Org>) new EntityWrapper<Org>()
                .eq("code", code));
        if (CollectionUtils.isNotEmpty(orgs)) {
            throw BizException.withMessage("机构代码已经被使用了，请更换机构代码");
        }
        Org org = new Org();
        String orgId = BaseUtil.generateId();
        org.setId(orgId);
        org.setCode(code);
        org.setName(name);
        org.setOrgType(OrgType.HOST_ORG);
        org.setParentId(parentId);
        org.setIsParent(Constants.NO);
        org.setStatus(Status.OK);
        org.setCreateTime(new Date());
        org.setRemark(remark);
        org.setContact(contact);
        org.setTelephone(telephone);
        mapper.insert(org);
    }

    /**
     * 修改机构（任意机构，不管类型）
     * @throws Exception
     */
    @Transactional
    public void editOrg(String id,String name, String code, String contact, String telephone, String remark) throws Exception {
        Org org = mapper.selectById(id);
        if (org == null) {
            throw BizException.withMessage("机构不存在");
        }
        if (StringUtils.isNotEmpty(code)) {
            List<Org> orgs = mapper.selectList((EntityWrapper<Org>) new EntityWrapper<Org>().ne("id", id)
                    .eq("code", code));
            if (CollectionUtils.isNotEmpty(orgs)) {
                throw BizException.withMessage("机构代码已经被使用了，请更换机构代码");
            }
        }
        org.setName(name);
        org.setCode(code);
        org.setUpdateTime(new Date());
        org.setRemark(remark);
        org.setContact(contact);
        org.setTelephone(telephone);
        mapper.updateById(org);
    }

    /**
     * 修改机构（任意机构，不管类型）(主办单位)
     * @throws Exception
     */
    @Transactional
    public void editHostOrgTop(String id,String name,String code,String remark, String portalDomain, String smsSign, String adminDomain,String adminSysName,String portalSysName,String adminLogo,String portalLogo,String appletQrCode) throws Exception {
        Org org = mapper.selectById(id);
        if (org == null) {
            throw BizException.withMessage("机构不存在");
        }
        if (StringUtils.isNotEmpty(code)) {
            List<Org> orgs = mapper.selectList((EntityWrapper<Org>) new EntityWrapper<Org>().ne("id", id)
                    .eq("code", code));
            if (CollectionUtils.isNotEmpty(orgs)) {
                throw BizException.withMessage("机构代码已经被使用了，请更换机构代码");
            }
        }
        org.setName(name);
        org.setCode(code);
        org.setAdminDomain(adminDomain);
        org.setAdminSysName(adminSysName);
        org.setAdminLogo(adminLogo);
        org.setPortalDomain(portalDomain);
        org.setPortalSysName(portalSysName);
        org.setPortalLogo(portalLogo);
        org.setAppletQrCode(appletQrCode);
        org.setSmsSign(smsSign);
        org.setUpdateTime(new Date());
        org.setRemark(remark);
        mapper.updateById(org);
        //更新缓存信息
        CacheHelper.removeCache(Constants.HOST_ORG_DOMAIN_CACHE);
    }

    public Org getHostOrgByDomain(String domain)  {
        if (StringUtils.isEmpty(domain)) {
            throw BizException.withMessage("域名不能为空");
        }
        EntityWrapper<Org> orgWraper =  new EntityWrapper<>();
        orgWraper.eq("is_parent", Constants.YES)
                .eq("status", Status.OK)
                .eq("org_type", OrgType.HOST_ORG);
        List<Org> orgs = mapper.selectList(orgWraper.andNew().eq("admin_domain", domain).or().eq("portal_domain", domain));
        if (orgs.isEmpty()) {
            orgs = mapper.selectList(orgWraper.andNew().eq("admin_domain", Constants.DEFAULT_DOMAIN).or().eq("portal_domain",  Constants.DEFAULT_DOMAIN));
        }
        if (orgs.isEmpty()) {
            orgWraper = new EntityWrapper<>();
            orgWraper.eq("is_parent", Constants.YES)
                    .eq("status", Status.OK)
                    .eq("org_type", OrgType.HOST_ORG)
                    .orderBy("create_time", false);
            orgs = mapper.selectList(orgWraper);
        }
        return CollectionUtils.isEmpty(orgs) ? null : orgs.get(0);
    }

}
