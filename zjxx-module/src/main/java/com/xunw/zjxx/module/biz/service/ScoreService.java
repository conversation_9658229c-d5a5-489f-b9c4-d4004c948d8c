package com.xunw.zjxx.module.biz.service;

import com.alibaba.fastjson.JSON;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.biz.mapper.ScoreMapper;
import com.xunw.zjxx.module.biz.params.StudentBmCourseQueryParams;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.mapper.CourseMapper;
import com.xunw.zjxx.module.inf.vo.ChapterVo;
import com.xunw.zjxx.module.inf.vo.CourseDetailVo;
import com.xunw.zjxx.module.inf.vo.LessonVo;
import com.xunw.zjxx.module.rpc.LearningCoursewareServiceApi;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 成绩管理
 *
 */
@Service
public class ScoreService {
	
	@Autowired
	private ScoreMapper mapper;
	@Autowired
	private CourseMapper courseMapper;
    @Autowired
    private LearningCoursewareServiceApi learningCoursewareServiceApi;
	
	/**
	 * 单科学习成绩列表
	 */
	public Object learningScoreList(StudentBmCourseQueryParams params) {
		params.setRecords(mapper.learningScoreList(params.getCondition(), params));
		return params;
	}
	
	/**
	 * 单科学习成绩详情
	 */
    @GlobalTransactional
    @Transactional
	public Map<String, Object> learningScoreDetails(String studentId, String courseId, String orgId, String token) {
        Course course = courseMapper.selectById(courseId);
        if (course == null) {
            throw BizException.withMessage("课程不存在");
        }
        Object resp = learningCoursewareServiceApi.coursewareList(null, courseId, Status.OK.name(), null, orgId, null, 1, Integer.MAX_VALUE).getData();
        //获取课件列表
        List<Map> records = JSON.parseArray(JSON.parseObject(JSON.toJSONString(resp)).getString("records"), Map.class);
        if (CollectionUtils.isEmpty(records)) {
            throw BizException.withMessage("课件不存在");
        }
        Map courseware = records.get(0);
        String kjId = (String)courseware.get("id");
        Object coursewareResp = learningCoursewareServiceApi.coursewareGetById(kjId, null).getData();
		CourseDetailVo courseDetailVo = JSON.parseObject(JSON.toJSONString(coursewareResp)).toJavaObject(CourseDetailVo.class);
		//获取已经完成的学时
        Map<String, Object> getLearnMap = new HashMap<>();
        getLearnMap.put("coursewareId", kjId);
        getLearnMap.put("studentId", studentId);
        getLearnMap.put("batchId", Constants.UPDATE_STUDY_TIME_BATCH_ID);
        Object learn = learningCoursewareServiceApi.studyGetLearn(studentId, Constants.UPDATE_STUDY_TIME_BATCH_ID, kjId).getData();
        CourseDetailVo finishDetailVo = JSON.parseObject(JSON.toJSONString(learn)).toJavaObject(CourseDetailVo.class);
        Map<String, Object> result = new HashMap<>();
        result.put("id", course.getId());
        result.put("name", course.getName());
        result.put("totalMinutes", courseDetailVo.getChapters().stream().flatMap(x -> x.getLessons().stream()).mapToInt(x -> Integer.parseInt(x.getMinutes())).sum());
        result.put("finishMinutes", finishDetailVo.getChapters().stream().flatMap(x -> x.getLessons().stream()).mapToInt(x -> Integer.parseInt(x.getDuration())).sum());
        //处理列表数据
        List<Map<String, Object>> chapterList = new ArrayList<>();
        for (ChapterVo chapterVo : courseDetailVo.getChapters()) {
            Map<String, Object> chapterMap = new HashMap<>();
            chapterMap.put("id", chapterVo.getId());
            chapterMap.put("name", chapterVo.getName());
            List<Map<String, Object>> lessons = new ArrayList<>();
            for (LessonVo lesson : chapterVo.getLessons()) {
                Map<String, Object> hashMap = new HashMap<>();
                hashMap.put("id", lesson.getId());
                hashMap.put("name", lesson.getName());
                hashMap.put("minutes", lesson.getMinutes());
                List<LessonVo> studyedLessons = finishDetailVo.getChapters().stream().flatMap(
                		x -> x.getLessons().stream()).filter(x->x.getId().equals(lesson.getId())).collect(Collectors.toList());
                hashMap.put("duration", studyedLessons != null && studyedLessons.size() > 0 ? studyedLessons.get(0).getDuration() : 0);
                lessons.add(hashMap);
            }
            chapterMap.put("lessons", lessons);
            chapterList.add(chapterMap);
        }
        result.put("chapters", chapterList);
        return result;
    }
	
	/**
	 * 培训成绩列表
	 */
	public Object trainingScoreList(StudentBmCourseQueryParams params) {
		params.setRecords(mapper.trainingScoreList(params.getCondition(), params));
		return params;
	}
	
	/**
	 * 培训成绩详情
	 */
	public List<Map<String, Object>> trainingScoreDetails(String studentId, String xmId) {
		return mapper.trainingScoreDetails(studentId, xmId);
	}

    /**
	 * 学习计划成绩详情
	 */
	public List<Map<String, Object>> xmScore(String studentId, String xmId) {
		return mapper.xmScore(studentId, xmId);
	}
	
}
