package com.xunw.zjxx.module.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;
import java.util.Objects;

public enum Status implements IEnum {
    OK("启用","0"),
    BLOCK("禁用","1");
    
    private String name;
    private String id;

    Status(String name, String id) {
        this.name = name;
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public static Status findById(String id) {
        for (Status status : Status.values()) {
            if (Objects.equals(status.id, id)) {
                return status;
            }
        }
        return null;
    }

    public static Status findByEnumName(String name){
        for (Status status : Status.values()) {
            if (status.name().equals(name)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public Serializable getValue() {
        return this.name();
    }
}
