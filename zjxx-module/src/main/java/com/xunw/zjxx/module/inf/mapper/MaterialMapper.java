package com.xunw.zjxx.module.inf.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.inf.dto.MaterialRespDto;
import com.xunw.zjxx.module.inf.entity.Material;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface MaterialMapper extends BaseMapper<Material> {

    /**
     * 表单分页查询
     */
    public List<MaterialRespDto> list(Map<String, Object> condition, Page<?> page);

    /***
     * 修改
     */
    public boolean updateByUrl(@Param("id") String id,@Param("url") String url);

    List<Map<String, Object>> findPractice(Map<String, Object> condition, Page<?> page);

    List<Material> materialList(@Param("name") String name, @Param("courseId") String courseId, @Param("studentId") String studentId, Page<?> page);
}
