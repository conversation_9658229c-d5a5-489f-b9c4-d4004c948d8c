package com.xunw.zjxx.module.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.OfficeToolExcel;
import com.xunw.zjxx.module.biz.entity.CoursewareProgress;
import com.xunw.zjxx.module.biz.entity.ExamData;
import com.xunw.zjxx.module.biz.entity.ExamPaper;
import com.xunw.zjxx.module.biz.mapper.CoursewareProgressMapper;
import com.xunw.zjxx.module.biz.mapper.ExamDataMapper;
import com.xunw.zjxx.module.biz.mapper.ExamPaperMapper;
import com.xunw.zjxx.module.biz.params.ExamDataQueryParams;
import com.xunw.zjxx.module.biz.params.ExamMonitorQueryParams;
import com.xunw.zjxx.module.biz.params.HomeworkQueryParams;
import com.xunw.zjxx.module.dto.RemoteExamDataDto;
import com.xunw.zjxx.module.enums.PaperCategory;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.mapper.CourseMapper;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.rpc.FaceServiceApi;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.mapper.StudentInfoMapper;
import io.seata.spring.annotation.GlobalTransactional;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_exam_data(已做考试练习表)】的数据库操作Service
* @createDate 2023-08-18 13:33:27
*/
@Service
public class ExamDataService extends BaseCRUDService<ExamDataMapper, ExamData> {

    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private FaceServiceApi faceServiceApi;
    @Autowired
    private StudentInfoMapper studentInfoMapper;
    @Autowired
    private ExamPaperMapper examPaperMapper;
    @Autowired
    private CourseMapper courseMapper;
    @Autowired
    private CoursewareProgressMapper coursewareProgressMapper;

    public Page getHomeworkList(HomeworkQueryParams params, String token) throws Exception {
        List<Map<String, Object>> homeworkList = mapper.getHomeworkList(params.getCondition(), params);
        params.setRecords(homeworkList);
        return params;
    }

    /**
     * 获取练习详情
     */
    @GlobalTransactional
    @Transactional
    public JSONObject getById(String id, String studentId, String token, String hostOrgId) throws Exception {
        Object resp = examServiceApi.examList(hostOrgId, null, null, null, null, id, "HOMEWORK",
                null, studentId, null, null, null, null, 1, Integer.MAX_VALUE).getData();
        List<Map> data = JSON.parseArray(JSON.parseObject(JSONObject.toJSONString(resp)).getString("records"), Map.class);
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        //答卷id
        String examDataId = data.get(0).get("id").toString();
        return JSONObject.parseObject(JSONObject.toJSONString(examServiceApi.examGetById(examDataId).getData()));
    }

    public void export(HomeworkQueryParams params, String accessToken, OutputStream os) throws Exception {
        List<Map<String, Object>> records = this.getHomeworkList(params, accessToken).getRecords();
        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("练习列表", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "课程名称", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "练习名称", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "证件号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "练习开始时间", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "练习结束时间", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "状态", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
        }
        row = 1;
        int i = 1;
        for (Map<String, Object> map : records) {
            int col = 0;
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("courseName") != null ? map.get("courseName") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("name") != null ? map.get("name") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(
                    new Label(col++, row, BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
                            OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("documentstring") != null ? map.get("documentstring") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("startTime") != null ? map.get("startTime") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("endTime") != null ? map.get("endTime") : ""),
                    OfficeToolExcel.getNormolCell()));
            String status = null;
            if (Objects.equals(map.get("status").toString(), "0")) {
                status = "未做";
            } else if (Objects.equals(map.get("status").toString(), "1")) {
                status = "已做";
            }
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(status), OfficeToolExcel.getNormolCell()));
            row++;
        }
        wwb.write();
        wwb.close();
        os.flush();
    }

    public Page undoList(ExamDataQueryParams params) {
        params.setRecords(mapper.undoList(params.getCondition(), params));
        return params;
    }

    public List<Map<String, Object>> exam(String studentId, String xmId) {
        return mapper.exam(studentId, xmId);
    }


    public void undoExport(ExamDataQueryParams params, OutputStream os) throws Exception {
        List<Map<String, Object>> records = this.undoList(params).getRecords();
        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("考试未做记录列表", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "课程名称", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "姓名", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "证件号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
        }
        row = 1;
        int i = 1;
        for (Map<String, Object> map : records) {
            int col = 0;
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("courseName") != null ? map.get("courseName") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("studentName") != null ? map.get("studentName") : ""),
                    OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row,
                    BaseUtil.convertNullToEmpty(map.get("documentstring") != null ? map.get("documentstring") : ""),
                    OfficeToolExcel.getNormolCell()));
            row++;
        }
        wwb.write();
        wwb.close();
        os.flush();
    }

    @GlobalTransactional
    @Transactional
    public JSONObject monitorList(ExamMonitorQueryParams params) throws Exception {
        Object resp = faceServiceApi.examVerifyLog(Constants.UPDATE_STUDY_TIME_BATCH_ID, params.getCourseId(), null, null,
                StringUtils.isNotEmpty(params.getXsdfw()) ? Double.valueOf(params.getXsdfw().split("-")[0]) : null,
                StringUtils.isNotEmpty(params.getXsdfw()) ? Double.valueOf(params.getXsdfw().split("-")[1]) : null, null,
                StringUtils.isNotEmpty(params.getIsVerify()) ? Integer.parseInt(params.getIsVerify()) : null, null, null, null, params.getHostOrgId(),
                params.getCurrent(), params.getSize()).getData();
        JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(resp));
        List<Map<String, Object>> list = data.getJSONArray("records").stream().map(x -> {
            Map<String, Object> records = new HashMap<>();
            JSONObject jsonObject = JSON.parseObject(x.toString());
            List<StudentInfo> studentInfos = studentInfoMapper.selectList(new EntityWrapper<StudentInfo>()
                    .eq("id", jsonObject.getString("studentId")));
            Course course = courseMapper.selectById(jsonObject.getString("courseId"));
            records.put("id", jsonObject.getString("id"));
            records.put("studentName", studentInfos.isEmpty() ? null : studentInfos.get(0).getName());
            records.put("documentstring", studentInfos.isEmpty() ? null : studentInfos.get(0).getSfzh());
            records.put("courseName", course == null ? null : course.getName());
            records.put("verifyPhoto", jsonObject.getString("uploadPhotoPath"));
            records.put("studentPhoto", studentInfos.isEmpty() ? null : studentInfos.get(0).getStudentPhoto());
            records.put("result", jsonObject.getString("confidence"));
            records.put("identifyTime", jsonObject.getString("createTime"));
            records.put("isVerify", jsonObject.getString("isPass"));
            return records;
        }).collect(Collectors.toList());
        list.removeIf(Objects::isNull);
        data.put("records", list);
        return data;
    }

    /**
     * 提交答卷
     */
    @GlobalTransactional
    @Transactional
    public void submit(String id, String token) throws Exception {
        JSONObject data = JSON.parseObject(JSON.toJSONString(examServiceApi.getExamSubmit(id))).getJSONObject("data");
        ExamData examData = mapper.selectById(id);
        examData.setIsSubmit(1);
        examData.setEndTime(data.getDate("endTime"));
        examData.setScore(data.getInteger("score"));
        mapper.updateById(examData);
    }

    /**
     * 试卷重做
     */
    @GlobalTransactional
    @Transactional
    public void redo(String id, String token) throws Exception {
        String resp = JSON.toJSONString(examServiceApi.getRedoExam(id));
        Integer code = JSON.parseObject(resp).getInteger("code");
        JSONObject data = JSON.parseObject(resp).getJSONObject("data");
        ExamData examData = mapper.selectById(id);
        if (code == 0) {
            examData.setStartTime(data.getDate("startTime"));
            examData.setScore(null);
            examData.setEndTime(null);
            examData.setIsSubmit(0);//未提交
            mapper.updateAllColumnById(examData);
        }
    }

    /**
     * 批阅试卷
     */
    @GlobalTransactional
    @Transactional
    public Integer revise(String examId, String questionId, String score, String token) throws Exception {
        //批阅
        examServiceApi.examRevise(examId, questionId, Integer.parseInt(score));
        //获取答卷
        JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(examServiceApi.examGetById(examId).getData()));
        Integer totalScore = data.getInteger("totalScore");
        //更新本地表冗余字段score
        ExamData examData = mapper.selectById(examId);
        examData.setScore(totalScore);
        mapper.updateAllColumnById(examData);
        return totalScore;
    }

    /**
     * 重新批阅
     */
    @GlobalTransactional
    @Transactional
    public void reCheck(String id, String token) throws Exception {
        examServiceApi.reviseReset(id);
        //获取答卷
        net.sf.json.JSONObject data = net.sf.json.JSONObject.fromObject(examServiceApi.examGetById(id).getData());
        Integer totalScore = data.getInt("totalScore");
        //更新成绩
        ExamData examData = mapper.selectById(id);
        examData.setScore(totalScore);
        mapper.updateById(examData);
    }

    /**
     * 获取超时未提交的答卷，自动提交
     */
    @GlobalTransactional
    @Transactional
    public void checkTimeoutExamData() throws Exception {
        List<Map<String, Object>> doneList = mapper.doneList(new HashMap<>(), new Page<>(1, Integer.MAX_VALUE));
        List<Map<String, Object>> unSubmit = doneList.stream().filter(x -> Objects.equals(x.get("isSubmit").toString(), Constants.NO)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(unSubmit)) {
            return;
        }
        List<String> examDataIds = unSubmit.stream().map(x -> x.get("exam_data_id").toString()).collect(Collectors.toList());
        for (String examDataId : examDataIds) {
            String respString = JSONObject.toJSONString(examServiceApi.examGetById(examDataId));
            JSONObject examDataDetail = JSON.parseObject(respString).getJSONObject("data");
            Date startTime = examDataDetail.getDate("startTime");
            if (new Date().getTime() - startTime.getTime() >= 120 * 60 * 1000) {
                String resp = JSONObject.toJSONString(examServiceApi.autoSubmit(examDataId));
                JSONObject data = JSON.parseObject(resp).getJSONObject("data");
                if (JSON.parseObject(resp).getInteger("code") == 0) {
                    ExamData examData = mapper.selectById(examDataId);
                    examData.setIsSubmit(1);
                    examData.setEndTime(data.getDate("endTime"));
                    examData.setScore(data.getInteger("score"));
                    mapper.updateById(examData);
                }
            }
        }
    }

    public void examCheck(String paperId, String userId, String token) {
        ExamPaper examPaper = examPaperMapper.selectById(paperId);
        if (examPaper == null) {
            throw BizException.withMessage("试卷不存在");
        }
        if (examPaper.getCategory() == PaperCategory.HOMEWORK) {
            return;
        }
        if (examPaper.getCategory() == PaperCategory.EXAMINE) {
            Optional<CoursewareProgress> coursewareProgress = coursewareProgressMapper.selectList(new EntityWrapper<CoursewareProgress>()
                    .eq("course_id", examPaper.getCourseId())
                    .eq("student_id", userId)).stream().findFirst();
            if (!coursewareProgress.isPresent() || coursewareProgress.get().getProgress() != 100) {
                throw BizException.withMessage("课程未学完，不能参加考试");
            }
        }
        String resp = JSON.toJSONString(examServiceApi.paperGetById(paperId));
        if (!Objects.equals(JSON.parseObject(resp).getJSONObject("data").getString("status"), "ENABLE")) {
            throw BizException.withMessage("当前试卷尚未启用");
        }
        Optional<ExamData> examDataOptional = mapper.selectList(new EntityWrapper<ExamData>()
                .eq("paper_id", paperId)
                .eq("student_id", userId)).stream().findFirst();
        if (examDataOptional.isPresent()) {
            String examDataId = examDataOptional.get().getId();
            JSONObject data = JSON.parseObject(JSON.toJSONString(examServiceApi.examGetById(examDataId))).getJSONObject("data");
            Date startTime = data.getDate("startTime");
            if (new Date().getTime() - startTime.getTime() >= 120 * 60 * 1000) {
                JSONObject sub = JSON.parseObject(resp).getJSONObject("data");
                if (JSON.parseObject(JSON.toJSONString(examServiceApi.autoSubmit(examDataId))).getInteger("code") == 0) {
                    ExamData examData = mapper.selectById(examDataId);
                    examData.setIsSubmit(1);
                    examData.setEndTime(sub.getDate("endTime"));
                    examData.setScore(sub.getInteger("score"));
                    mapper.updateById(examData);
                }
                throw BizException.withMessage("考试剩余时长为0，系统自动提交试卷，请勿重复操作");
            }
        }
    }

    /**
     * 删除已做记录
     */
    @GlobalTransactional
    @Transactional
    public void delete(String id) {
        //删除本地冗余的答卷
        mapper.deleteById(id);
        examServiceApi.examDelete(id);
    }

    public void exportExamDataScoreStatic(String courseId, String paperName, String hostOrgId, OutputStream os) throws IOException, WriteException {
        Object o = examServiceApi.paperList(paperName, PaperCategory.EXAMINE.name(), hostOrgId, "ENABLE", courseId, null, null, 1, Integer.MAX_VALUE).getData();
        JSONArray paperList = JSON.parseObject(JSON.toJSONString(o)).getJSONArray("records");
        for (Object object : paperList) {
            JSONObject paper = (JSONObject) object;
            JSONObject data = (JSONObject) examServiceApi.examList(paper.getString("id"), 1, Integer.MAX_VALUE).getData();
            List<RemoteExamDataDto> examDataDtos = JSONObject.parseArray(data.getString("records"), RemoteExamDataDto.class);
            long count = examDataDtos.stream().filter(x -> x.getScore() >= x.getPassScore()).count();//及格人数
            Course course = courseMapper.selectById(paper.getString("courseId"));
            paper.put("courseName", course == null ? null : course.getName());
            paper.put("passRate", new DecimalFormat("0.00").format(count / examDataDtos.size() * 100));//及格率
            paper.put("average", examDataDtos.stream().mapToInt(RemoteExamDataDto::getScore).average().orElse(0));//平均分
            List<Integer> scores = examDataDtos.stream().map(RemoteExamDataDto::getScore).collect(Collectors.toList());
            //中位数
            if (scores.size() % 2 == 0) {
                paper.put("median", (scores.get(scores.size() / 2) + scores.get(scores.size() / 2 - 1)) / 2);
            } else {
                paper.put("median", scores.get(scores.size() / 2));
            }
            paper.put("maxScore", examDataDtos.stream().mapToInt(RemoteExamDataDto::getScore).max());//最高分
            paper.put("minScore", examDataDtos.stream().mapToInt(RemoteExamDataDto::getScore).min());//最低分
            for (int i = 0; i < 9; i++) {
                paper.putAll(this.getCountAndP(i * 10, (i + 1) * 10, examDataDtos.stream().map(RemoteExamDataDto::getScore).collect(Collectors.toList())));
            }
        }
        //excel生成
        WritableWorkbook wwb = Workbook.createWorkbook(os);
        WritableSheet ws = wwb.createSheet("考试统计", 0);
        int row = 0;
        {
            int i = 0;
            ws.addCell(new Label(i, row, "序号", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "课程名称", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "试卷名称", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 25);
            ws.addCell(new Label(i, row, "合格率", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 15);
            ws.addCell(new Label(i, row, "平均分", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "中位分", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "最高分", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            ws.addCell(new Label(i, row, "最低分", OfficeToolExcel.getTitle()));
            ws.setColumnView(i++, 10);
            for (int j = 0; j < 10; j++) {
                ws.addCell(new Label(i, row, j * 10 + "-" + (j + 1) * 10 + "分人数", OfficeToolExcel.getTitle()));
                ws.setColumnView(i++, 15);
                ws.addCell(new Label(i, row, j * 10 + "-" + (j + 1) * 10 + "分占比", OfficeToolExcel.getTitle()));
                ws.setColumnView(i++, 15);
            }
        }
        row = 1;
        int i = 1;
        for (Object object : paperList) {
            JSONObject paper = (JSONObject) object;
            int col = 0;
            ws.addCell(new Label(col++, row, BaseUtil.convertNullToEmpty(i++), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.getStringValueFromJson(paper, "courseName"), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.getStringValueFromJson(paper, "name"), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.getStringValueFromJson(paper, "passRate") + "%", OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.getStringValueFromJson(paper, "average"), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, BaseUtil.getStringValueFromJson(paper, "median"), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, String.valueOf(((OptionalInt)paper.get("maxScore")).getAsInt()), OfficeToolExcel.getNormolCell()));
            ws.addCell(new Label(col++, row, String.valueOf(((OptionalInt)paper.get("minScore")).getAsInt()), OfficeToolExcel.getNormolCell()));
            for (int j = 0; j < 10; j++) {
                ws.addCell(new Label(col++, row, paper.getOrDefault("count_" + j * 10 + "_" + (j + 1) * 10, 0).toString(), OfficeToolExcel.getNormolCell()));
                ws.addCell(new Label(col++, row, paper.getOrDefault("p_" + j * 10 + "_" + (j + 1) * 10, 0).toString(), OfficeToolExcel.getNormolCell()));
            }
            row++;
        }
        wwb.write();
        wwb.close();
        os.flush();
    }

    public Map<String, Object> getCountAndP(int startNum, int endNum, List<Integer> scores) {
        Map<String, Object> result = new HashMap<>();
        long count = scores.stream().filter(x -> x > startNum && x <= endNum).count();
        //0-10 0和10都包含
        if (startNum == 0) {
            count = scores.stream().filter(x -> x >= startNum && x <= endNum).count();
        }
        result.put("count_" + startNum + "_" + endNum, count);
        result.put("p_" + startNum + "_" + endNum, CollectionUtils.isEmpty(scores) ? 0.00 : new DecimalFormat("0.00").format(count / scores.size() * 100));
        return result;
    }
}
