package com.xunw.zjxx.module.biz.mapper;


import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.biz.entity.Xm;
import com.xunw.zjxx.module.enums.OrderStatus;

/**
* <AUTHOR>
* @description 针对表【biz_xm】的数据库操作Mapper
* @createDate 2023-08-08 13:32:09
*/
public interface XmMapper extends BaseMapper<Xm> {

    List<Map<String, Object>> pageQuery(Map<String, Object> condition, Page<?> page);

    List<Map<String, Object>> getStudentLearningRecoredList(Map<String, Object> condition, Page<?> page);
    

    List<String> getBmCourseXmIdByStudentId(@Param("loginUserId") String loginUserId);

    List<Map<String, Object>> getAllCourse(@Param("id")String id);

    List<String> getStudentBuyCourse(@Param("loginUserId")String loginUserId, @Param("id")String id, @Param("orderStatus")OrderStatus orderStatus);

    List<Map<String, Object>> recordList(@Param("studentId") String loginUserId,@Param("year") String year);
    
    List<Integer> recordYears(@Param("studentId") String studentId);

    List<Map<String, Object>> getStudentCertiByXmId(@Param("xmIdList")List<String> bmXmIdList, @Param("userId")String userId);

    List<String> getIsBuyCourse(@Param("loginUserId")String loginUserId, @Param("ids") Set<String> ids, @Param("status") String status);

    List<Map<String, Object>> getCourseListByXmId(@Param("id") String id, @Param("studentId") String studentId);

    List<Map<String, Object>> getXmStudyRecords(@Param("studentId") String studentId, @Param("hostOrgId") String hostOrgId);
}




