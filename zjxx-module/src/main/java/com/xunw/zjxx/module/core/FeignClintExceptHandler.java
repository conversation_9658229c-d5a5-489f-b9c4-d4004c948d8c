package com.xunw.zjxx.module.core;

import com.alibaba.fastjson.JSON;
import com.xunw.zjxx.common.core.ResultMsg;
import com.xunw.zjxx.common.exception.BizException;
import feign.FeignException;
import feign.Response;
import feign.Util;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Description 自定义decode解码器
 */
public class FeignClintExceptHandler implements Decoder {

    Logger logger = LoggerFactory.getLogger(FeignClintExceptHandler.class);

    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        String body = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
        ResultMsg resultMsg = JSON.parseObject(body).toJavaObject(ResultMsg.class);
        if (resultMsg.getCode() == -500) {
            throw BizException.withMessage(resultMsg.getMessage());
        } else if (resultMsg.getCode() != 0) {
            logger.error("url:" + response.request().url().split("\\?")[0] + ",上游服务出现异常:" + response.reason());
            throw new DecodeException(response.status(), "上游服务出现异常,请联系管理员处理！", response.request());
        }
        return resultMsg;
    }
}
