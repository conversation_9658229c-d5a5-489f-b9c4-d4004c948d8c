package com.xunw.zjxx.module.inf.params;

import java.util.List;

import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.module.enums.Category;
import com.xunw.zjxx.module.inf.entity.Type;

public class CourseQueryParams extends BaseQueryParams {

    private static final long serialVersionUID = 7915470138377688575L;

    //课程关键字
    private String keyword;
    //状态
    private String status;
    //主办单位ID
    private String hostOrgId;

    private String typeId;

    private List<Type> allTypes;

    private String studentId;

    private Category category;

    public Category getCategory() {
		return category;
	}

	public void setCategory(Category category) {
		this.category = category;
	}

	public String getStudentId() {
        return studentId;
    }

    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }

    public List<Type> getAllTypes() {
        return allTypes;
    }

    public void setAllTypes(List<Type> allTypes) {
        this.allTypes = allTypes;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }
}
