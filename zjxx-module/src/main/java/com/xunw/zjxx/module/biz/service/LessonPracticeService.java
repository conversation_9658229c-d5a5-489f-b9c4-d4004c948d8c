package com.xunw.zjxx.module.biz.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.base.BaseCRUDService;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.biz.entity.ExamData;
import com.xunw.zjxx.module.biz.entity.LessonPractice;
import com.xunw.zjxx.module.biz.mapper.LessonPracticeMapper;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class LessonPracticeService extends BaseCRUDService<LessonPracticeMapper, LessonPractice> {

    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private ExamDataService examDataService;

    @Transactional
    public void save(String id, String coursewareId, String lessonId, String paperIds, String loginUserId) {
        if (StringUtils.isEmpty(id)) {
            Arrays.asList(paperIds.split(",")).forEach(paperId -> {
                EntityWrapper<LessonPractice> wrapper = new EntityWrapper();
                wrapper.eq("courseware_id", coursewareId);
                wrapper.eq("paper_id", paperId);
                List<LessonPractice> lessonPractices = this.selectList(wrapper);
                //判断练习是否已存在，不存在则添加
                if (lessonPractices!=null&&lessonPractices.size()==0) {
                    LessonPractice lessonPractice = new LessonPractice();
                    lessonPractice.setCoursewareId(coursewareId);
                    lessonPractice.setLessonId(lessonId);
                    lessonPractice.setId(BaseUtil.generateId2());
                    lessonPractice.setPaperId(paperId);
                    lessonPractice.setCreatorId(loginUserId);
                    lessonPractice.setCreateTime(new Date());
                    this.insert(lessonPractice);
                } else {
                    throw BizException.withMessage("存在练习已被使用");
                }
            });
        }
    }

    public Object getPracticesByLesson(String lessonId, String studentId, String status) {
        EntityWrapper<LessonPractice> wrapper = new EntityWrapper();
        wrapper.eq("lesson_id", lessonId);
        List<LessonPractice> lessonPractices = mapper.selectList(wrapper);
        if (lessonPractices.isEmpty()) {
            return new ArrayList<LessonPractice>();
        }
        List<JSONObject> collect = lessonPractices.stream().map(x -> {
            Object resp = examServiceApi.paperGetById(x.getPaperId()).getData();
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(resp));
            jsonObject.put("practiceId", x.getId());
            jsonObject.put("status", jsonObject.getString("status"));
            if (BaseUtil.isNotEmpty(studentId)) {
                List<ExamData> examData = examDataService.selectList((EntityWrapper<ExamData>) new EntityWrapper<ExamData>().eq("paper_id", x.getPaperId()).eq("student_id", studentId));
                String examDataId = null;
                if (!CollectionUtils.isEmpty(examData)) {
                    jsonObject.put("isSubmit", examData.get(0).getIsSubmit());//已作答
                    examDataId = examData.get(0).getId();
                } else {
                    jsonObject.put("isSubmit", null);//未作答
                }
                jsonObject.put("examDataId", examDataId);
            }
            return jsonObject;
        }).collect(Collectors.toList());
        if (BaseUtil.isEmpty(status)) {
            return collect;
        }
        return collect.stream().filter(x->status.equals(x.getString("status"))).collect(Collectors.toList());//只查启用的
    }
}
