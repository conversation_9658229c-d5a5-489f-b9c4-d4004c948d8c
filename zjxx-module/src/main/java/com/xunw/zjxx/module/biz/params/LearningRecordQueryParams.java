package com.xunw.zjxx.module.biz.params;

import com.xunw.zjxx.common.base.BaseQueryParams;
public class LearningRecordQueryParams extends BaseQueryParams {

	/**
	 * 课程id
	 */
	private String courseId;

	/**
	 * 姓名/证件号
	 */
	private String keyword;

	/**
	 * 主办单位id
	 */
	private String hostOrgId;

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}
}
