package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.xunw.zjxx.module.enums.Status;

import java.io.Serializable;

/**
 * 
 * @TableName sys_bank
 */
@TableName(value ="sys_bank")
public class Bank implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 支付平台 ：微信   支付宝
     */
    @TableField(value = "name")
    private String name;

    /**
     * 主办单位id
     */
    @TableField(value = "host_org_id")
    private String hostOrgId;

    /**
     * 启用OK   禁用BLOCK
     */
    @TableField(value = "status")
    private Status status;

    /**
     * 是否默认
     */
    @TableField(value = "is_default")
    private String isDefault;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 支付平台 ：微信   支付宝
     */
    public String getName() {
        return name;
    }

    /**
     * 支付平台 ：微信   支付宝
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 是否默认
     */
    public String getHostOrgId() {
        return hostOrgId;
    }

    /**
     * 是否默认
     */
    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    /**
     * 启用OK   禁用BLOCK
     */
    public Status getStatus() {
        return status;
    }

    /**
     * 启用OK   禁用BLOCK
     */
    public void setStatus(Status status) {
        this.status = status;
    }

    /**
     * 是否默认
     */
    public String getIsDefault() {
        return isDefault;
    }

    /**
     * 是否默认
     */
    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }
}