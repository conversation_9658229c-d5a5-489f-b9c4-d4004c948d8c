package com.xunw.zjxx.module.biz.params;


import com.xunw.zjxx.common.base.BaseQueryParams;

/**
 * 考试人脸比对分页查询参数
 */
public class ExamMonitorQueryParams extends BaseQueryParams {

	/**
	 * 课程id
	 */
	private String courseId;

	/**
	 * 关键字，姓名/身份证号
	 */
	private String keyword;

	/**
	 * 相似度范围
	 */
	private String xsdfw;

	/**
	 * 是否通过比对
	 */
	private String isVerify;

	private String token;

	private String hostOrgId;

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getHostOrgId() {
		return hostOrgId;
	}

	public void setHostOrgId(String hostOrgId) {
		this.hostOrgId = hostOrgId;
	}

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}

	public String getXsdfw() {
		return xsdfw;
	}

	public void setXsdfw(String xsdfw) {
		this.xsdfw = xsdfw;
	}

	public String getIsVerify() {
		return isVerify;
	}

	public void setIsVerify(String isVerify) {
		this.isVerify = isVerify;
	}
}
