package com.xunw.zjxx.module.inf.params;

import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.module.enums.MaterialType;

/**
 *  页面查询参数
 */
public class MaterialQueryParams extends BaseQueryParams {
    private static final long serialVersionUID = 7915470138377688575L;

    private String keyword;
    private String hostOrgId;
    //类型
    private MaterialType type;
    //课程id
    private String courseId;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    public MaterialType getType() {
        return type;
    }

    public void setType(MaterialType type) {
        this.type = type;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }
}
