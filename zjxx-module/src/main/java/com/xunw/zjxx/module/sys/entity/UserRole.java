package com.xunw.zjxx.module.sys.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.io.Serializable;

/**
 * 用户角色
 */
@TableName("sys_user_role")
public class UserRole implements Serializable {

	private static final long serialVersionUID = 9110377559116791925L;

	@TableId(type= IdType.INPUT)
	@TableField("id")
	private String id;
	
	/**用户id
	*/
	@TableField("user_id")
	private String userId;
	
	/**角色ID
	*/
	@TableField("role_id")
	private String roleId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
}
