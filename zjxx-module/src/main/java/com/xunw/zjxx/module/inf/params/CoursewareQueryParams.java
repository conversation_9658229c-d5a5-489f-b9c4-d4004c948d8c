package com.xunw.zjxx.module.inf.params;

import com.xunw.zjxx.common.base.BaseQueryParams;
import com.xunw.zjxx.module.enums.Status;

public class CoursewareQueryParams extends BaseQueryParams {

    private static final long serialVersionUID = 7915479138377688575L;

    //课程关键字
    private String keyword;
    //状态
    private String status;
    //课程ID
    private String courseId;

    /**
     * 不等于某一个课件ID
     */
    private String idNot;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getCourseId() {
		return courseId;
	}

	public void setCourseId(String courseId) {
		this.courseId = courseId;
	}

	public String getIdNot() {
		return idNot;
	}

	public void setIdNot(String idNot) {
		this.idNot = idNot;
	}

}
