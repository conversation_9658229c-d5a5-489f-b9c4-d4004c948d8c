package com.xunw.zjxx.module.inf.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.xunw.zjxx.module.enums.MaterialType;

import java.io.Serializable;
import java.util.Date;

/**
 * 素材表
 */
@TableName("inf_material")
public class Material implements Serializable {
    private static final long serialVersionUID = -354116107759164784L;

    // 主键id
    @TableId(type= IdType.INPUT)
    @TableField("id")
    private String id;

    // 文件名
    @TableField("name")
    private String name;

    // 文档类型
    @TableField("type")
    private MaterialType type;

    // 文件大小
    @TableField("size")
    private Long size;

    // 资源地址
    @TableField("url")
    private String url;

    //课程id
    @TableField("course_id")
    private String courseId;

    //主办单位ID
    @TableField("host_org_id")
    private String hostOrgId;

    // 创建人
    @TableField("creator_id")
    private String creatorId;

    // 创建时间
    @TableField("create_time")
    private Date createTime;

    // 修改人
    @TableField("updator_id")
    private String updatorId;

    // 修改时间
    @TableField("update_time")
    private Date updateTime;

    //课程名称
    @TableField(exist = false)
    private String courseName;

    public Material() {

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public MaterialType getType() {
        return type;
    }

    public void setType(MaterialType type) {
        this.type = type;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getHostOrgId() {
        return hostOrgId;
    }

    public void setHostOrgId(String hostOrgId) {
        this.hostOrgId = hostOrgId;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(String updatorId) {
        this.updatorId = updatorId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
}
