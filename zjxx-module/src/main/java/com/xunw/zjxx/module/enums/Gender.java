package com.xunw.zjxx.module.enums;

import com.baomidou.mybatisplus.enums.IEnum;

import java.io.Serializable;
import java.util.Objects;

public enum Gender implements IEnum {

    M("男", "1"),
	F("女", "2"),
    OTHER("其他", "3");

    private String name;

    private String code;

    private Gender(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public static Gender findById(Integer code) {
        for (Gender orgType : Gender.values()) {
            if (Objects.equals(orgType.code, code)) {
                return orgType;
            }
        }
        return null;
    }

    public static Gender findByEnumName(String name){
        for (Gender orgType : Gender.values()) {
            if (orgType.name().equals(name)) {
                return orgType;
            }
        }
        return null;
    }

    @Override
    public Serializable getValue() {
        return this.name();
    }
}
