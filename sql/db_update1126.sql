---
biz_courseware_progress 增加课件ID

sys_student_info 删除student_id 增加open_id password,同时数据需要处理


alter table sys_user add open_id varchar(100) null comment '第三方平台的id';

-- 创建素材表 inf_material
CREATE TABLE inf_material
(
    id          VARCHAR(64) NOT NULL COMMENT '主键ID',
    name        VARCHAR(255) COMMENT '文件名',
    type        VARCHAR(50) COMMENT '文档类型',
    size        BIGINT COMMENT '文件大小',
    url         VARCHAR(255) COMMENT '资源地址',
    course_id   VARCHAR(64) COMMENT '课程ID',
    host_org_id VARCHAR(64) COMMENT '主办单位ID',
    creator_id  VARCHAR(64) COMMENT '创建人ID',
    create_time DATETIME COMMENT '创建时间',
    updator_id  VARCHAR(64) COMMENT '修改人ID',
    update_time DATETIME COMMENT '修改时间',
    PRIMARY KEY (id)
) COMMENT ='素材表';
