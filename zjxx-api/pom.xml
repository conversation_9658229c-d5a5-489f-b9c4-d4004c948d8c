<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.xunw</groupId>
        <artifactId>zjxx-server</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>zjxx-api</artifactId>
    <name>zjxx-api</name>

    <dependencies>
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>zjxx-common</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.xunw</groupId>
            <artifactId>zjxx-module</artifactId>
            <version>1.0</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
         	<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.2</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
			</plugin>
        </plugins>
    </build>
</project>
