package com.xunw.zjxx.config;

import com.baomidou.mybatisplus.enums.DBType;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.MybatisMapWrapperFactory;
import com.baomidou.mybatisplus.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.plugins.PerformanceInterceptor;
import com.baomidou.mybatisplus.spring.boot.starter.ConfigurationCustomizer;

@Configuration
public class MybatisPlusConfig {
	
    /**
     * mybatis-plus SQL执行效率插件【生产环境可以关闭】
     */
    //@Bean
    public PerformanceInterceptor performanceInterceptor() {
    	PerformanceInterceptor performanceInterceptor = new PerformanceInterceptor();
    	performanceInterceptor.setFormat(true);
        return performanceInterceptor;
    }

    /**
     * mybatis-plus分页插件<br>
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor page = new PaginationInterceptor();
        page.setDialectType(DBType.ORACLE.name());
        return page;
    }
    
    /**
     * 开启返回map结果集的下划线转驼峰
     */
    @Bean
    public ConfigurationCustomizer mybatisConfigurationCustomizer() {
     return new ConfigurationCustomizer() {
      @Override
      public void customize(org.apache.ibatis.session.Configuration configuration) {
       configuration.setObjectWrapperFactory(new MybatisMapWrapperFactory());
      }
     };
    }
    
   
}
