package com.xunw.zjxx.core;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import com.xunw.zjxx.common.utils.CacheHelper;
import com.xunw.zjxx.common.utils.Constants;

/**
 * 服务启动时初始化运行，哪个微服务模块需要则拿此模版去用
 */
@Component
public class StartRunning implements CommandLineRunner{
	
	 private final static Logger log = LoggerFactory.getLogger(StartRunning.class);

   @Override
   public void run(String... args) throws Exception {
       log.info("---服务器启动时执行 start");
       CacheHelper.removeCache(Constants.HOST_ORG_DOMAIN_CACHE);
       log.info("---服务器启动时执行 end");
   }


}
