package com.xunw.zjxx.core;

import java.lang.reflect.Method;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.xunw.zjxx.common.utils.CacheHelper;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.servlet.ModelAndView;

import com.xunw.zjxx.common.core.ResultMsg;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.JSONUtils;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.core.TomSystemQueue;
import com.xunw.zjxx.module.sys.entity.SystemLog;

/**
 * 包装Ajax请求的返回值,并处理所有的错误信息
 *
 * <AUTHOR>
 *
 */
@Aspect
@Component
public class SystemControllerAspect {

	private static  final Logger logger = LoggerFactory.getLogger(SystemControllerAspect.class);

	@SuppressWarnings("unchecked")
	@Around("execution(public * com.xunw.zjxx.api..*.controller.*.*(..))")
	public Object aroundController(ProceedingJoinPoint joinPoint) throws Exception {
		boolean isAjax = false;//根据方法的定义判断是否是ajax方法，如果是则包装其返回值，否则不进行包装
		ResultMsg result = new ResultMsg();
		try {
			MethodSignature msig = (MethodSignature) joinPoint.getSignature();
			Object target = joinPoint.getTarget();
			Class controllerClazz = target.getClass();
			RestController restController = (RestController) controllerClazz.getAnnotation(RestController.class);
			if(restController != null){
				isAjax = true;
			}
			Method currentMethod = controllerClazz.getMethod(msig.getName(), msig.getParameterTypes());
			ResponseBody responseBody = currentMethod.getAnnotation(ResponseBody.class);
			if(responseBody != null){
				isAjax = true;
			}
			logger.info(
					"---------------------------------------------request start--------------------------------------------");
			logger.info(joinPoint.getSignature() + " start!");
			saveLog(currentMethod);
			if(isAjax){
				result.returnData(joinPoint.proceed());
			}
			else{
				return joinPoint.proceed();
			}
		} catch (BizException e) {
			if(isAjax){
				result.returnError(e.getCode(), e.getMessage());
			}
			else{
				ModelAndView view = new ModelAndView();
				view.setViewName("err");
				view.addObject("status", 500);
				view.addObject("exception",e);
				return view;
			}
		} catch (Throwable e) {
			//e.printStackTrace();
			logger.error(
					joinPoint.getTarget().getClass().getSimpleName() + joinPoint.getSignature().getName() + " error,",
					e);
			if(isAjax){
				result.returnError(BizException.SYSTEM_EXCEPTION.getCode(), e.getMessage());
			}
			else{
				ModelAndView view = new ModelAndView();
				view.setViewName("err");
				view.addObject("status", 500);
				view.addObject("exception", e);
				return view;
			}
		}
		logger.info("响应信息:" + JSONUtils.toString(result));
		logger.info("---------------------------------------request end------------------------------------------");
		return result;
	}

	//记录系统日志
	private void saveLog(Method currentMethod)  {
		try {
			RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
			HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
			String ip = BaseUtil.getClientIpAddr(request);
			logger.info("==> 请求客户端IP："+ ip);
			LoginUser user = CacheHelper.getCache(Constants.AUTH.USER_TOKEN_CACHE, request.getHeader(Constants.AUTH.AUTHORIZATION));
			if(user!=null){
				Operation operation = currentMethod.getAnnotation(Operation.class);
				if (operation == null) {
					return;
				}
				SystemLog log = new SystemLog();
				log.setId(BaseUtil.generateId2());
				log.setCreateTime(new Date());
				log.setVisitUrl(request.getRequestURL().toString());
				log.setIp(ip);
				log.setLogType(operation.logType());
				log.setOperation(operation.desc());
				log.setUserType(user.getUserType());
				log.setUsername(user.getUsername());
				log.setHostOrgId(user.getHostOrg() != null ? user.getHostOrg().getId() : null);
				//放入日志队列
				TomSystemQueue.SYSTEM_LOG_QUEUE.add(log);
			}
		}catch (Exception exception) {
			logger.error("save log to db error:", exception);
		}
	}
}