package com.xunw.zjxx.task;

import com.xunw.zjxx.module.biz.service.ExamDataService;
import com.xunw.zjxx.module.comm.service.CommOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.xunw.zjxx.module.sys.service.SystemLogService;

/**
 *  管理后台定时任务
 * <AUTHOR>
 */
@Component
public class AdminTask {

	private static final Logger LOGGER = LoggerFactory.getLogger(AdminTask.class);

	@Autowired
	private SystemLogService systemLogService;
	@Autowired
	private ExamDataService examDataService;
	@Autowired
	private CommOrderService commOrderService;

	 /**
     * 扫描系统日志队列
     */
	@Scheduled(fixedDelay = 10*1000,initialDelay = 10*1000)
    public void doSystemLogCheck() {
        systemLogService.persistLogFromQueue(100);
    }

	/**
	 * 扫描超时未提交的答卷，系统自动提交这些试卷
	 */
	@Scheduled(fixedDelay=180*1000,initialDelay = 180*1000)
	public void submitTimeoutExamData() throws Exception {
		examDataService.checkTimeoutExamData();
	}

	/**
	 * 调用微信、支付宝等第三方支付系统接口，检查未支付的订单是否已经支付
	 */
	@Scheduled(fixedDelay = 600*1000,initialDelay = 600*1000)
	public void doSyncOrderStatus() {
		commOrderService.doSyncOrderStatus();
	}
}
