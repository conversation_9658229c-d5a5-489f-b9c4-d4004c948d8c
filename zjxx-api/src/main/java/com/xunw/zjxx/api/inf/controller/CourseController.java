package com.xunw.zjxx.api.inf.controller;

import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.entity.Type;
import com.xunw.zjxx.module.inf.params.CourseQueryParams;
import com.xunw.zjxx.module.inf.service.CourseService;
import com.xunw.zjxx.module.inf.service.TypeService;

@RestController
@RequestMapping("/htgl/inf/zypx/course")
public class CourseController extends BaseController {

    @Autowired
    private CourseService service;

    @Autowired
    private TypeService typeService;

    @RequestMapping("/list")
    @Operation(desc = "课程列表")
    public Object list(HttpServletRequest request,
                       CourseQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return service.pageQuery(params);
    }

    @RequestMapping("/add")
    @Operation(desc = "添加课程")
    public Object add(HttpServletRequest request,
                      @RequestParam(required = false) String typeId,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) String tag,
                      @RequestParam(required = false) String logo,
                      @RequestParam(required = false) String xct,
                      @RequestParam(required = false) Double hours,
                      @RequestParam(required = false) Double amount) throws Exception {
        if(StringUtils.isEmpty(name)){
            throw BizException.withMessage("请输入课程名称");
        }
        if(StringUtils.isEmpty(typeId)){
            throw BizException.withMessage("请输入课程分类");
        }
//        if(hours == null){
//            throw BizException.withMessage("请输入课程的学时");
//        }
//        if(hours <= 0){
//            throw BizException.withMessage("课程学时必须大于0");
//        }
        Integer count = service.selectCount((EntityWrapper<Course>) new EntityWrapper<Course>().eq("host_org_id", super.getCurrentHostOrgId(request)).eq("name", name));
        if (count > 0) {
            throw BizException.withMessage("课程名称在系统中重复");
        }
        Course course = new Course();
        course.setName(name);
        course.setCreateTime(new Date());
        course.setCreatorId(super.getLoginUserId(request));
        course.setTypeId(typeId);
        course.setXct(xct);
        course.setLogo(logo);
        course.setHours(hours);
        course.setTag(tag);
        course.setHostOrgId(super.getCurrentHostOrgId(request));
        course.setAmount(amount);
        course.setStatus(Status.OK);
        Type topType = typeService.getCourseTopType(typeId);
        course.setTopTypeId(topType.getId());
        service.addCourse(course);
        return true;
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改课程")
    public Object edit(
            HttpServletRequest request,
            @RequestParam(required = false) String id,
            @RequestParam(required = false) String typeId,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String tag,
            @RequestParam(required = false) String logo,
            @RequestParam(required = false) String xct,
            @RequestParam(required = false) Double hours,
            @RequestParam(required = false) Double amount
    ) throws Exception {
        if(StringUtils.isEmpty(name)){
            throw BizException.withMessage("请输入课程名称");
        }
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择课程");
        }
//        if(hours == null){
//            throw BizException.withMessage("请输入课程的学时");
//        }
//        if(hours <= 0){
//            throw BizException.withMessage("课程学时必须大于0");
//        }
        Integer count = service.selectCount((EntityWrapper<Course>) new EntityWrapper<Course>()
                .eq("host_org_id", super.getCurrentHostOrgId(request))
                .eq("name", name)
                .ne("id", id));
        if (count > 0) {
            throw BizException.withMessage("课程名称在系统中重复");
        }
        Course course = new Course();
        course.setId(id);
        course.setName(name);
        course.setUpdateTime(new Date());
        course.setTypeId(typeId);
        Type topType = typeService.getCourseTopType(typeId);
        course.setTopTypeId(topType.getId());
        course.setXct(xct);
        course.setLogo(logo);
        course.setHours(hours);
        course.setTag(tag);
        course.setUpdatorId(super.getCurrentHostOrgId(request));
        course.setAmount(amount);
        service.editCourse(course, super.getAccessToken(request));
        return true;
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除课程")
    public Object deleteById(
            HttpServletRequest request,
            @RequestParam(required = false) String id
    ) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择课程");
        }
        service.deleteCourse(id, super.getAccessToken(request));
        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "根据id查询课程")
    public Object getById(
            HttpServletRequest request,
            @RequestParam(required = false) String id
    ) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择课程");
        }
        Course course = service.selectById(id);
        return course;
    }

    @RequestMapping("/enable")
    @Operation(desc = "课程状态启用/禁用")
    public Object enable(
            HttpServletRequest request,
            @RequestParam(required = false) String id,
            @RequestParam(required = false) Status status
    ) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择课程");
        }
        if(status == null){
            throw BizException.withMessage("请选择状态");
        }
        Course course = service.selectById(id);
        course.setStatus(status);
        service.updateById(course);
        return true;
    }

    @RequestMapping("/select")
    @Operation(desc = "获取课程下拉框")
    public Object select(
            HttpServletRequest request
    ) throws Exception {
        EntityWrapper<Course> wrapper = new EntityWrapper();
        wrapper.eq("status", Status.OK);
        wrapper.eq("host_org_id", super.getCurrentHostOrgId(request));
        return service.selectList(wrapper);
    }

    @RequestMapping("/importCourse")
    @Operation(desc = "批量导入课程")
    public Object importCourse(HttpServletRequest request,
                               @RequestParam(value = "file") MultipartFile file,
                               @RequestParam(required = false) String typeId) throws Exception {
        if (StringUtils.isEmpty(typeId)) {
            throw BizException.withMessage("课程类型不能为空");
        }
        if (file == null) {
            throw BizException.withMessage("文件不能为空");
        }
        return service.importCourse(file, super.getLoginUserId(request), super.getCurrentHostOrgId(request),typeId);
    }
}
