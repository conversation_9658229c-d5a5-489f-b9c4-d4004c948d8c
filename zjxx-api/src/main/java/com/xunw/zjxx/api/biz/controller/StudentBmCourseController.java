package com.xunw.zjxx.api.biz.controller;


import java.io.IOException;

import javax.servlet.http.HttpServletRequest;

import com.xunw.zjxx.module.biz.entity.StudentBmCourse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.StudentBmCourseQueryParams;
import com.xunw.zjxx.module.biz.service.StudentBmCourseService;
import com.xunw.zjxx.module.inf.service.CourseService;

import jxl.read.biff.BiffException;


@RestController
@RequestMapping("/htgl/biz/xkgl")
public class StudentBmCourseController  extends BaseController {


    @Autowired
    private StudentBmCourseService studentBmCourseService;
    @Autowired
    private CourseService courseService;

    @RequestMapping("/list")
    @Operation(desc = "选课信息查询", loginRequired = false)
    public Object list(HttpServletRequest request,
                       StudentBmCourseQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return studentBmCourseService.pageQuery(params);
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除选课")
    public Object deleteById(
            HttpServletRequest request,
            @RequestParam(required = false) String id
    ) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择要删除的选课");
        }
//        StudentBmCourse studentBmCourse = studentBmCourseService.selectById(id);
//        if(studentBmCourse.getIsPayed().equals("1")){
//            throw BizException.withMessage("已经缴费的课程不能删除！");
//        }
        studentBmCourseService.deleteById(id);
        return true;
    }

    @RequestMapping("/batchPay")
    @Operation(desc = "批量标记缴费")
    public Object batchPay(
            HttpServletRequest request,
            @RequestParam(required = false) String ids
    ) throws Exception {
        if(StringUtils.isEmpty(ids)){
            throw BizException.withMessage("请选择要批量标记缴费的选课");
        }
        studentBmCourseService.batchPay(ids);
        return true;
    }


    @RequestMapping("/import")
    @Operation(desc = "导入")
    public Object importStudentBmCourse(HttpServletRequest request,
                                        @RequestParam(value = "file", required = false) MultipartFile file,
                                        @RequestParam(required = false) String courseId) throws BiffException, IOException {
        if (file == null) {
            throw new BizException("请上传文件");
        }
        if (StringUtils.isEmpty(courseId)) {
            throw new BizException("请选择课程");
        }
        if (courseService.selectById(courseId) == null) {
            throw new BizException("选择课程有误");
        }
        return studentBmCourseService.importStudentBmCourse(file, courseId, super.getLoginUserId(request));
    }
}
