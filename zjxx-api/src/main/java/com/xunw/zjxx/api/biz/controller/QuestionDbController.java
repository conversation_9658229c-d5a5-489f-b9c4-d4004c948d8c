package com.xunw.zjxx.api.biz.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.config.ExamServiceConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.QuestionDbQueryParams;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.mapper.CourseMapper;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.sys.entity.User;
import com.xunw.zjxx.module.sys.service.UserService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/htgl/biz/questionDb")
public class QuestionDbController extends BaseController {

    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private ExamServiceConfig examServiceConfig;
    @Autowired
    private UserService userService;
    @Autowired
    private CourseMapper courseMapper;

    @RequestMapping("/list")
    @Operation(desc = "题库列表")
    public Object list(HttpServletRequest request,
                       QuestionDbQueryParams params) throws Exception {
        params.setOrgId(super.getCurrentHostOrgId(request));
        Object questionDbList = examServiceApi.questionDbList(params.getName(), params.getStatus(), null, 
                params.getCourseId(), params.getCourseName(), params.getOrgId(), null, params.getCurrent(), params.getSize()).getData();
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(questionDbList));
        List<JSONObject> records = jsonObject.getJSONArray("records").stream().map(x -> {
            JSONObject map = JSON.parseObject(JSON.toJSONString(x));
            String userId = map.getString("createdBy");
            String courseId = map.getString("courseId");
            User user = userService.selectById(userId);
            if(user != null){
                map.put("createName", user.getName());
            }
            Course course = courseMapper.selectById(courseId);
            if(course!=null){
                map.put("courseName", course.getName());
            }
            return map;
        }).collect(Collectors.toList());
        jsonObject.put("records", records);
        return jsonObject;
    }

    @RequestMapping("/add")
    @Operation(desc = "新增题库")
    public Object add(HttpServletRequest request,
                      HttpServletResponse response,
                      @RequestBody JSONObject json) throws Exception {
        json.put("isOldPaper",1);
        json.put("orgId",super.getCurrentHostOrgId(request));
        return examServiceApi.questionDbAdd(json).getData();
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除题库")
    public Object delete(HttpServletRequest request,
                      HttpServletResponse response,
                      String id) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择题库");
        }
        examServiceApi.questionDbDelete(id);
        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "根据ID查询题库")
    public Object getById(HttpServletRequest request,
                       HttpServletResponse response,
                       String id) throws Exception {
        Object resp = examServiceApi.questionDbPreview(id).getData();
        return JSON.parseObject(JSON.toJSONString(resp));
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改题库")
    public Object edit(HttpServletRequest request,
                      HttpServletResponse response,
                      @RequestBody JSONObject json) throws Exception {
        examServiceApi.questionDbUpdate(json);
        return true;
    }

    @RequestMapping("/viewQues")
    @Operation(desc = "题库预览")
    public Object viewQues(HttpServletRequest request,
                       HttpServletResponse response,
                       @RequestBody JSONObject json) throws Exception {
        String id = String.valueOf(json.get("id"));
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择题库");
        }
        return examServiceApi.questionPreview(id).getData();
    }

    @RequestMapping("/select")
    @Operation(desc = "获取题库下拉框",loginRequired = false)
    public Object select(
            HttpServletRequest request
    ) throws Exception {
        Object resp = examServiceApi.questionDbSelect(null, null, null, null, 
                null, super.getCurrentHostOrgId(request), null, 1, Integer.MAX_VALUE).getData();
        return JSON.parseArray(JSON.toJSONString(resp));
    }
}
