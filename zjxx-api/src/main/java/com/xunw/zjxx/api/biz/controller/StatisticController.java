package com.xunw.zjxx.api.biz.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.service.CertiApproveService;
import com.xunw.zjxx.module.biz.service.ExamDataService;
import com.xunw.zjxx.module.dto.RemoteExamDataDto;
import com.xunw.zjxx.module.dto.RemoteExamPaperDto;
import com.xunw.zjxx.module.dto.RemoteQuestionDto;
import com.xunw.zjxx.module.enums.PaperCategory;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.service.CourseService;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.sys.service.StudentInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/htgl/biz/xm")
public class StatisticController extends BaseController {

    @Autowired
    private CertiApproveService certiApproveService;
    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private CourseService courseService;
    @Autowired
    private ExamDataService examDataService;
    @Autowired
    private StudentInfoService studentInfoService;

    @RequestMapping("/statistic")
    @Operation(desc = "统计分析查询")
    public Object statistic(HttpServletRequest request,
                            @RequestParam(required = false) String year) throws Exception {
        return certiApproveService.statistic(year, super.getCurrentHostOrgId(request));
    }

    @RequestMapping("/examDataStatic")
    @Operation(desc = "作答结果统计")
    public Object examDataStatic(HttpServletRequest request,
                                 @RequestParam(required = false) String courseId,
                                 @RequestParam(required = false) String paperName,
                                 Page page) throws Exception {
        Object o = examServiceApi.paperList(paperName, PaperCategory.EXAMINE.name(), super.getCurrentHostOrgId(request), "ENABLE", courseId, null, null, page.getCurrent(), page.getSize()).getData();
        JSONArray paperList = JSON.parseObject(JSON.toJSONString(o)).getJSONArray("records");
        for (Object object : paperList) {
            JSONObject paper = (JSONObject) object;
            JSONObject data = (JSONObject) examServiceApi.examList(paper.getString("id"), 1, Integer.MAX_VALUE).getData();
            List<RemoteExamDataDto> examDataDtos = JSONObject.parseArray(data.getString("records"), RemoteExamDataDto.class);
            if (CollectionUtils.isEmpty(examDataDtos)) {
                continue;
            }
            long count = examDataDtos.stream().filter(x -> x.getScore() >= x.getPassScore()).count();//及格人数
            Course course = courseService.selectById(paper.getString("courseId"));
            paper.put("courseName", course == null ? null : course.getName());
            paper.put("passRate", CollectionUtils.isEmpty(examDataDtos) ? 0 : new DecimalFormat("0.00").format(count / examDataDtos.size() * 100));//及格率
            paper.put("average", examDataDtos.stream().mapToInt(RemoteExamDataDto::getScore).average().orElse(0));//平均分
            List<Integer> scores = examDataDtos.stream().map(RemoteExamDataDto::getScore).collect(Collectors.toList());
            //中位数
            if(CollectionUtils.isEmpty(scores)){
                paper.put("median", 0);
            } else if (scores.size() == 1) {
                paper.put("median", scores.get(0));
            } else {
                Collections.sort(scores);
                if (scores.size() % 2 == 0) {
                    paper.put("median", (scores.get(scores.size() / 2) + scores.get(scores.size() / 2 - 1)) / 2);
                } else {
                    paper.put("median", scores.get(scores.size() / 2));
                }
            }
            paper.put("maxScore", examDataDtos.stream().mapToInt(RemoteExamDataDto::getScore).max());//最高分
            paper.put("minScore", examDataDtos.stream().mapToInt(RemoteExamDataDto::getScore).min());//最低分
        }
        page.setTotal(JSON.parseObject(JSON.toJSONString(o)).getLong("total"));
        page.setRecords(paperList);
        return page;
    }

    @RequestMapping("/examDataScoreStatic")
    @Operation(desc = "作答结果分数段统计")
    public Object examDataScoreStatic(HttpServletRequest request,
                                      @RequestParam(required = false) String paperId) throws Exception {
        if (StringUtils.isEmpty(paperId)) {
            throw BizException.withMessage("请选择试卷");
        }
        JSONObject data = (JSONObject) examServiceApi.examList(paperId, 1, Integer.MAX_VALUE).getData();
        List<RemoteExamDataDto> examDataDtos = JSONObject.parseArray(data.getString("records"), RemoteExamDataDto.class);
        Map<String, Object> result = new TreeMap<>();
        for (int i = 0; i < 10; i++) {
            result.putAll(examDataService.getCountAndP(i * 10, (i + 1) * 10, examDataDtos.stream().map(RemoteExamDataDto::getScore).collect(Collectors.toList())));
        }
        return result;
    }

    @RequestMapping("/exportExamDataScoreStatic")
    @Operation(desc = "作答结果统计")
    public Object exportExamDataScoreStatic(HttpServletRequest request,
                                            HttpServletResponse response,
                                            @RequestParam(required = false) String courseId,
                                            @RequestParam(required = false) String paperName) throws Exception {
        response.setContentType("text/html;charset=utf-8");
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=student_examdata.xls");
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            examDataService.exportExamDataScoreStatic(courseId, paperName, super.getCurrentHostOrgId(request), os);
            os.flush();
        } finally {
            if (os != null) {
                IOUtils.closeQuietly(os);
            }
        }
        return true;
    }

    @RequestMapping("/collect/statistic")
    @Operation(desc = "错题统计列表")
    public Object collectStatic(HttpServletRequest request,
                                            HttpServletResponse response,
                                            @RequestParam(required = false) String courseId,
                                            @RequestParam(required = false) String paperName,
                                            Page page) throws Exception {
        Object o = examServiceApi.paperList(paperName, PaperCategory.EXAMINE.name(), super.getCurrentHostOrgId(request), "ENABLE", courseId, null, null, page.getCurrent(), page.getSize()).getData();
        JSONArray paperList = JSON.parseObject(JSON.toJSONString(o)).getJSONArray("records");
        for (Object object : paperList) {
            JSONObject paper = (JSONObject) object;
            JSONObject data = (JSONObject) examServiceApi.examList(paper.getString("id"), 1, Integer.MAX_VALUE).getData();
            List<RemoteExamDataDto> examDataDtos = JSONObject.parseArray(data.getString("records"), RemoteExamDataDto.class);
            Course course = courseService.selectById(paper.getString("courseId"));
            paper.put("courseName", course == null ? null : course.getName());
            paper.put("count", examDataDtos.size());//已做人数
        }
        page.setTotal(JSON.parseObject(JSON.toJSONString(o)).getLong("total"));
        page.setRecords(paperList);
        return page;
    }

    @RequestMapping("/collect/detail")
    @Operation(desc = "错题统计详情")
    public Object collectDetail(HttpServletRequest request,
                                @RequestParam(required = false) String paperId) throws Exception {
        if (StringUtils.isEmpty(paperId)) {
            throw BizException.withMessage("请选择试卷");
        }
        //试卷
        JSONObject paperRsp = (JSONObject) examServiceApi.paperGetById(paperId).getData();
        RemoteExamPaperDto paper = JSONObject.toJavaObject(paperRsp, RemoteExamPaperDto.class);
        //作答记录
        JSONObject data = (JSONObject) examServiceApi.examList(paperId, 1, Integer.MAX_VALUE).getData();
        List<RemoteExamDataDto> examDataDtos = JSONObject.parseArray(data.getString("records"), RemoteExamDataDto.class);
        Set<String> examDataIds = examDataDtos.stream().map(RemoteExamDataDto::getId).collect(Collectors.toSet());
        List<Map<String, Object>> examDatas = new ArrayList<>();
        for (String examDataId : examDataIds) {
            examDatas.add((Map<String, Object>) examServiceApi.getExamDetail(examDataId, paperId).getData());
        }
        //所有试题
        List<RemoteQuestionDto> questions = paper.getSections().stream().flatMap(section -> section.getQuestions().stream()).collect(Collectors.toList());
        //所有的作答答案，用于计算作答人数
        List<JSONObject> answers = examDatas.stream().map(x -> (JSONObject) x.get("answers")).collect(Collectors.toList());
        questions.forEach(q -> {
            //错误人数
            List<Map<String, Object>> mapList = examDatas.stream().filter(e -> {
                JSONObject checkMap = (JSONObject) e.get("checks");
                return checkMap.containsKey(q.getId()) && checkMap.getInteger(q.getId()) < q.getScore();
            }).collect(Collectors.toList());
            System.out.println(mapList);
            //学生id
            List<String> studentIds = mapList.stream().map(m -> JSONObject.toJavaObject((JSONObject) m.get("exam"), RemoteExamDataDto.class).getStudentId()).collect(Collectors.toList());
            long submitCount = answers.stream().filter(x -> x.containsKey(q.getId())).count();
            q.setCollectCount((long) mapList.size());
            q.setSubmitCount(submitCount);
            q.setCollectP(submitCount == 0 ? 0.00 : Double.parseDouble(new DecimalFormat("0.00").format(mapList.size() * 100.0 / submitCount)));
            q.setStudents(CollectionUtils.isEmpty(studentIds) ? new ArrayList<>() : studentInfoService.selectBatchIds(studentIds));
        });
        questions.sort((o1, o2) -> {
            return o1.getCollectP() >= o2.getCollectP() ? -1 : 1;
        });
        return questions;
    }
}
