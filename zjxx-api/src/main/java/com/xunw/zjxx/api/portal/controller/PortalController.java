package com.xunw.zjxx.api.portal.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.FileHelper;
import com.xunw.zjxx.common.utils.HttpKit;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.biz.params.ZypxCertiApproveQueryParams;
import com.xunw.zjxx.module.biz.service.*;
import com.xunw.zjxx.module.comm.service.CommOrderService;
import com.xunw.zjxx.module.dto.InvoiceDetailDto;
import com.xunw.zjxx.module.enums.*;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.entity.Type;
import com.xunw.zjxx.module.inf.params.CourseQueryParams;
import com.xunw.zjxx.module.inf.params.TypeQueryParams;
import com.xunw.zjxx.module.inf.service.CourseService;
import com.xunw.zjxx.module.inf.service.MaterialService;
import com.xunw.zjxx.module.inf.service.TypeService;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.rpc.ExamService.params.QuestionCollectQueryParams;
import com.xunw.zjxx.module.rpc.FaceServiceApi;
import com.xunw.zjxx.module.rpc.LearningCoursewareServiceApi;
import com.xunw.zjxx.module.sys.entity.*;
import com.xunw.zjxx.module.sys.params.NoticeCategoryQueryParams;
import com.xunw.zjxx.module.sys.params.NoticeQueryParams;
import com.xunw.zjxx.module.sys.params.OrgQueryParams;
import com.xunw.zjxx.module.sys.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.xunw.zjxx.api.comm.controller.AdminCommController.isImageTypeAllowed;
@RestController
@RequestMapping("/portal")
public class PortalController extends BaseController {

	@Autowired
	private XmService xmService;
	@Autowired
	private TypeService typeService;
	@Autowired
	private StudentInfoService studentInfoService;
	@Autowired
	private CommOrderService orderService;
	@Autowired
	private CourseService courseService;
	@Autowired
	private NoticeCategoryService noticeCategoryService;
	@Autowired
	private ExamPaperService examPaperService;
	@Autowired
	private NoticeService noticeService;
	@Autowired
	private StudentBmCourseService studentBmCourseService;
	@Autowired
	private ExamDataService examDataService;
	@Autowired
	private AttConfig attConfig;
	@Autowired
	private SettingService settingService;
	@Autowired
	private QuestionService questionService;
	@Autowired
	private BankService bankService;
	@Autowired
	private BankConfigService bankConfigService;
	@Autowired
	private CertiApproveService certiApproveService;
	@Autowired
	private FaceServiceApi faceServiceApi;
	@Autowired
	private LearningCoursewareServiceApi learningCoursewareServiceApi;
	@Autowired
	private OrgService orgService;
    @Autowired
    private MaterialService materialService;
	@Autowired
	private LessonPracticeService service;
	@Autowired
	private ExamServiceApi examServiceApi;

	@RequestMapping("/details")
	@Operation(desc = "培训计划详情", loginRequired = false)
	public Object details(HttpServletRequest request, String id) {
		String loginUserId = this.getLoginUserId(request);

		return xmService.portalDetails(id, loginUserId);
	}

	@RequestMapping("/courseClassify")
	@Operation(desc = "课程类型分类", loginRequired = false)
	public Object courseClassify(HttpServletRequest request, TypeQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return typeService.courseClassify(params);
	}

	/**
	 * 组织机构的下拉框查询
	 */
	@RequestMapping("/relatedOrg/select")
	@Operation(desc = "组织机构的下拉框查询", loginRequired = false)
	public Object relatedOrgSelect(HttpServletRequest request) throws Exception {
		OrgQueryParams params = new OrgQueryParams();
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setOrgType(OrgType.ORG);
		params.setIsParent(Constants.YES);
		params.setSize(Integer.MAX_VALUE);
		params.setCurrent(Constants.DEFAULT_PAGE_NUMBER);
		return orgService.list(params).getRecords();
	}

	/**
	 * 职称系列下拉查询
	 */
	@RequestMapping("/titleSeries/select")
	@Operation(desc = "职称系列下拉查询", loginRequired = false)
	public Object titleSeriesSelect(HttpServletRequest request) throws Exception {
		Type type = typeService.getZykTopType(super.getCurrentHostOrgId(request));
		return type != null ? typeService.getChildrenType(type.getId()) : Collections.EMPTY_LIST;
	}

	@RequestMapping("/bm")
	@Operation(desc = "课程报名")
	public Object bm(HttpServletRequest request, String courseIds) {
		if (BaseUtil.isEmpty(courseIds)) {
			throw BizException.withMessage("课程id不能为空");
		}
		studentBmCourseService.bmCourse(super.getLoginUserId(request), courseIds);
		return true;
	}

	@RequestMapping("/coursePay")
	@Operation(desc = "课程购买")
	public Object coursePay(HttpServletRequest request, String courseIds) {
		LoginUser loginUser = this.getLoginUser(request);
		if (BaseUtil.isEmpty(courseIds)) {
			throw BizException.withMessage("请至少选择一个课程");
		}
		EntityWrapper<StudentInfo> wrap = new EntityWrapper<>();
		wrap.eq("id", loginUser.getId());
		List<StudentInfo> studentInfos = studentInfoService.selectList(wrap);
		if (studentInfos == null || studentInfos.size() == 0) {
			throw BizException.withMessage("当前用户登录信息有误");
		}
		return orderService.coursePay(courseIds, studentInfos.get(0), loginUser.getHostOrg().getId());
	}

	@RequestMapping("/course/updateStudyTime")
	@Operation(desc = "保存学习进度", loginRequired = false)
	public Object updateStudyTime(HttpServletRequest request, String courseId, String coursewareId, String chapterId, String lessonId)
			throws Exception {
		String accessToken = this.getAccessToken(request);
		LoginUser loginUser = this.getLoginUser(request);
		if (BaseUtil.isEmpty(courseId)) {
			throw BizException.withMessage("请选择一个课程");
		}
		if (BaseUtil.isEmpty(chapterId)) {
			throw BizException.withMessage("请选择一个章");
		}
		if (BaseUtil.isEmpty(lessonId)) {
			throw BizException.withMessage("请选择一个节");
		}
		courseService.updateStudyTime(loginUser, courseId, coursewareId, chapterId, lessonId, accessToken);
		return true;
	}

	@RequestMapping("/course/details")
	@Operation(desc = "获取课程详情", loginRequired = false)
	public Object getCourseDetails(HttpServletRequest request, String id,@RequestParam(required = false) String isOnlyStructer) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("课程id不能为空");
		}
		return courseService.getCourseDetails(id, isOnlyStructer, super.getAccessToken(request), super.getLoginUserId(request),
				super.getCurrentHostOrgId(request));
	}

	@RequestMapping("/course/addNotes")
	@Operation(desc = "保存学习笔记", loginRequired = false)
	public Object addNotes(HttpServletRequest request, String lessonId, String content, Long point) throws Exception {
		if (BaseUtil.isEmpty(lessonId)) {
			throw BizException.withMessage("请选择一个节");
		}
		if (BaseUtil.isEmpty(content)) {
			throw BizException.withMessage("笔记内容不能为空");
		}
		if (BaseUtil.isEmpty(point)) {
			throw BizException.withMessage("请选择一个时间节点");
		}
		String accessToken = this.getAccessToken(request);
		return courseService.addNotes(accessToken, lessonId, content, point);

	}

	@RequestMapping("/course/delNotes")
	@Operation(desc = "删除学习笔记", loginRequired = false)
	public Object delNotes(HttpServletRequest request, String lessonId, String id) throws Exception {
		if (BaseUtil.isEmpty(lessonId)) {
			throw BizException.withMessage("请选择一个节");
		}
		if (BaseUtil.isEmpty(id)) {
			throw BizException.withMessage("请选择一个笔记");
		}
		String accessToken = this.getAccessToken(request);
		return courseService.delNotes(accessToken, lessonId, id);
	}

	@RequestMapping("/course/addComments")
	@Operation(desc = "课程添加评论", loginRequired = false)
	public Object addComments(HttpServletRequest request, String lessonId, String content) throws Exception {
		String accessToken = this.getAccessToken(request);
		return courseService.addComments(accessToken, lessonId, content);
	}

	@RequestMapping("/course/list")
	@Operation(desc = "课程列表", loginRequired = false)
	public Object selectPageList(HttpServletRequest request, CourseQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return courseService.selectPageList(params);
	}

	@RequestMapping("/course/myCourseList")
	@Operation(desc = "已购买课程列表")
	public Object myCourseList(HttpServletRequest request, @RequestParam(required = false) String courseName,
			@RequestParam(required = false) Category category) throws Exception {
		CourseQueryParams params = new CourseQueryParams();
		params.setCurrent(1);
		params.setSize(Integer.MAX_VALUE);
		params.setKeyword(courseName);
		params.setCategory(category);
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setStudentId(super.getLoginUserId(request));
		return courseService.myCourseList(params, super.getLoginUserId(request), this.getAccessToken(request));
	}

	@RequestMapping("/noticeCategory/list")
	@Operation(desc = "新闻公告分类", loginRequired = false)
	public Object noticeCategoryList(HttpServletRequest request, NoticeCategoryQueryParams params) {
		params.setStatus(Status.OK);
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return noticeCategoryService.pageQuery(params);
	}

	@RequestMapping("/notice/list")
	@Operation(desc = "新闻公告列表", loginRequired = false)
	public Object noticeList(HttpServletRequest request, NoticeQueryParams params) throws Exception {
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		params.setStatus(Status.OK);
		return noticeService.pageQuery(params);
	}

	@RequestMapping("/notice/getById")
	@Operation(desc = "新闻公告详情", loginRequired = false)
	public Object noticeGetById(HttpServletRequest request, @RequestParam(required = false) String id) {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("公告id不能为空");
		}
		Notice notice = noticeService.selectById(id);
		notice.setCount(notice.getCount() + 1);
		noticeService.updateById(notice);
		return notice;
	}

	@RequestMapping("/recordList")
	@Operation(desc = "档案列表")
	public Object recordList(HttpServletRequest request, @RequestParam(required = false) String year) {
		return xmService.recordList(super.getLoginUserId(request), year);
	}

	@RequestMapping("/recordYears")
	@Operation(desc = "档案列表年度")
	public Object recordYears(HttpServletRequest request, @RequestParam(required = false) String year) {
		return xmService.recordYeas(super.getLoginUserId(request));
	}
	
	@RequestMapping("/recordDetail")
	@Operation(desc = "档案详情", loginRequired = false)
	public Object recordDetail(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		Map retMap = new HashMap<>();
		// 获取用户基本信息
		retMap.put("student", studentInfoService.selectById(super.getLoginUserId(request)));
		// 获取学习情况
		List<Map<String, Object>> study = studentBmCourseService.study(super.getLoginUserId(request), id);
		retMap.put("study", study);
		// 获取考试情况
		List<Map<String, Object>> exam = examDataService.exam(super.getLoginUserId(request), id);
		retMap.put("exam", exam);
		//培训整体情况统计
		retMap.put("trainingOverview", studentBmCourseService.statistics(super.getLoginUserId(request), id));
		return retMap;
	}

	@RequestMapping("/exam/list/{category}")
	@Operation(desc = "考试列表")
	public Object examList(HttpServletRequest request,@PathVariable PaperCategory category, String isDone,
			@RequestParam(required = false) String courseId) throws Exception {
		LoginUser loginUser = this.getLoginUser(request);
		String accessToken = this.getAccessToken(request);
		return examPaperService.examList(category,courseId, accessToken, isDone, loginUser);
	}

	@RequestMapping("/exam/start")
	@Operation(desc = "作业、考试开始、继续")
	public Object examStart(HttpServletRequest request,
							@RequestParam(required = false) String examDataId,
							@RequestParam(required = false) String paperId) throws Exception {
		if (BaseUtil.isEmpty(paperId)) {
			throw BizException.withMessage("试卷id不能为空");
		}
		LoginUser loginUser = this.getLoginUser(request);
		String accessToken = this.getAccessToken(request);
		return examPaperService.examStart(examDataId, paperId, loginUser, accessToken);
	}

	@RequestMapping("/exam/details")
	@Operation(desc = "作业、考试详情", loginRequired = false)
	public Object examDetails(HttpServletRequest request, String paperId, String examDataId) throws Exception {
		if (BaseUtil.isEmpty(paperId)) {
			throw BizException.withMessage("试卷id不能为空");
		}
		LoginUser loginUser = this.getLoginUser(request);
		String accessToken = this.getAccessToken(request);
		return examPaperService.examDetails(examDataId, loginUser, accessToken, paperId);
	}

	/**
	 * 进入练习考试之前的校验
	 */
	@RequestMapping("/exam/beforeCheck")
	@Operation(desc = "进入练习、考试之间的校验")
	public Object examCheck(HttpServletRequest request, String paperId) throws Exception {
		if (StringUtils.isEmpty(paperId)) {
			throw BizException.withMessage("试卷id不能为空");
		}
		examDataService.examCheck(paperId, super.getLoginUserId(request), super.getAccessToken(request));
		return true;
	}

	@RequestMapping("/exam/uploadAns")
	@Operation(desc = "上传答题卡")
	public Object uploadAns(HttpServletRequest request, String url, String examDataId) throws Exception {
		if (BaseUtil.isEmpty(examDataId)) {
			throw BizException.withMessage("请选择一个作答记录");
		}
		if (BaseUtil.isEmpty(url)) {
			throw BizException.withMessage("请选择一个文件");
		}
		return examPaperService.uploadAns(examDataId, url);
	}

	@RequestMapping("/exam/deleteAns")
	@Operation(desc = "删除答题卡")
	public Object deleteAns(HttpServletRequest request, String url, String examDataId) throws Exception {
		if (BaseUtil.isEmpty(examDataId)) {
			throw BizException.withMessage("请选择一个作答记录");
		}
		if (BaseUtil.isEmpty(url)) {
			throw BizException.withMessage("请选择一个文件");
		}
		return examPaperService.deleteAns(examDataId, url);
	}

	@RequestMapping("/exam/save")
	@Operation(desc = "保存作答记录")
	public Object examSave(HttpServletRequest request, @RequestBody JSONObject json) throws Exception {
		if (BaseUtil.isEmpty(json.get("id"))) {
			throw BizException.withMessage("作答记录id不能为空");
		}
		if (BaseUtil.isEmpty(json.get("answers"))) {
			throw BizException.withMessage("作答内容不能为空");
		}
		LoginUser loginUser = this.getLoginUser(request);
		String accessToken = this.getAccessToken(request);
		return examPaperService.examSave(json, loginUser, accessToken);
	}

	@RequestMapping("/exam/submit")
	@Operation(desc = "提交答卷")
	public Object submit(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if (BaseUtil.isEmpty(id)) {
			throw BizException.withMessage("作答记录id不能为空");
		}
		examDataService.submit(id, super.getAccessToken(request));
		return true;
	}

	@RequestMapping("/exam/redo")
	@Operation(desc = "重做")
	public Object redo(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if (BaseUtil.isEmpty(id)) {
			throw BizException.withMessage("作答记录id不能为空");
		}
		examDataService.redo(id, super.getAccessToken(request));
		return true;
	}

	@RequestMapping("/certificateList")
	@Operation(desc = "证书列表")
	public Object certificateList(HttpServletRequest request) throws Exception {
		LoginUser loginUser = this.getLoginUser(request);
		String accessToken = this.getAccessToken(request);
		return xmService.certificateList(loginUser, accessToken);
	}

	@RequestMapping("/certificateView")
	@Operation(desc = "查看证书")
	public Object certificateList(HttpServletRequest request,
								  @RequestParam(required = false) String xmId) throws Exception {
		if (BaseUtil.isEmpty(xmId)) {
			throw BizException.withMessage("项目id不能为空");
		}
		return certiApproveService.buildStudentCerti(xmId, super.getLoginUserId(request), getCurrentHostOrgPortalWebUrl(request));
	}


	@RequestMapping("/invoiceOrderList")
	@Operation(desc = "发票-订单列表")
	public Object invoiceOrderList(HttpServletRequest request, @RequestParam(required = false) String status) {
		LoginUser loginUser = this.getLoginUser(request);
		return orderService.invoiceOrderList(loginUser, status);
	}

	@RequestMapping("/invoiceList")
	@Operation(desc = "发票列表")
	public Object invoiceList(HttpServletRequest request, @RequestParam(required = false) String status) {
		LoginUser loginUser = this.getLoginUser(request);
		return orderService.invoiceList(loginUser, status);
	}

	@RequestMapping("/certificateApply")
	@Operation(desc = "证书申请/重新申请")
	public Object certificateApply(HttpServletRequest request, @RequestParam(required = false) String xmId) throws Exception {
		String loginUserId = this.getLoginUserId(request);
		return xmService.certificateApply(xmId, loginUserId);
	}

	@RequestMapping("/planning/details")
	@Operation(desc = "培训计划详情")
	public Object planningDetails(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		String loginUserId = this.getLoginUserId(request);
		return xmService.planningDetails(id, loginUserId, super.getAccessToken(request));
	}

	@RequestMapping("/invoiceApply")
	@Operation(desc = "发票申请")
	public Object invoiceApply(HttpServletRequest request, InvoiceDetailDto invoiceDetailDto) throws Exception {
		if(BaseUtil.isEmpty(invoiceDetailDto.getIds())){
			throw BizException.withMessage("订单id不能为空");
		}
		if(BaseUtil.isEmpty(invoiceDetailDto.getInvoiceTitle())){
			throw BizException.withMessage("发票抬头不能为空");
		}
		if(BaseUtil.isEmpty(invoiceDetailDto.getInvoiceCode())){
			throw BizException.withMessage("纳税人识别号不能为空");
		}
		orderService.invoiceApply(invoiceDetailDto,this.getLoginUserId(request));
		return true;
	}

	@RequestMapping("/invoiceDetails")
	@Operation(desc = "发票申请详情")
	public Object invoiceDetails(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
		if(BaseUtil.isEmpty(id)){
			throw BizException.withMessage("发票申请id不能为空");
		}
		return orderService.invoiceDetails(id,this.getLoginUserId(request));
	}

	@RequestMapping("/exam/snap")
	@Operation(desc = "考试抓拍")
	public Object examSnap(HttpServletRequest request,
						   @RequestParam(required = false) String isCheck,
						   @RequestParam(required = false) String courseId,
						   @RequestParam(required = false) String paperId,
						   @RequestParam(required = false) String photoPath) throws Exception {
		if (StringUtils.isEmpty(courseId)) {
			throw BizException.withMessage("课程id不能为空");
		}
		if (StringUtils.isEmpty(paperId)) {
			throw BizException.withMessage("试卷id不能为空");
		}
		if (StringUtils.isEmpty(photoPath)) {
			throw BizException.withMessage("抓拍照片不能为空");
		}
		if (StringUtils.isEmpty(isCheck)) {
			throw BizException.withMessage("是否强制检测人脸不能为空");
		}
		String resp = JSONObject.toJSONString(faceServiceApi.getExamCapture(Constants.UPDATE_STUDY_TIME_BATCH_ID,
				paperId,courseId,photoPath,"NONE",Constants.YES,isCheck, super.getCurrentHostOrgId(request)));
		JSONObject data = JSON.parseObject(resp).getJSONObject("data");
		if (Objects.equals(data.getString("isFace"), Constants.NO)) {
			throw BizException.withMessage("抓拍异常，抓拍照片无人脸");
		}
		return true;
	}

	@RequestMapping("/exam/verify")
	@Operation(desc = "考试人脸比对")
	public Object examVerify(HttpServletRequest request,
							 @RequestParam(required = false) String courseId,
						     @RequestParam(required = false) String paperId,
						     @RequestParam(required = false) String photoPath) throws Exception {
		if (StringUtils.isEmpty(courseId)) {
			throw BizException.withMessage("课程id不能为空");
		}
		if (StringUtils.isEmpty(paperId)) {
			throw BizException.withMessage("试卷id不能为空");
		}
		if (StringUtils.isEmpty(photoPath)) {
			throw BizException.withMessage("抓拍照片不能为空");
		}
		StudentInfo studentInfo = super.getLoginUser(request).getStudentInfo();
		if (StringUtils.isEmpty(studentInfo.getStudentPhoto())) {
			throw BizException.withMessage("学员登记照为空，请完善个人信息");
		}
		List<Setting> settings = settingService.selectList((EntityWrapper<Setting>) new EntityWrapper<Setting>()
				.eq("host_org_id", super.getCurrentHostOrgId(request)));
		if (CollectionUtils.isEmpty(settings)) {
			throw BizException.withMessage("系统尚未配置人脸比对阈值，请联系管理员");
		}
		Object verifyThreshold = JSON.parseObject(settings.get(0).getContent()).get(SysSettingEnum.VERIFY_THRESHOLD.name());
		String resp = JSONObject.toJSONString(faceServiceApi.examVerify(Constants.UPDATE_STUDY_TIME_BATCH_ID,paperId,courseId,
				studentInfo.getStudentPhoto(),photoPath,"STUDENT",verifyThreshold,"NONE",Constants.YES,
				super.getCurrentHostOrgId(request)));
		JSONObject data = JSON.parseObject(resp).getJSONObject("data");
		return Objects.equals(data.getString("isPass"), Constants.YES);
	}

	@RequestMapping("/course/snap")
	@Operation(desc = "课程学习抓拍")
	public Object courseSnap(HttpServletRequest request,
							 @RequestParam(required = false) String isCheck,
							 @RequestParam(required = false) String courseId,
							 @RequestParam(required = false) String coursewareId,
							 @RequestParam(required = false) String chapterId,
							 @RequestParam(required = false) String lessonId,
							 @RequestParam(required = false) String photoPath) throws Exception {
		if (StringUtils.isEmpty(courseId)) {
			throw BizException.withMessage("课程id不能为空");
		}
		Course course = courseService.selectById(courseId);
		if (course == null) {
			throw BizException.withMessage("课程不存在");
		}
		if (course.getStatus() == Status.BLOCK) {
			throw BizException.withMessage("课程已被禁用");
		}
		if (StringUtils.isEmpty(coursewareId)) {
			throw BizException.withMessage("课件id不能为空");
		}
		String coursewareResp = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareGetById(coursewareId,null));
		JSONObject courseware = JSON.parseObject(coursewareResp).getJSONObject("data");
		if (courseware == null) {
			throw BizException.withMessage("课件不存在");
		}
		if (Objects.equals(courseware.getString("status"), Status.BLOCK.name())) {
			throw BizException.withMessage("课件已被禁用");
		}
		if (StringUtils.isEmpty(chapterId)) {
			throw BizException.withMessage("章节id不能为空");
		}
		if (StringUtils.isEmpty(lessonId)) {
			throw BizException.withMessage("课时id不能为空");
		}
		if (StringUtils.isEmpty(photoPath)) {
			throw BizException.withMessage("抓拍照片不能为空");
		}
		if (StringUtils.isEmpty(isCheck)) {
			throw BizException.withMessage("是否强制检测人脸不能为空");
		}
		String resp = JSONObject.toJSONString(faceServiceApi.learnCapture(Constants.UPDATE_STUDY_TIME_BATCH_ID,coursewareId,courseId,
				chapterId,lessonId,photoPath,"NONE",Constants.YES,isCheck, super.getCurrentHostOrgId(request)));
		JSONObject data = JSON.parseObject(resp).getJSONObject("data");
		if (Objects.equals(data.getString("isFace"), Constants.NO)) {
			throw BizException.withMessage("抓拍异常，抓拍照片无人脸");
		}
		return true;
	}

	@RequestMapping("/course/verify")
	@Operation(desc = "课程学习人脸比对")
	public Object courseVerify(HttpServletRequest request,
							   @RequestParam(required = false) String courseId,
						       @RequestParam(required = false) String coursewareId,
						       @RequestParam(required = false) String photoPath) throws Exception {
		if (StringUtils.isEmpty(courseId)) {
			throw BizException.withMessage("课程id不能为空");
		}
		Course course = courseService.selectById(courseId);
		if (course == null) {
			throw BizException.withMessage("课程不存在");
		}
		if (course.getStatus() == Status.BLOCK) {
			throw BizException.withMessage("课程已被禁用");
		}
		if (StringUtils.isEmpty(coursewareId)) {
			throw BizException.withMessage("课件id不能为空");
		}
		String coursewareResp = JSONObject.toJSONString(learningCoursewareServiceApi.coursewareGetById(coursewareId,null));
		JSONObject courseware = JSON.parseObject(coursewareResp).getJSONObject("data");
		if (courseware == null) {
			throw BizException.withMessage("课件不存在");
		}
		if (Objects.equals(courseware.getString("status"), Status.BLOCK.name())) {
			throw BizException.withMessage("课件已被禁用");
		}
		if (StringUtils.isEmpty(photoPath)) {
			throw BizException.withMessage("抓拍照片不能为空");
		}
		StudentInfo studentInfo = super.getLoginUser(request).getStudentInfo();
		if (StringUtils.isEmpty(studentInfo.getStudentPhoto())) {
			throw BizException.withMessage("学员登记照为空，请完善个人信息");
		}
		List<Setting> settings = settingService.selectList((EntityWrapper<Setting>) new EntityWrapper<Setting>()
				.eq("host_org_id", super.getCurrentHostOrgId(request)));
		if (CollectionUtils.isEmpty(settings)) {
			throw BizException.withMessage("系统尚未配置人脸比对阈值，请联系管理员");
		}
		Object verifyThreshold = JSON.parseObject(settings.get(0).getContent()).get(SysSettingEnum.VERIFY_THRESHOLD.name());
		String resp = JSONObject.toJSONString(faceServiceApi.learnVerify(Constants.UPDATE_STUDY_TIME_BATCH_ID,coursewareId,
				studentInfo.getStudentPhoto(),photoPath,verifyThreshold,"NONE",Constants.YES, super.getCurrentHostOrgId(request)));
		JSONObject data = JSON.parseObject(resp).getJSONObject("data");
		return Objects.equals(data.getString("isPass"), Constants.YES);
	}

	@RequestMapping("/common/uploadImage")
	@Operation(desc = "图片上传", loginRequired = false)
	public Object uploadImage(HttpServletRequest request,
							  @RequestParam("file") MultipartFile file) throws Exception {
		if(file== null){
			throw BizException.withMessage("在请求中没有检测到图片");
		}
		String ext = FileHelper.getExtension(file.getOriginalFilename());
		//检查文件类型是否被允许
		if(!isImageTypeAllowed(ext)) {
			throw BizException.withMessage("不允许上传:"+ext+"格式的图片文件");
		}
		String path = attConfig.getRootDir()+"/upload/images/"+new SimpleDateFormat("yyyyMMddHH").format(new Date());
		String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()+ext;
		return FileHelper.storeFile(path, file.getInputStream(), newFileName);
	}

	@RequestMapping("/common/uploadBae64Image")
	@Operation(desc = "通用图片文件上传接口(仅支持64)")
	public Object uploadBae64Image(HttpServletRequest request,
								   @RequestParam(required = false, value = "base64Image") String base64Image) throws Exception {
		if(StringUtils.isEmpty(base64Image)){
			throw BizException.withMessage("在请求中没有检测到Base64图片");
		}
		String url = FileHelper.convertImgBase64SrcStrToUrl(base64Image);
		return url;
	}

	@RequestMapping("/courseware/getRandomObjectiveQuestion")
	@Operation(desc = "获取课件弹题")
	public Object getRandomObjectiveQuestion(HttpServletRequest request,
											 @RequestParam(required = false) String quesDbIds) throws Exception {
		if(StringUtils.isEmpty(quesDbIds)) {
			throw BizException.withMessage("题库id不能为空");
		}
		return questionService.getRandomObjectiveQuestion(quesDbIds, super.getAccessToken(request));
	}

	@RequestMapping("/getOpenId")
	@Operation(desc = "获取openId，用于小程序授权", loginRequired = false)
	public Object getOpenId(HttpServletRequest request,
							@RequestParam(required = false) String code) throws Exception {
		if (StringUtils.isEmpty(code)) {
			throw BizException.withMessage("code不能为空");
		}
		Optional<Bank> optionalBank = bankService.selectList((EntityWrapper<Bank>) new EntityWrapper<Bank>()
				.eq("host_org_id", super.getCurrentHostOrgId(request))
				.eq("status", Status.OK)).stream().findFirst();
		if (!optionalBank.isPresent()) {
			throw BizException.withMessage("当前主办单位未配置微信小程序参数");
		}
		EntityWrapper<BankConfig> wrapper = new EntityWrapper<>();
		wrapper.eq("bank_id", optionalBank.get().getId());
		wrapper.eq("pay_plat", PayPlatform.WX);
		List<BankConfig> bankConfigs = bankConfigService.selectList(wrapper);
		if (bankConfigs.size() == 0) {
			throw BizException.withMessage("当前主办单位未配置微信小程序参数");
		}
		BankConfig bankConfig = bankConfigs.get(0);
		net.sf.json.JSONObject setting = net.sf.json.JSONObject.fromObject(bankConfig.getPaySettting());
		String app_id = setting.getString("wx_app_id");
		String secret = setting.getString("wx_app_secret");
		String url = "https://api.weixin.qq.com/sns/jscode2session?appid="+app_id+"&secret="+secret+"&js_code="+code+"&grant_type=authorization_code&connect_redirect=1";
		return HttpKit.post(url);
	}

	@RequestMapping("/selectCertiByNameAndSfzh")
	@Operation(desc = "查询合格证书")
	public Object selectCertiByNameAndSfzh(HttpServletRequest request,
                                      		@RequestParam(required = false) String name,
                                      		@RequestParam(required = false) String sfzh) throws Exception {
		ZypxCertiApproveQueryParams params = new ZypxCertiApproveQueryParams();
		params.setKeyword(name);
		params.setSfzh(sfzh);
		params.setHostOrgId(super.getCurrentHostOrgId(request));
		return certiApproveService.selectPassList(params);
	}

	@RequestMapping("/material")
	@Operation(desc = "材料")
	public Object materialList(HttpServletRequest request,
							   @RequestParam(required = false) String name,
							   @RequestParam(required = false) String courseId,
							   Page page) throws Exception {
		return materialService.materialList(name, courseId, super.getLoginUserId(request), page);
	}

	@RequestMapping("/getPracticesByLesson")
	@Operation(desc = "获取课时所有练习")
	public Object getPracticesByLesson(HttpServletRequest request,
									   @RequestParam(required = false) String lessonId,
									   @RequestParam(required = false) String status) {
		if (StringUtils.isEmpty(lessonId)) {
			throw BizException.withMessage("课时id不能为空");
		}
		return service.getPracticesByLesson(lessonId, super.getLoginUserId(request), status);
	}

	//错题列表
	@RequestMapping("/collect/list")
	@Operation(desc = "错题列表")
	public Object collectList(HttpServletRequest request, QuestionCollectQueryParams params) {
		params.setAppId(Constants.CURRENT_APPID);
		params.setOrgId(super.getCurrentHostOrgId(request));
		params.setStudentId(super.getLoginUserId(request));
		JSONObject data = JSONObject.parseObject(JSONObject.toJSONString(examServiceApi.collectList(params).getData()));
		List<Map<String, Object>> list = new ArrayList<>();
		for (Object o : data.getJSONArray("records")) {
			Map<String, Object> map = (Map<String, Object>) o;
			Course course = courseService.selectById(map.get("courseId").toString());
			map.put("courseName", course.getName());
			list.add(map);
		}
		data.put("records",list);
        return data;
	}

	//添加收藏
	@RequestMapping("/collect/add")
	@Operation(desc = "添加收藏")
	public Object collectAdd(HttpServletRequest request, @RequestBody JSONObject json) {
		json.put("studentId", super.getLoginUserId(request));
		json.put("appId", Constants.CURRENT_APPID);
		json.put("orgId", super.getCurrentHostOrgId(request));
		examServiceApi.collectAdd(json);
		return true;
	}

	//删除收藏
	@RequestMapping("/collect/delete")
	@Operation(desc = "删除收藏")
	public Object collectDetele(@RequestParam(required = false) String ids) {
		if(StringUtils.isEmpty(ids)) {
			throw BizException.withMessage("请选择要删除的错题");
		}
		examServiceApi.collectDetele(ids);
		return true;
	}

	//收藏题目练习
	@RequestMapping("/collect/question/practise")
	@Operation(desc = "收藏题目练习")
	public Object collectQuestionPractise(HttpServletRequest request, @RequestBody JSONObject json) {
		json.put("studentId", super.getLoginUserId(request));
		json.put("appId", Constants.CURRENT_APPID);
		json.put("orgId", super.getCurrentHostOrgId(request));
		examServiceApi.collectQuestionPractise(json);
		return true;
	}
}
