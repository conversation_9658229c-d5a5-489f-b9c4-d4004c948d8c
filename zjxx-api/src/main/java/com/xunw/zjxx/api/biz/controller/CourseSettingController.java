package com.xunw.zjxx.api.biz.controller;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.entity.Xm;
import com.xunw.zjxx.module.biz.service.XmCourseScopeService;
import com.xunw.zjxx.module.biz.service.XmService;

@RestController
@RequestMapping("/htgl/biz/xmCourseSetting")
public class CourseSettingController extends BaseController {

    @Autowired
    private XmService xmService;
    @Autowired
    private XmCourseScopeService xmCourseScopeService;

    @RequestMapping("/add")
    @Operation(desc = "课程设置新增")
    public Object add(HttpServletRequest request,
                      @RequestParam(required = false) String planId,
                      @RequestParam(required = false) Integer publicHours,
                      @RequestParam(required = false) Integer specialtyHours,
                      @RequestParam(required = false) String publicTypeId,
                      @RequestParam(required = false) String specialtyTypeId) throws Exception {
        if (StringUtils.isEmpty(planId)) {
            throw BizException.withMessage("学习计划id不能为空");
        }
        if (publicHours == null) {
            throw BizException.withMessage("公共课时不能为空");
        }
        if (specialtyHours == null) {
            throw BizException.withMessage("专业课时不能为空");
        }
        if (StringUtils.isEmpty(publicTypeId)) {
            throw BizException.withMessage("公共课类型id不能为空");
        }
        if (StringUtils.isEmpty(specialtyTypeId)) {
            throw BizException.withMessage("专业课类型id不能为空");
        }
        Xm xm = xmService.selectById(planId);
        if (xm == null) {
            throw BizException.withMessage("学习计划不存在");
        }
        xmCourseScopeService.add(xm, publicHours, specialtyHours, publicTypeId, specialtyTypeId);
        return true;
    }

    @RequestMapping("/edit")
    @Operation(desc = "课程设置修改")
    public Object edit(HttpServletRequest request,
                       @RequestParam(required = false) String planId,
                       @RequestParam(required = false) Integer publicHours,
                       @RequestParam(required = false) Integer specialtyHours,
                       @RequestParam(required = false) String publicTypeId,
                       @RequestParam(required = false) String specialtyTypeId) throws Exception {
        if (StringUtils.isEmpty(planId)) {
            throw BizException.withMessage("学习计划id不能为空");
        }
        if (publicHours == null) {
            throw BizException.withMessage("公共课时不能为空");
        }
        if (specialtyHours == null) {
            throw BizException.withMessage("专业课时不能为空");
        }
        if (StringUtils.isEmpty(publicTypeId)) {
            throw BizException.withMessage("公共课类型id不能为空");
        }
        if (StringUtils.isEmpty(specialtyTypeId)) {
            throw BizException.withMessage("专业课类型id不能为空");
        }
        Xm xm = xmService.selectById(planId);
        if (xm == null) {
            throw BizException.withMessage("学习计划不存在");
        }
        xmCourseScopeService.edit(xm, publicHours, specialtyHours, publicTypeId, specialtyTypeId);
        return true;
    }
}
