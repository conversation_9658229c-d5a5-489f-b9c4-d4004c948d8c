package com.xunw.zjxx.api.biz.controller;

import java.io.OutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.encoders.UTF8;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.HomeworkQueryParams;
import com.xunw.zjxx.module.biz.service.ExamDataService;

@RestController
@RequestMapping("/htgl/biz/zypx/homework")
public class HomeworkController extends BaseController {

    @Autowired
    private ExamDataService examDataService;

    @RequestMapping("/list")
    @Operation(desc = "练习记录列表")
    public Object list(HttpServletRequest request,
                       HomeworkQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return examDataService.getHomeworkList(params, super.getAccessToken(request));
    }

    @RequestMapping("/getById")
    @Operation(desc = "练习记录详情")
    public Object getById(HttpServletRequest request,
                          @RequestParam(required = false) String id,
                          @RequestParam(required = false) String studentId) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("练习id不能为空");
        }
        if (StringUtils.isEmpty(studentId)) {
            throw BizException.withMessage("学员id不能为空");
        }
        return examDataService.getById(id, studentId, super.getAccessToken(request), super.getCurrentHostOrgId(request));
    }

    @RequestMapping("/export")
    @Operation(desc = "导出练习记录")
    public void export(HttpServletRequest request,
                         HttpServletResponse response,
                         HomeworkQueryParams params) throws Exception {
        params.setSize(Integer.MAX_VALUE);
        params.setCurrent(1);
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=practiceRecord.xls");
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            examDataService.export(params, super.getAccessToken(request), os);
            os.flush();
        } catch (Exception e) {
//            LOGGER.error("导出失败:", e);
        } finally {
            if (os != null) {
                IOUtils.closeQuietly(os);
            }
        }
    }
}
