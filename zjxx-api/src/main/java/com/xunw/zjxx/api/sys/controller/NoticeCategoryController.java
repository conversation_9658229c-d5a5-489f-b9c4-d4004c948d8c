package com.xunw.zjxx.api.sys.controller;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.sys.params.NoticeQueryParams;
import com.xunw.zjxx.module.sys.service.NoticeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.dto.reqParams.NoticeCategoryReq;
import com.xunw.zjxx.module.sys.params.NoticeCategoryQueryParams;
import com.xunw.zjxx.module.sys.service.NoticeCategoryService;

@RestController
@RequestMapping("/htgl/sys/noticeCategory")
public class NoticeCategoryController extends BaseController {

    @Autowired
    private NoticeCategoryService noticeCategoryService;

    @Autowired
    private NoticeService noticeService;

    @RequestMapping("/list")
    @Operation(desc = "公告类型列表")
    public Object query(HttpServletRequest request,
                        NoticeCategoryQueryParams params) {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return noticeCategoryService.pageQuery(params);
    }

    @RequestMapping("/getById")
    @Operation(desc = "根据id获取公告类型详情")
    public Object getById(HttpServletRequest request,
                          @RequestParam(required = false) String id) {
        return noticeCategoryService.selectById(id);
    }

    @RequestMapping("/add")
    @Operation(desc = "新增公告类型")
    public Object add(HttpServletRequest request, NoticeCategoryReq req) throws Exception {
        noticeCategoryService.add(req, super.getCurrentHostOrgId(request), super.getLoginUserId(request));
        return true;
    }

    @RequestMapping("/edit")
    @Operation(desc = "编辑公告类型")
    public Object edit(HttpServletRequest request, NoticeCategoryReq req) {
        noticeCategoryService.edit(req, super.getLoginUserId(request));
        return true;
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除公告类型")
    public Object deleteById(HttpServletRequest request, @RequestParam(required = false) String ids) {
        if (StringUtils.isEmpty(ids)) {
            throw BizException.withMessage("id不能为空");
        }
        NoticeQueryParams params = new NoticeQueryParams();
        params.setCategoryId(ids);
        Page page = noticeService.pageQuery(params);
        if(page.getTotal() != 0){
            throw BizException.withMessage("该公告类型下有启用的公告信息，不可删除！");
        }
        noticeCategoryService.deleteById(ids);
        return true;
    }

    @RequestMapping("/tree")
    @Operation(desc = "获取公告类型树")
    public Object tree(HttpServletRequest request, NoticeCategoryQueryParams params) {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return noticeCategoryService.tree(params, super.getLoginUserId(request));
    }
}
