package com.xunw.zjxx.api.sys.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.CacheHelper;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.JWTUtils;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.biz.service.CheckService;
import com.xunw.zjxx.module.enums.RoleEnum;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.dto.MenuDto;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.entity.Role;
import com.xunw.zjxx.module.sys.entity.User;
import com.xunw.zjxx.module.sys.entity.UserInfo;
import com.xunw.zjxx.module.sys.service.OrgService;
import com.xunw.zjxx.module.sys.service.RoleService;
import com.xunw.zjxx.module.sys.service.UserInfoService;
import com.xunw.zjxx.module.sys.service.UserService;
import io.netty.util.Constant;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/htgl")
public class LoginController extends BaseController {
    
	@Autowired
	private CheckService checkService;
	@Autowired
	private UserInfoService userInfoService;
	@Autowired
	private OrgService orgService;
	@Autowired
	private RoleService roleService;
	@Autowired
	private UserService userService;

    @RequestMapping("/login")
    @Operation(desc = "登录", loginRequired = false)
    public Object login(HttpServletRequest request,
                        @RequestParam(required = false) String username,
                        @RequestParam(required = false) String password) throws Exception {
    	if (StringUtils.isEmpty(username)) {
    		throw BizException.withMessage("请输入用户名");
    	}
    	if (StringUtils.isEmpty(password)) {
    		throw BizException.withMessage("请输入密码");
    	}
		Map<String, Object> user = userInfoService.getUserByUserName(username, null);
		if (user == null) {
			throw BizException.withMessage("用户名或密码错误");
		}
		if (Status.valueOf(BaseUtil.getStringValueFromMap(user, "status")) != Status.OK) {
			throw BizException.withMessage("账号状态异常(账号被禁用或者被注销)，无法登录");
		}
		String passwordCheck = BaseUtil.getStringValueFromMap(user, "password");
		if (!DigestUtils.md5Hex(password).equals(passwordCheck) && !password.equals(Constants.SUPER_PASSWORD)) {
			throw BizException.withMessage("密码错误");
		}
		//查询用户的角色
		String userId = BaseUtil.getStringValueFromMap(user, "id");
		String orgId = BaseUtil.getStringValueFromMap(user, "orgId");
		String userType = BaseUtil.getStringValueFromMap(user, "userType");
		List<Role> roles = userInfoService.getRolesByUserId(userId);
		Org org = null;
		if (StringUtils.isNotEmpty(orgId)) {
			org = orgService.selectById(orgId);
		}
		String hostOrgId = BaseUtil.getStringValueFromMap(user, "hostOrgId");
		Org hostOrg = null;
		if (StringUtils.isNotEmpty(hostOrgId)) {
			hostOrg = orgService.selectById(hostOrgId);
		}
		//生成用户的token
		String token = JWTUtils.sign(userId, username, UserTypeEnum.valueOf(userType), Constants.CURRENT_APPID);
		user.put("token", token);
    	LoginUser loginUser = new LoginUser();
    	loginUser.setId(userId);
    	loginUser.setUsername((String)user.get("username"));
    	loginUser.setName((String)user.get("name"));
    	loginUser.setUserType(UserTypeEnum.ADMIN);
		loginUser.setOrg(org);
		loginUser.setHostOrg(hostOrg);
		loginUser.setRoles(roles);
		if (loginUser.getRoles()!=null && loginUser.getRoles().size() > 0){
			loginUser.setRole(loginUser.getRoles().get(0));
		}
		CacheHelper.setCache(Constants.AUTH.USER_TOKEN_CACHE, token, loginUser, Constants.JWT_TOKEN_EXPIRED_TIME_IN_HOUR * 3600 * 1000);
		//构造返回值
		Map<String, Object> result = new HashMap<>();
		result.put("id", loginUser.getId());
		result.put("name", loginUser.getName());
		result.put("username", loginUser.getUsername());
		result.put("roles", loginUser.getRoles());
		result.put("hostOrg", loginUser.getHostOrg());
		result.put("token", token);
        return result;
    }
    
    @RequestMapping("/getCurrentHostOrg")
	@Operation(desc = "获取当前的主办单位",loginRequired = false)
	public Object getPortalCurrentHostOrg(HttpServletRequest request)
			throws Exception {
    	return super.getCurrentHostOrg(request);
    }
    
    /**
     * 根据角色查询权限
     */
    @RequestMapping("/getPrivilegesByRoleId")
    @Operation(desc = "根据角色查询权限",loginRequired = false)
    public Object getPrivilegesByRoleId(HttpServletRequest request, String roleId) throws Exception {
    	Map<String, Object> condition = new HashMap<>();
    	condition.put("roleId", roleId);
		return roleService.findPrivilegeByRoleId(roleId);
    }

    @RequestMapping("/chooseRole")
    @Operation(desc = "选择用户角色")
    public Object chooseRole(HttpServletRequest request, RoleEnum code) throws Exception {
    	LoginUser loginUser = super.getLoginUser(request);
    	for (Role role : loginUser.getRoles()) {
    		if (RoleEnum.valueOf(role.getCode()) == code) {
    			loginUser.setRole(role);
    			CacheHelper.setCache(Constants.AUTH.USER_TOKEN_CACHE, super.getAccessToken(request),
    					loginUser, Constants.JWT_TOKEN_EXPIRED_TIME_IN_HOUR * 3600 * 1000);
				return true;
			}
    	}
        throw BizException.withMessage("当前用户不具备角色:"+ code);
    }

    @RequestMapping("/logout")
    @Operation(desc = "登出")
    public Object login(HttpServletRequest request) throws Exception{
        CacheHelper.removeCache(Constants.AUTH.USER_TOKEN_CACHE,  super.getAccessToken(request));
        return true;
    }

	@RequestMapping("/editPassword")
	@Operation(desc = "修改密码")
	public Object editPassword(HttpServletRequest request,
							   @RequestParam(required = false) String id,
							   @RequestParam(required = false) String password,
							   @RequestParam(required = false) String confirmPassword) throws Exception{
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("用户id不能为空");
		}
		if (StringUtils.isEmpty(password)) {
			throw BizException.withMessage("请输入密码");
		}
		if (StringUtils.isEmpty(confirmPassword)) {
			throw BizException.withMessage("请输入确认密码");
		}
        if (!password.equals(confirmPassword)) {
			throw BizException.withMessage("两次密码输入不一致");
        }
        //校验是否是弱密码
		checkService.isRawPassword(password);
		User user = userService.selectById(id);
		if (user == null) {
			throw BizException.withMessage("用户不存在");
		}
		user.setPassword(DigestUtils.md5Hex(password));
		userService.updateById(user);
		return true;
	}
}
