package com.xunw.zjxx.api.biz.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.QuestionQueryParams;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.mapper.CourseMapper;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.rpc.ExamService.enums.QuestionType;
import com.xunw.zjxx.module.rpc.ExamService.enums.Status;
import com.xunw.zjxx.module.sys.entity.User;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/htgl/biz/question")
public class QuestionController extends BaseController {

    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    AttConfig attConfig;
    @Autowired
    private CourseMapper courseMapper;

    @RequestMapping("/add")
    @Operation(desc = "新增试题")
    public Object add(HttpServletRequest request,
                      HttpServletResponse response,
                      @RequestBody JSONObject json) throws Exception {
        json.put("isOldPaper", Constants.NO);
        return examServiceApi.questionAdd(json).getData();
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改试题")
    public Object edit(HttpServletRequest request,
                      HttpServletResponse response,
                      @RequestBody JSONObject json) throws Exception {
        return examServiceApi.questionEdit(json).getData();
    }

    @RequestMapping("/list")
    @Operation(desc = "管理试题列表查询")
    public Object list(HttpServletRequest request,
                       HttpServletResponse response,
                       QuestionQueryParams params) throws Exception {
        Object questionList = examServiceApi.questionList(params.getCourseId(), params.getCourseName(), params.getRealType(), params.getDbId(),
                params.getDifficulty(), params.getContent(), QuestionType.findByEnumName(params.getType()), Status.findByEnumName(params.getStatus()), null, null,
                null, null, null, null, null, null, null,
                null, super.getCurrentHostOrgId(request), params.getCurrent(), params.getSize()).getData();
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(questionList));
        List<JSONObject> records = jsonObject.getJSONArray("records").stream().map(x -> {
            JSONObject map = JSON.parseObject(JSON.toJSONString(x));
            String courseId = map.getString("courseId");
            Course course = courseMapper.selectById(courseId);
            if(course!=null){
                map.put("couseName", course.getName());
            }
            return map;
        }).collect(Collectors.toList());
        jsonObject.put("records", records);
        return jsonObject;
    }

    @RequestMapping("/getQuesDetailsById")
    @Operation(desc = "根据ID查询试题")
    public Object getQuesDetailsById(HttpServletRequest request,
                       HttpServletResponse response,
                       String id) throws Exception {
        //该接口调的是微服务的预览试题接口
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择试题");
        }
        return examServiceApi.questionGetQuesDetailsById(id).getData();
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除试题")
    public Object deleteById(HttpServletRequest request,
                             HttpServletResponse response,
                             String id) throws Exception {
        //该接口调的是微服务的预览试题接口
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择要删除的试题");
        }
        examServiceApi.questionDeleteById(id);
        return true;
    }

    @RequestMapping("/editStatus")
    @Operation(desc = "修改试题状态")
    public Object editStatus(HttpServletRequest request,
                             HttpServletResponse response,
                             @RequestParam String ids,
                             @RequestParam String status) throws Exception {
        examServiceApi.questionUpdateStatus(ids, status);
        return true;
    }

    @RequestMapping(value = "/import" ,method = RequestMethod.POST)
    @Operation(desc = "试题导入")
    public Object importQues(HttpServletRequest request,
                                 @RequestParam(value = "file", required = false) MultipartFile file,
                                 @RequestParam(required = false)String dbId,
                                 @RequestParam(required = false)String type) throws Exception {
        if(file==null){
            throw new BizException("请上传文件");
        }
        if(StringUtils.isEmpty(dbId)){
            throw new BizException("请选择题库");
        }
        if(StringUtils.isEmpty(type)) {
            throw new BizException("请选择导入类型");
        }
        String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        if(!(("txt".equals(type) && "txt".equals(suffix.replace(".",""))) ||
                ("excel".equals(type) && ("xls".equals(suffix.replace(".","")) || "xlsx".equals(suffix.replace(".","")))) ||
                ("word".equals(type) && ("doc".equals(suffix.replace(".","")) || "docx".equals(suffix.replace(".","")))))){
            throw new BizException("请选择模板对应格式类型的文件");
        }
        return (Integer) examServiceApi.questionImport(file, dbId, null, null, type, null).getData();//导入成功的试题数量
    }

    @RequestMapping("/count")
    @Operation(desc = "智能组卷页面试题数量查询")
    public Object count(HttpServletRequest request,
                       HttpServletResponse response,
                       QuestionQueryParams params) throws Exception {
        params.setStatus("ENABLE");
        return examServiceApi.questionCount(params.getCourseId(), params.getCourseName(), params.getRealType(), params.getDbId(),
                params.getDifficulty(), params.getContent(), QuestionType.findByEnumName(params.getType()), Status.ENABLE,
                null, null, null, null, null, null, null, null,
                null, null, super.getCurrentHostOrgId(request), params.getCurrent(), params.getSize()).getData();
    }

    @RequestMapping("/getTTDetails")
    @Operation(desc = "套题详情")
    public Object getTTDetails(HttpServletRequest request,
                        HttpServletResponse response,
                        String parentId) throws Exception {
        return examServiceApi.questionGetChildByPid(parentId).getData();
    }

    @RequestMapping("/selectRealType")
    @Operation(desc = "真实题型下拉接口")
    public Object selectRealType(HttpServletRequest request) {
        return examServiceApi.questionSelectRealType(super.getCurrentHostOrgId(request)).getData();
    }
}
