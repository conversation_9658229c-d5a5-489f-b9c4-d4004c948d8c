package com.xunw.zjxx.api.biz.controller;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.DoApproveQueryParams;
import com.xunw.zjxx.module.biz.params.ZypxCertiApproveQueryParams;
import com.xunw.zjxx.module.biz.service.CertiApproveService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/htgl/biz/certiApprove")
public class CertiApproveController extends BaseController {

    @Autowired
    private CertiApproveService certiApproveService;

    @RequestMapping("/list")
    @Operation(desc = "证书审批列表查询")
    public Object list(HttpServletRequest request, ZypxCertiApproveQueryParams params) throws Exception {
        params.setHostOrgId(getCurrentHostOrgId(request));
        return certiApproveService.list(params);
    }

    @RequestMapping("/doApprove")
    @Operation(desc = "批量审批")
    public Object doApprove(HttpServletRequest request, DoApproveQueryParams params) throws Exception {
        if(StringUtils.isEmpty(params.getId())){
            throw BizException.withMessage("请选择审批id");
        }
        if(StringUtils.isEmpty(params.getResult())){
            throw BizException.withMessage("请选择审批结果");
        }
        certiApproveService.doApprove(params);
        return true;
    }

    @RequestMapping("/certiDetail")
    @Operation(desc = "证书审批详情")
    public Object certiDetail(HttpServletRequest request,
                              @RequestParam(required = false) String id) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("证书审批id不能为空");
        }
        return certiApproveService.certiDetail(id);
    }
}
