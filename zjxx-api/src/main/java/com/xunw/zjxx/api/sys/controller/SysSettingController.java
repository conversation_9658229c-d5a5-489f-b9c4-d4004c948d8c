package com.xunw.zjxx.api.sys.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.enums.RoleEnum;
import com.xunw.zjxx.module.enums.SysSettingEnum;
import com.xunw.zjxx.module.sys.entity.Setting;
import com.xunw.zjxx.module.sys.service.SettingService;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/htgl/sys/xtsz")
public class SysSettingController extends BaseController {

    @Autowired
    private SettingService service;

    @RequestMapping("/getGlobalSetting")
    @Operation(desc = "获取系统设置")
    public Object cx(
            HttpServletRequest request) throws Exception {
        EntityWrapper<Setting> wrapper = new EntityWrapper<>();
        if (RoleEnum.valueOf(super.getLoginUser(request).getRole().getCode()) == RoleEnum.ADMIN) {
            wrapper.isNull("host_org_id");
        } else {
            wrapper.eq("host_org_id", super.getCurrentHostOrgId(request));
        }
        List<Setting> list = service.selectList(wrapper);
        if (list.size() == 0) {
            return new JSONObject();
        } else {
            Setting sysXtsz = list.get(0);
            if (StringUtils.isNotEmpty(sysXtsz.getContent())) {
                return JSONObject.fromObject(sysXtsz.getContent());
            } else {
                return new JSONObject();
            }
        }
    }

    @RequestMapping("/editGlobalSetting")
    @Operation(desc = "修改系统设置")
    public Object editGlobalSetting(
            HttpServletRequest request,
            @RequestBody JSONObject setting) throws Exception {
        for (Object key : setting.keySet()) {
            if (SysSettingEnum.findByEnumName(key.toString()) == null) {
                throw BizException.withMessage("不被识别的设置:" + key.toString());
            }
        }
        String hostOrgId = null;
        EntityWrapper<Setting> wrapper = new EntityWrapper<>();
        if (RoleEnum.valueOf(super.getLoginUser(request).getRole().getCode()) == RoleEnum.ADMIN) {
            wrapper.isNull("host_org_id");
        } else {
            hostOrgId = super.getCurrentHostOrgId(request);
            wrapper.eq("host_org_id", hostOrgId);
        }
        List<Setting> list = service.selectList(wrapper);
        LoginUser loginUser = getLoginUser(request);
        if (list.size() == 0) {
            Setting sysXtsz = new Setting();
            sysXtsz.setId(BaseUtil.generateId());
            sysXtsz.setContent(setting.toString());
            sysXtsz.setTime(new Date());
            sysXtsz.setUserId(loginUser.getId());
            sysXtsz.setHostOrgId(hostOrgId);
            service.insert(sysXtsz);
        } else {
            Setting sysXtsz = list.get(0);
            sysXtsz.setContent(setting.toString());
            sysXtsz.setTime(new Date());
            sysXtsz.setUserId(loginUser.getId());
            sysXtsz.setHostOrgId(hostOrgId);
            service.updateAllColumnById(sysXtsz);
        }
        return true;
    }
}
