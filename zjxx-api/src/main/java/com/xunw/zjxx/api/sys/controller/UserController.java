package com.xunw.zjxx.api.sys.controller;


import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.enums.Education;
import com.xunw.zjxx.module.enums.RoleEnum;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.dto.UserAddReqParams;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.entity.Role;
import com.xunw.zjxx.module.sys.params.UserQueryParams;
import com.xunw.zjxx.module.sys.service.OrgService;
import com.xunw.zjxx.module.sys.service.UserInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@RestController
@RequestMapping("/htgl/sys/user")
public class UserController extends BaseController {

    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private OrgService orgService;

    @RequestMapping("/list")
    @Operation(desc = "主办单位用户列表")
    public Object pageQuery(HttpServletRequest request,
                            UserQueryParams params) throws Exception {
        String hostOrgId = null;
        if(super.getLoginRoleEnum(request) == RoleEnum.ADMIN){//超管用户传入的orgId可能是主办单位ID或者主办单位下的部门ID
//            if (BaseUtil.isNotEmpty(params.getOrgId())){
//                Org topOrg = orgService.getTopOrg(params.getOrgId());
//                hostOrgId = topOrg.getId();
//                if (topOrg.getId().equals(params.getOrgId())){
//                    params.setOrgId(null);
//                }
//            }
        }
        else{
            hostOrgId = super.getCurrentHostOrgId(request);
        }
        params.setHostOrgId(hostOrgId);
        return userInfoService.pageQuery(params);
    }

    @RequestMapping("/add")
    @Operation(desc = "新增主办单位用户")
    public Object add(HttpServletRequest request,
                      UserAddReqParams params) throws Exception {
        if (StringUtils.isEmpty(params.getUsername())) {
            throw BizException.withMessage("用户账户名不能为空");
        }
        if (StringUtils.isEmpty(params.getName())) {
            throw BizException.withMessage("姓名不能为空");
        }
        if (StringUtils.isEmpty(params.getMobile())) {
            throw BizException.withMessage("手机号不能为空");
        }
        if (StringUtils.isEmpty(params.getPassword())) {
            throw BizException.withMessage("密码不能为空");
        }
        if (StringUtils.isEmpty(params.getQrmm())) {
            throw BizException.withMessage("确认密码不能为空");
        }
        if (!Objects.equals(params.getPassword(), params.getQrmm())) {
            throw BizException.withMessage("密码与确认密码不一致，请重新输入");
        }
        if (StringUtils.isEmpty(params.getRole())) {
            throw BizException.withMessage("角色不能为空");
        }
        String hostOrgId = null;
        if (StringUtils.isNotEmpty(params.getOrgId())) {
            Org topOrg = orgService.getTopOrg(params.getOrgId());
            hostOrgId = topOrg.getId();
        } else {
            hostOrgId = super.getCurrentHostOrgId(request);
        }
        userInfoService.add(params, hostOrgId, super.getLoginUserId(request));
        return true;
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改主办单位用户")
    public Object edit(HttpServletRequest request,
                       @RequestParam(required = false) String id,
                       @RequestParam(required = false) String name,
                       @RequestParam(required = false) String mobile,
                       @RequestParam(required = false) String role,
                       @RequestParam(required = false) String sfzh,
                       @RequestParam(required = false) Education education,
                       @RequestParam(required = false) String isOut,
                       @RequestParam(required = false) String workUnit,
                       @RequestParam(required = false) String gw,
                       @RequestParam(required = false) String zw,
                       @RequestParam(required = false) String zc,
                       @RequestParam(required = false) String workPhoto,
                       @RequestParam(required = false) String brief) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("姓名不能为空");
        }
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.withMessage("手机号不能为空");
        }
        if (StringUtils.isEmpty(role)) {
            throw BizException.withMessage("权限不能为空");
        }
        userInfoService.edit(id, name, mobile, role, sfzh, education, isOut, workUnit, gw, zw, zc, workPhoto, brief, super.getLoginUserId(request));
        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "根据id获取主办单位用户")
    public Object getById(HttpServletRequest request,
                          @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        return userInfoService.getById(id);
    }

    @RequestMapping("/enable")
    @Operation(desc = "主办单位用户启用/禁用")
    public Object enable(HttpServletRequest request,
                         @RequestParam(required = false) String id,
                         @RequestParam(required = false) Status status) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        if (status == null) {
            throw BizException.withMessage("状态不能为空");
        }
        userInfoService.enable(id, status);
        return true;
    }

    @RequestMapping("/delete")
    @Operation(desc = "主办单位用户删除")
    public Object delete(HttpServletRequest request,
                         @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        userInfoService.delete(id);
        return true;
    }

    @RequestMapping("/resetPassword")
    @Operation(desc = "主办单位用户重置密码")
    public Object resetPassword(HttpServletRequest request,
                                @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        userInfoService.resetPassword(id);
        return true;
    }
}
