package com.xunw.zjxx.api.inf.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.FileHelper;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.entity.Material;
import com.xunw.zjxx.module.inf.params.CoursewareQueryParams;
import com.xunw.zjxx.module.inf.service.CourseService;
import com.xunw.zjxx.module.inf.service.CoursewareService;
import com.xunw.zjxx.module.inf.service.MaterialService;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.rpc.LearningCoursewareServiceApi;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/htgl/inf/zypx/kj")
public class CoursewareController extends BaseController {

    @Autowired
    private AttConfig attConfig;
    @Autowired
    private CoursewareService coursewareService;
    @Autowired
    private CourseService courseService;
    @Autowired
    private LearningCoursewareServiceApi learningCoursewareServiceApi;
    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private MaterialService materialService;


    @RequestMapping("/list")
    @Operation(desc = "课件列表")
    public Object list(HttpServletRequest request,
                       CoursewareQueryParams params) throws Exception {
        Object resultMsg = learningCoursewareServiceApi.coursewareList(null, params.getKeyword(), params.getStatus(),
                null, super.getCurrentHostOrgId(request), null, params.getCurrent(), params.getSize()).getData();
        JSONArray jsonArray = JSON.parseObject(JSON.toJSONString(resultMsg)).getJSONArray("records");
        for(int i =0; i< jsonArray.size(); i++) {
            String courseId = jsonArray.getJSONObject(i).getString("courseId");
            Course course = courseService.selectById(courseId);
            jsonArray.getJSONObject(i).put("courseName",course != null ? course.getName() : null);
        }
        params.setTotal(JSON.parseObject(JSON.toJSONString(resultMsg)).getLong("total"));
        params.setRecords(jsonArray);
        return params;
    }

    @RequestMapping("/getDetailsById")
    @Operation(desc = "根据ID查询课件")
    public Object getDetailsById(HttpServletRequest request,
                                 HttpServletResponse response,
                                 String courseId, String id) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("courseId", courseId);
        map.put("id", id);
        Object resp = learningCoursewareServiceApi.coursewareGetById(id, courseId).getData();
        return JSON.parseObject(JSON.toJSONString(resp));
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改课件")
    public Object edit(HttpServletRequest request,
                       @RequestBody JSONObject jsonObject) throws Exception {
        if (jsonObject.get("id") == null) {
            throw BizException.withMessage("请选择课件");
        }
        if (jsonObject.get("courseId") == null) {
            throw BizException.withMessage("请选择课程");
        }
        if (jsonObject.get("teacherName") == null) {
            throw BizException.withMessage("请选择讲师");
        }
        if (jsonObject.get("status") == null) {
            throw BizException.withMessage("请选择状态");
        }
        if (jsonObject.get("chapters") == null) {
            throw BizException.withMessage("请选择章节");
        }
        Course course = courseService.selectById(jsonObject.getString("courseId"));
        if (course == null) {
            throw BizException.withMessage("课程不存在");
        }
        coursewareService.edit(jsonObject, super.getCurrentHostOrgId(request), super.getAccessToken(request));
        return true;
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除课件")
    public Object deleteById(
            HttpServletRequest request,
            @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择要删除的课件");
        }
        coursewareService.deleteById(id, super.getCurrentHostOrgId(request),super.getAccessToken(request));
        return true;
    }

    @RequestMapping("/add")
    @Operation(desc = "新增课件")
    public Object add(HttpServletRequest request,
                      @RequestBody JSONObject jsonObject) throws Exception {
        if (jsonObject.get("courseId") == null) {
            throw BizException.withMessage("请选择课程");
        }
        if (jsonObject.get("teacherName") == null) {
            throw BizException.withMessage("请选择讲师");
        }
        if (jsonObject.get("status") == null) {
            throw BizException.withMessage("请选择状态");
        }
        if (jsonObject.get("chapters") == null) {
            throw BizException.withMessage("请选择章节");
        }
        coursewareService.add(jsonObject, super.getCurrentHostOrgId(request), super.getAccessToken(request));
        return true;
    }

    @RequestMapping("/enable")
    @Operation(desc = "课件导入", loginRequired = false)
    public Object coursewareImport(HttpServletRequest request,
                                   @RequestParam("id") String id,
                                   @RequestParam("courseId") String courseId,
                                   @RequestParam("teacherName") String teacherName,
                                   @RequestParam("lecturerPhoto") String lecturerPhoto,
                                   @RequestParam("status") Status status,
                                   @RequestParam("file") MultipartFile file) throws Exception {
        if (file == null) {
            throw BizException.withMessage("在请求中没有检测到章节文件");
        }
        if (StringUtils.isEmpty(courseId)) {
            throw BizException.withMessage("请选择课程");
        }
        if (StringUtils.isEmpty(teacherName)) {
            throw BizException.withMessage("请选择讲师");
        }
        if (status == null) {
            throw BizException.withMessage("请选择状态");
        }
        Course course = courseService.selectById(courseId);
        if (course == null) {
            throw BizException.withMessage("课程不存在");
        }
        coursewareService.coursewareImport(id, courseId, teacherName,lecturerPhoto, status, file, super.getCurrentHostOrgId(request), super.getAccessToken(request));
        return true;
    }


    @RequestMapping("/material/add")
    @Operation(desc = "课件上传学习资料", loginRequired = false)
    public Object uploadImage(HttpServletRequest request,
                              @RequestParam("id") String id,
                              @RequestParam("file") MultipartFile file) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("课时id不能为空");
        }
        if (ObjectUtils.isEmpty(file) || file.getSize() <= 0) {
            throw BizException.withMessage("在请求中没有检测到文件");
        }
        String fileName = file.getOriginalFilename();
        String ext = FileHelper.getExtension(fileName);
        String path = attConfig.getRootDir() + "/upload/material/" + new SimpleDateFormat("yyyyMMddHH").format(new Date());
        String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext;
        String filePath = FileHelper.storeFile(path, file.getInputStream(), newFileName);
        learningCoursewareServiceApi.coursewareAddStudyInfo(id, filePath, fileName, BaseUtil.getFileShowSize(file.getSize()));
        return true;
    }

    @RequestMapping("/getMaterials")
    @Operation(desc = "获取学习资料")
    public Object getMaterials(HttpServletRequest request,
                               @RequestParam(required = false) String coursewareId,
                               @RequestParam(required = false) String lessonId) throws Exception {
        if ((StringUtils.isEmpty(coursewareId) && StringUtils.isEmpty(lessonId))
                || (StringUtils.isNotEmpty(coursewareId) && StringUtils.isNotEmpty(lessonId))) {
            throw BizException.withMessage("课时id，课件id同时有且仅存在一个");
        }
        return learningCoursewareServiceApi.coursewareGetStudyMaterials(coursewareId, lessonId).getData();
    }

    @RequestMapping("/material/delete")
    @Operation(desc = "删除学习资料")
    public Object removeMaterial(HttpServletRequest request, @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择要删除的学习资料");
        }
        learningCoursewareServiceApi.coursewareRemoveStudyMaterial(id);
        return true;
    }

    @RequestMapping("/getPopQues")
    @Operation(desc = "通过题库ids查询题库信息")
    public Object addExtConfig(HttpServletRequest request,
                               @RequestParam("ids") String ids) throws Exception {
        List list = new ArrayList();
        if (StringUtils.isEmpty(ids)) {
            throw BizException.withMessage("题库ids不能为空");
        }
        for (String id : ids.split(",")) {
            list.add(examServiceApi.questionDbPreview(id).getData());
        }
        return list;
    }

    @RequestMapping("/popQues/add")
    @Operation(desc = "新增课件弹题配置")
    public Object addExtConfig(HttpServletRequest request,
                               @RequestParam("id") String id,
                               @RequestParam("dbIds") String dbIds) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("课时id不能为空");
        }
        if (StringUtils.isEmpty(dbIds)) {
            throw BizException.withMessage("题库id不能为空");
        }
        learningCoursewareServiceApi.coursewareAddExtConfig(id, dbIds);
        return true;
    }

    @RequestMapping("/popQues/delete")
    @Operation(desc = "删除课件弹题配置")
    public Object deleteExtConfig(HttpServletRequest request,
                                  @RequestParam("id") String id,
                                  @RequestParam("dbId") String dbId) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("课时id不能为空");
        }
        if (StringUtils.isEmpty(dbId)) {
            throw BizException.withMessage("题库id不能为空");
        }
        learningCoursewareServiceApi.coursewareRemoveExtConfig(id, dbId);
        return true;
    }

    @RequestMapping("/playQues/add")
    @Operation(desc = "弹题及作答保存")
    public Object addPlayQues(HttpServletRequest request,
                              @RequestParam("recordId") String recordId,
                              @RequestParam("questionId") String questionId,
                              @RequestParam("content") String content,
                              @RequestParam("isCorrect") Integer isCorrect) throws Exception {
        if (StringUtils.isEmpty(recordId)) {
            throw BizException.withMessage("学习记录id不能为空");
        }
        if (StringUtils.isEmpty(questionId)) {
            throw BizException.withMessage("试题id不能为空");
        }
        if (StringUtils.isEmpty(content)) {
            throw BizException.withMessage("答题内容不能为空");
        }
        if (isCorrect == null) {
            throw BizException.withMessage("答案是否正确不能为空");
        }
        learningCoursewareServiceApi.studyAddPlayQues(recordId, questionId, content, isCorrect);
        return true;
    }

    @RequestMapping("/choose/material")
    @Operation(desc = "选择材料")
    public Object addPlayQues(HttpServletRequest request, @RequestParam(required = false) String lessonId, @RequestParam(required = false) String materialIds) {
        if (StringUtils.isEmpty(lessonId)) {
            throw BizException.withMessage("课时id不能为空");
        }
        if (StringUtils.isEmpty(materialIds)) {
            throw BizException.withMessage("素材id不能为空");
        }
        for (String id : materialIds.split(",")) {
            Material material = materialService.selectById(id);
            learningCoursewareServiceApi.coursewareAddStudyInfo(lessonId, material.getUrl(), material.getName(), BaseUtil.getFileShowSize(material.getSize()));
        }
        return true;
    }

}
