package com.xunw.zjxx.api.biz.controller;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.InvoiceQueryParams;
import com.xunw.zjxx.module.biz.service.InvoiceService;

@RestController
@RequestMapping("/htgl/biz/zypx/invoice")
public class InvoiceController extends BaseController {

    @Autowired
    private InvoiceService service;

    @RequestMapping("/list")
    @Operation(desc = "发票列表")
    public Object list(HttpServletRequest request,
                       InvoiceQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return service.list(params);
    }

    @RequestMapping("/upload")
    @Operation(desc = "上传发票")
    public Object upload(HttpServletRequest request,
                         @RequestParam(required = false) String id,
                         @RequestParam(required = false) String file
                         ) throws Exception {
        if(StringUtils.isEmpty(file)){
            throw BizException.withMessage("请选择文件");
        }
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择发票");
        }
        return service.upload(id, file);
    }

    @RequestMapping("/getById")
    @Operation(desc = "发票详情")
    public Object getById(HttpServletRequest request,
                       String id) throws Exception {
        return service.getById(id);
    }

}
