package com.xunw.zjxx.api.inf.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.module.enums.Category;
import com.xunw.zjxx.module.inf.params.TypeQueryParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.inf.entity.Type;
import com.xunw.zjxx.module.inf.params.CourseQueryParams;
import com.xunw.zjxx.module.inf.service.TypeService;

@RestController
@RequestMapping("/htgl/inf/zypx/type")
public class CourseTypeController extends BaseController {

    @Autowired
    private TypeService service;

    @RequestMapping("/list")
    @Operation(desc = "课程分类列表")
    public Object list(
            HttpServletRequest request,
            TypeQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        Page page = service.pageQueryList(params);
        return page;
    }

    @RequestMapping("/add")
    @Operation(desc = "新增课程分类")
    public Object add(
            HttpServletRequest request,
            @RequestParam(required = false) String parentId,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String sort
    ) throws Exception {
        if(StringUtils.isEmpty(parentId)){
            throw BizException.withMessage("请选择父类型");
        }
        if(StringUtils.isEmpty(name)){
            throw BizException.withMessage("请输入类型名称");
        }
        if(StringUtils.isEmpty(sort)){
            throw BizException.withMessage("请输入排序值");
        }
        String id = BaseUtil.generateId();
        Type type = new Type();
        type.setId(id);
        type.setName(name);
        type.setSort(Integer.valueOf(sort));
        Type type1 = service.selectById(parentId);
        type.setCategory(type1.getCategory());
        type.setParentId(parentId);
        type.setCreateTime(new Date());
        type.setCreatorId(super.getLoginUserId(request));
        type.setHostOrgId(super.getCurrentHostOrgId(request));
        service.insert(type);
        return type;
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改课程分类")
    public Object edit(
            HttpServletRequest request,
            @RequestParam(required = false) String id,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer sort
    ) throws Exception {
        if(StringUtils.isEmpty(name)){
            throw BizException.withMessage("请输入类型名称");
        }
        if(sort == null){
            throw BizException.withMessage("请输入排序值");
        }
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择要修改的课程分类");
        }
        Type type = service.selectById(id);
        type.setName(name);
        type.setSort(sort);
        service.updateById(type);
        return true;
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除课程分类")
    public Object deleteById(
            HttpServletRequest request,
            @RequestParam(required = false) String id
    ) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择要删除的课程分类");
        }
        service.delete(id);
        return true;
    }

    @RequestMapping("/tree")
    @Operation(desc = "课程分类树查询")
    public Object tree(
            HttpServletRequest request) throws Exception {
      return service.tree(super.getCurrentHostOrgId(request));
    }

}
