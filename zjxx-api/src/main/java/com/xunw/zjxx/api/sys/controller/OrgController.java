package com.xunw.zjxx.api.sys.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.config.UserAuthServiceConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.dto.OrgTree;
import com.xunw.zjxx.module.enums.OrgType;
import com.xunw.zjxx.module.enums.RoleEnum;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.params.OrgQueryParams;
import com.xunw.zjxx.module.sys.service.OrgService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/htgl/sys/")
public class OrgController extends BaseController {

    @Autowired
    private UserAuthServiceConfig userAuthServiceConfig;
    @Autowired
    private OrgService orgService;

    @RequestMapping("/hostOrg/list")
    @Operation(desc = "主办单位或主办单位部门查询")
    public Object list(HttpServletRequest request,
                       OrgQueryParams params) throws Exception {
        if (StringUtils.isEmpty(params.getParentId()) && RoleEnum.valueOf(super.getLoginRole(request).getCode()) == RoleEnum.HOST_ORG) {
        	params.setParentId(super.getCurrentHostOrgId(request));
		}
        params.setOrgType(OrgType.HOST_ORG);
        params.setIsDeep(Constants.YES);
        return orgService.list(params);
    }

    @RequestMapping("/hostOrg/tree")
    @Operation(desc = "主办单位树")
    public Object tree(HttpServletRequest request) throws Exception {
        String loginRoleCode = super.getLoginRole(request).getCode();
        List<OrgTree> trees = orgService.tree(loginRoleCode!=null && RoleEnum.valueOf(loginRoleCode) == RoleEnum.HOST_ORG ? super.getCurrentHostOrgId(request):null, OrgType.HOST_ORG);
        return trees;
    }

    /**
     * 主办单位下拉框
     */
    @RequestMapping("/hostOrg/select")
    @Operation(desc = "主办单位下拉框")
    public Object hostOrgSelect(HttpServletRequest request,OrgQueryParams params) throws Exception {
        params.setSize(Integer.MAX_VALUE);
        params.setCurrent(Constants.DEFAULT_PAGE_NUMBER);
        params.setOrgType(OrgType.HOST_ORG);
        params.setIsParent(Constants.YES);
        return orgService.list(params).getRecords();
    }

    /**
     * 组织机构下拉框
     */
    @RequestMapping("/relatedOrg/select")
    @Operation(desc = "组织机构下拉框")
    public Object orgSelect(HttpServletRequest request,OrgQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        params.setOrgType(OrgType.ORG);
        params.setIsParent(Constants.YES);
        params.setSize(Integer.MAX_VALUE);
        params.setCurrent(Constants.DEFAULT_PAGE_NUMBER);
        return orgService.list(params).getRecords();
    }
    
    /**
     * 新增组织机构或下级部门
     */
    @RequestMapping("/relatedOrg/add")
    @Operation(desc = "新增组织机构或下级部门")
    public Object add(HttpServletRequest request,
                      @RequestParam(required = false) String parentId,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) String code,
                      @RequestParam(required = false) String contact,
                      @RequestParam(required = false) String telephone,
                      @RequestParam(required = false) String remark) throws Exception {
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("机构名称不能为空");
        }
        orgService.addOrg(parentId, name, code, contact, telephone, remark, super.getCurrentHostOrgId(request));
        return true;
    }

    @RequestMapping("/relatedOrg/edit")
    @Operation(desc = "修改组织机构或下级部门")
    public Object edit(HttpServletRequest request,
                      @RequestParam(required = false) String id,
                      @RequestParam(required = false) String parentId,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) String code,
                      @RequestParam(required = false) String contact,
                      @RequestParam(required = false) String telephone,
                      @RequestParam(required = false) String remark) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("机构id不能为空");
        }
        orgService.editOrg(id, name, code, contact, telephone, remark);
        return true;
    }
    
    /**
     * 组织机构列表查询
     */
    @RequestMapping("/relatedOrg/list")
    @Operation(desc = "组织机构列表查询")
    public Object listRelatedOrg(HttpServletRequest request,
                      OrgQueryParams params) throws Exception {
        params.setHostOrgId(BaseUtil.isEmpty(params.getParentId()) ? super.getCurrentHostOrgId(request) : null);
        params.setOrgType(OrgType.ORG);
        return orgService.list(params);
    }
    
    /**
     * 组织机构树查询
     */
    @RequestMapping("/relatedOrg/tree")
    @Operation(desc = "组织机构树查询")
    public Object treeRelatedOrg(HttpServletRequest request) throws Exception {
         List<OrgTree> trees = orgService.relatedOrgTree(super.getCurrentHostOrgId(request));
         return trees;
    }

    /**
     * 新增主办单位部门
     */
    @RequestMapping("/hostOrg/addTop")
    @Operation(desc = "新增主办单位")
    public Object addHostOrgTop(HttpServletRequest request,
                             @RequestParam(required = false) String name,
                             @RequestParam(required = false) String code,
                             @RequestParam(required = false) String remark,
                             @RequestParam(required = false) String portalDomain,
                             @RequestParam(required = false) String smsSign,
                             @RequestParam(required = false) String adminDomain,
                             @RequestParam(required = false) String adminSysName,
                             @RequestParam(required = false) String portalSysName,
                             @RequestParam(required = false) String adminLogo,
                             @RequestParam(required = false) String portalLogo,
                             @RequestParam(required = false) String appletQrCode) throws Exception {
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("机构名称不能为空");
        }
        orgService.addHostOrgTop(name, code, remark, portalDomain,smsSign,adminDomain,adminSysName,portalSysName,adminLogo,portalLogo,appletQrCode);
        return true;
    }

    /**
     * 修改主办单位部门
     */
    @RequestMapping("/hostOrg/editTop")
    @Operation(desc = "修改主办单位")
    public Object editHostOrgTop(HttpServletRequest request,
                                @RequestParam(required = false) String id,
                                @RequestParam(required = false) String name,
                                @RequestParam(required = false) String code,
                                @RequestParam(required = false) String remark,
                                @RequestParam(required = false) String portalDomain,
                                @RequestParam(required = false) String smsSign,
                                @RequestParam(required = false) String adminDomain,
                                @RequestParam(required = false) String adminSysName,
                                @RequestParam(required = false) String portalSysName,
                                @RequestParam(required = false) String adminLogo,
                                @RequestParam(required = false) String portalLogo,
                                @RequestParam(required = false) String appletQrCode) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("机构id不能为空");
        }
        orgService.editHostOrgTop(id,name, code, remark, portalDomain,smsSign,adminDomain,adminSysName,portalSysName,adminLogo,portalLogo,appletQrCode);
        return true;
    }

    /**
     * 新增主办单位下级部门
     */
    @RequestMapping("/hostOrg/add")
    @Operation(desc = "新增主办单位下级部门")
    public Object addHostOrg(HttpServletRequest request,
                      @RequestParam(required = false) String parentId,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) String code,
                      @RequestParam(required = false) String contact,
                      @RequestParam(required = false) String telephone,
                      @RequestParam(required = false) String remark) throws Exception {
        if (RoleEnum.valueOf(super.getLoginRole(request).getCode()) == RoleEnum.HOST_ORG && StringUtils.isEmpty(parentId)) {
            throw BizException.withMessage("父节点ID不能为空");
        }
        if (StringUtils.isEmpty(name)) {
        	throw BizException.withMessage("机构名称不能为空");
        }
        orgService.addHostOrg(parentId, name, code, contact, telephone, remark);
        return true;
    }

    @RequestMapping("/hostOrg/edit")
    @Operation(desc = "修改主办单位下级部门")
    public Object editHostOrg(HttpServletRequest request,
                      @RequestParam(required = false) String id,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) String code,
                      @RequestParam(required = false) String contact,
                      @RequestParam(required = false) String telephone,
                      @RequestParam(required = false) String remark) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("机构id不能为空");
        }
        orgService.editOrg(id, name, code, contact, telephone, remark);
        return true;
    }
    
    @RequestMapping("/org/deleteById")
    @Operation(desc = "删除机构")
    public Object deleteById(HttpServletRequest request,
                      @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("机构id不能为空");
        }
        Org org = orgService.selectById(id);
        if (org == null) {
            throw BizException.withMessage("机构不存在");
        }
        if (orgService.countUserByOrgId(id) > 0) {
            throw BizException.withMessage("操作失败，因为当前机构下存在用户信息，不能删除");
        }
        orgService.deleteById(id);
        return true;
    }
    
    @RequestMapping("/org/getById")
    @Operation(desc = "根据id获取机构")
    public Object getById(HttpServletRequest request,
                          @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("机构id不能为空");
        }
        return orgService.selectById(id);
    }
}
