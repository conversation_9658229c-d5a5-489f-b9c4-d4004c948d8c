package com.xunw.zjxx.api.sys.controller;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.enums.PayPlatform;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.entity.BankConfig;
import com.xunw.zjxx.module.sys.params.BankConfigQueryParams;
import com.xunw.zjxx.module.sys.service.BankConfigService;

import net.sf.json.JSONObject;

/**
 * 收款方配置
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/sys/bankConfig")
public class BankConfigController extends BaseController {
	
	@Autowired
	private BankConfigService service;
	
	/**
	 * 列表
     */
    @RequestMapping("/list")
    @Operation(desc = "收款方配置列表查询")
    public Object list(
            HttpServletRequest request,
            BankConfigQueryParams params) throws Exception {
    	EntityWrapper<BankConfig> wrapper = new EntityWrapper<>();
    	if (StringUtils.isNotEmpty(params.getBankId())) {
    		wrapper.eq("bank_id", params.getBankId());
		}
    	Page page = service.selectPage(params, wrapper);
    	return page;
    }
    
    /**
	 * 新增收款方配置
	 * 注意，本系统支付宝支付签名加密使用的是RSA2签名加密算法
     */
    @RequestMapping("/add")
    @Operation(desc = "新增收款方配置")
    public Object add(
			HttpServletRequest request,
			@RequestParam(required = false) String bankId,    //收款方ID
			@RequestParam(required = false) PayPlatform payPlat,    //支付平台
			@RequestParam(required = false) Status status,    //状态
			@RequestParam(required = false) String ali_app_id,    //支付宝应用ID
			@RequestParam(required = false) String ali_merchant_private_key,    //支付宝商户RSA2私钥
			@RequestParam(required = false) String ali_alipay_public_key,    //支付宝公钥
			@RequestParam(required = false) String ali_notify_url,    //支付宝异步通知回调地址
			@RequestParam(required = false) String ali_return_url,    //支付宝支付完毕后的同步跳转地址
			@RequestParam(required = false) String wx_app_id,    //微信公众号-开发者中心-应用ID
			@RequestParam(required = false) String wx_app_secret,    //微信公众号-开发者中心-应用秘钥
			@RequestParam(required = false) String wx_mch_id,    //微信支付商户号
			@RequestParam(required = false) String wx_api_key,    //微信支付API秘钥
			@RequestParam(required = false) String wx_notify_url,    //微信支付异步通知地址
			@RequestParam(required = false) String ccb_merchantid,    //建设银行商户代码
			@RequestParam(required = false) String ccb_postid,    //建设银行商户柜台代码
			@RequestParam(required = false) String ccb_branchid,    //建设银行分行代码
			@RequestParam(required = false) String ccb_pub,    //商户柜台公钥
			@RequestParam(required = false) String icbc_appid,
			@RequestParam(required = false) String icbc_merchantid,
			@RequestParam(required = false) String icbc_mer_prtcl_no,
			@RequestParam(required = false) String icbc_public_key,
			@RequestParam(required = false) String icbc_private_key,
			@RequestParam(required = false) String icbc_mer_url
            ) throws Exception {
    	if (StringUtils.isEmpty(bankId)) {
    		throw BizException.withMessage("请传入收款方ID");
		}
    	if(payPlat == null){
    		throw BizException.withMessage("请选择支付平台");
    	}
    	String setttings = null;
    	if(payPlat == PayPlatform.ALI){
    		if(StringUtils.isEmpty(ali_app_id)){
        		throw BizException.withMessage("请输入支付宝应用ID");
    		}
    		if(StringUtils.isEmpty(ali_merchant_private_key)){
        		throw BizException.withMessage("请输入支付宝商户RSA2私钥");
    		}
    		if(StringUtils.isEmpty(ali_alipay_public_key)){
        		throw BizException.withMessage("请输入支付宝公钥");
    		}
    		if(StringUtils.isEmpty(ali_notify_url)){
        		throw BizException.withMessage("请输入支付宝异步通知回调地址");
    		}
    		if(StringUtils.isEmpty(ali_return_url)){
        		throw BizException.withMessage("请输入支付宝支付完毕后的同步跳转地址");
    		}
    		Map<String, String> config = new HashMap<String, String>();
    		config.put("ali_app_id", ali_app_id);
    		config.put("ali_merchant_private_key", ali_merchant_private_key);
    		config.put("ali_alipay_public_key", ali_alipay_public_key);
    		config.put("ali_notify_url", ali_notify_url);
    		config.put("ali_return_url", ali_return_url);
    		setttings = JSONObject.fromObject(config).toString();
    	}
    	else if(payPlat == PayPlatform.WX){
    		if(StringUtils.isEmpty(wx_app_id)){
        		throw BizException.withMessage("请输入微信支付应用ID");
    		}
    		if(StringUtils.isEmpty(wx_mch_id)){
        		throw BizException.withMessage("请输入微信支付商户号");
    		}
    		if(StringUtils.isEmpty(wx_api_key)){
        		throw BizException.withMessage("请输入微信支付API秘钥");
    		}
    		if(StringUtils.isEmpty(wx_notify_url)){
        		throw BizException.withMessage("请输入微信支付异步通知地址");
    		}
    		Map<String, String> config = new HashMap<String, String>();
    		config.put("wx_app_id", wx_app_id);
    		config.put("wx_app_secret", wx_app_secret);//JSAPI支付必须配置
    		config.put("wx_mch_id", wx_mch_id);
    		config.put("wx_api_key", wx_api_key);
    		config.put("wx_notify_url", wx_notify_url);
    		setttings = JSONObject.fromObject(config).toString();
    	}
    	else if(payPlat == PayPlatform.CCB) {
    		if(StringUtils.isEmpty(ccb_merchantid)){
        		throw BizException.withMessage("请输入商户代码(MERCHANTID)");
    		}
    		if(StringUtils.isEmpty(ccb_postid)){
        		throw BizException.withMessage("请输入商户柜台代码(POSID)");
    		}
    		if(StringUtils.isEmpty(ccb_branchid)){
        		throw BizException.withMessage("请输入分行代码(BRANCHID)");
    		}
    		if(StringUtils.isEmpty(ccb_pub)){
        		throw BizException.withMessage("请输入商户柜台公钥(PUB)");
    		}
    		Map<String, String> config = new HashMap<String, String>();
    		config.put("ccb_merchantid", ccb_merchantid);
    		config.put("ccb_postid", ccb_postid);
    		config.put("ccb_branchid", ccb_branchid);
    		config.put("ccb_pub", ccb_pub);
    		setttings = JSONObject.fromObject(config).toString();
    	}
    	else if(payPlat == PayPlatform.ICBC) {
    		if(StringUtils.isEmpty(icbc_appid)){
    			throw BizException.withMessage("请输入APP_ID");
    		}
    		if(StringUtils.isEmpty(icbc_merchantid)){
    			throw BizException.withMessage("请输入商户编号(merchantid)");
    		}
    		if(StringUtils.isEmpty(icbc_mer_prtcl_no)){
    			throw BizException.withMessage("请输入协议编号(mer_prtcl_no)");
    		}
    		if(StringUtils.isEmpty(icbc_public_key)){
    			throw BizException.withMessage("请输入公钥(public_key)");
    		}
    		if(StringUtils.isEmpty(icbc_private_key)){
    			throw BizException.withMessage("请输入公钥(private_key)");
    		}
    		if(StringUtils.isEmpty(icbc_mer_url)){
    			throw BizException.withMessage("请输入异步通知地址(mer_url)");
    		}
    		Map<String, String> config = new HashMap<String, String>();
    		config.put("icbc_appid", icbc_appid);
    		config.put("icbc_merchantid", icbc_merchantid);
    		config.put("icbc_mer_prtcl_no", icbc_mer_prtcl_no);
    		config.put("icbc_public_key", icbc_public_key);
    		config.put("icbc_private_key", icbc_private_key);
    		config.put("icbc_mer_url", icbc_mer_url);
    		setttings = JSONObject.fromObject(config).toString();
    	}
    	else {
    		throw BizException.withMessage("系统不支持您选择的支付平台");
		}
    	if(StringUtils.isEmpty(setttings)){
    		throw BizException.withMessage("传入的支付配置参数错误,请仔细检查");
		}
		BankConfig bankConfig = new BankConfig();
		bankConfig.setId(BaseUtil.generateId2());
		bankConfig.setBankId(bankId);
		bankConfig.setPayPlat(payPlat);
		bankConfig.setPaySettting(setttings);
		bankConfig.setStatus(status);
		bankConfig.setCreateTime(new Date());
		bankConfig.setCreatorId(super.getLoginUserId(request));
		service.insert(bankConfig);
		return true;
    }
    
    /**
     * 获取收款方配置
     */
    @RequestMapping("/getById")
    @Operation(desc = "获取收款方配置")
	public Object getById(HttpServletRequest request,
			@RequestParam(required = false) String id){
		if(StringUtils.isEmpty(id)){
    		throw BizException.withMessage("请选择一条数据记录");
		}
		BankConfig bankConfig = service.selectById(id);
		bankConfig.setSetting(JSONObject.fromObject(bankConfig.getPaySettting()));
		return bankConfig;
	}
    

    /**
     * 删除收款方配置
     */
    @RequestMapping("/deleteById")
    @Operation(desc = "删除收款方配置")
	public Object deleteById(HttpServletRequest request,
			@RequestParam(required = false) String id){
		if(StringUtils.isEmpty(id)){
    		throw BizException.withMessage("请选择一条数据记录");
		}
		service.deleteById(id);
		return true;
	}
    
    /**
	 * 修改收款方配置
     */
    @RequestMapping("/edit")
    @Operation(desc = "修改收款方配置")
    public Object edit(
            HttpServletRequest request,
            @RequestParam(required = false) String id,	//记录ID
            @RequestParam(required = false) PayPlatform payPlat,	//支付平台
            @RequestParam(required = false) Status status,	//状态
            @RequestParam(required = false) String ali_app_id,	//支付宝应用ID
            @RequestParam(required = false) String ali_merchant_private_key,	//支付宝商户RSA2私钥
            @RequestParam(required = false) String ali_alipay_public_key,	//支付宝公钥
            @RequestParam(required = false) String ali_notify_url,	//支付宝异步通知回调地址
            @RequestParam(required = false) String ali_return_url,	//支付宝支付完毕后的同步跳转地址
            @RequestParam(required = false) String wx_app_id,	//微信支付应用ID
            @RequestParam(required = false) String wx_app_secret,	//微信支付应用秘钥
            @RequestParam(required = false) String wx_mch_id,	//微信支付商户号
            @RequestParam(required = false) String wx_api_key,	//微信支付API秘钥
            @RequestParam(required = false) String wx_notify_url,	//微信支付异步通知地址
            @RequestParam(required = false) String ccb_merchantid,	//建设银行商户代码
            @RequestParam(required = false) String ccb_postid,	//建设银行商户柜台代码
            @RequestParam(required = false) String ccb_branchid,	//建设银行分行代码
            @RequestParam(required = false) String ccb_pub,	//商户柜台公钥后30位
            @RequestParam(required = false) String icbc_appid,
            @RequestParam(required = false) String icbc_merchantid,
            @RequestParam(required = false) String icbc_mer_prtcl_no,
            @RequestParam(required = false) String icbc_public_key,
            @RequestParam(required = false) String icbc_private_key,
            @RequestParam(required = false) String icbc_mer_url
            ) throws Exception {
    	if(StringUtils.isEmpty(id)){
    		throw BizException.withMessage("请选择一条数据记录");
		}
    	if (payPlat == null) {
    		throw BizException.withMessage("请选择一个收款平台");
		}
		BankConfig bankConfig = service.selectById(id);
		if (bankConfig == null){
    		throw BizException.withMessage("选择的数据记录在系统中不存在");
		}
		String settings = null;
    	if (payPlat == PayPlatform.ALI){
    		if(StringUtils.isEmpty(ali_app_id)){
        		throw BizException.withMessage("请输入支付宝应用ID");
    		}
    		if(StringUtils.isEmpty(ali_merchant_private_key)){
        		throw BizException.withMessage("请输入支付宝商户RSA2私钥");
    		}
    		if(StringUtils.isEmpty(ali_alipay_public_key)){
        		throw BizException.withMessage("请输入支付宝公钥");
    		}
    		if(StringUtils.isEmpty(ali_notify_url)){
        		throw BizException.withMessage("请输入支付宝异步通知回调地址");
    		}
    		if(StringUtils.isEmpty(ali_return_url)){
        		throw BizException.withMessage("请输入支付宝支付完毕后的同步跳转地址");
    		}
    		Map<String, String> config = new HashMap<String, String>();
    		config.put("ali_app_id", ali_app_id);
    		config.put("ali_merchant_private_key", ali_merchant_private_key);
    		config.put("ali_alipay_public_key", ali_alipay_public_key);
    		config.put("ali_notify_url", ali_notify_url);
    		config.put("ali_return_url", ali_return_url);
    		settings = JSONObject.fromObject(config).toString();
    	}
    	else if(payPlat == PayPlatform.WX){
    		if(StringUtils.isEmpty(wx_app_id)){
        		throw BizException.withMessage("请输入微信支付应用ID");
    		}
    		if(StringUtils.isEmpty(wx_mch_id)){
        		throw BizException.withMessage("请输入微信支付商户号");
    		}
    		if(StringUtils.isEmpty(wx_api_key)){
        		throw BizException.withMessage("请输入微信支付API秘钥");
    		}
    		if(StringUtils.isEmpty(wx_notify_url)){
        		throw BizException.withMessage("请输入微信支付异步通知地址");
    		}
    		Map<String, String> config = new HashMap<String, String>();
    		config.put("wx_app_id", wx_app_id);
    		config.put("wx_app_secret", wx_app_secret);
    		config.put("wx_mch_id", wx_mch_id);
    		config.put("wx_api_key", wx_api_key);
    		config.put("wx_notify_url", wx_notify_url);
    		settings = JSONObject.fromObject(config).toString();
    	}
    	else if(payPlat == PayPlatform.CCB) {
    		if(StringUtils.isEmpty(ccb_merchantid)){
        		throw BizException.withMessage("请输入商户代码(MERCHANTID)");
    		}
    		if(StringUtils.isEmpty(ccb_postid)){
        		throw BizException.withMessage("请输入商户柜台代码(POSID)");
    		}
    		if(StringUtils.isEmpty(ccb_branchid)){
        		throw BizException.withMessage("请输入分行代码(BRANCHID)");
    		}
    		if(StringUtils.isEmpty(ccb_pub)){
        		throw BizException.withMessage("请输入商户柜台公钥后30位(PUB)");
    		}
    		Map<String, String> config = new HashMap<String, String>();
    		config.put("ccb_merchantid", ccb_merchantid);
    		config.put("ccb_postid", ccb_postid);
    		config.put("ccb_branchid", ccb_branchid);
    		config.put("ccb_pub", ccb_pub);
    		settings = JSONObject.fromObject(config).toString();
    	}
    	else if(payPlat == PayPlatform.ICBC) {
    		if(StringUtils.isEmpty(icbc_appid)){
    			throw BizException.withMessage("请输入APP_ID");
    		}
    		if(StringUtils.isEmpty(icbc_merchantid)){
    			throw BizException.withMessage("请输入商户编号(merchantid)");
    		}
    		if(StringUtils.isEmpty(icbc_mer_prtcl_no)){
    			throw BizException.withMessage("请输入协议编号(mer_prtcl_no)");
    		}
    		if(StringUtils.isEmpty(icbc_public_key)){
    			throw BizException.withMessage("请输入公钥(public_key)");
    		}
    		if(StringUtils.isEmpty(icbc_private_key)){
    			throw BizException.withMessage("请输入公钥(private_key)");
    		}
    		if(StringUtils.isEmpty(icbc_mer_url)){
    			throw BizException.withMessage("请输入异步通知地址(mer_url)");
    		}
    		Map<String, String> config = new HashMap<String, String>();
    		config.put("icbc_appid", icbc_appid);
    		config.put("icbc_merchantid", icbc_merchantid);
    		config.put("icbc_mer_prtcl_no", icbc_mer_prtcl_no);
    		config.put("icbc_public_key", icbc_public_key);
    		config.put("icbc_private_key", icbc_private_key);
    		config.put("icbc_mer_url", icbc_mer_url);
    		settings = JSONObject.fromObject(config).toString();
    	}
    	else {
    		throw BizException.withMessage("系统不支持您选择的支付平台");
		}
		bankConfig.setPayPlat(payPlat);
		bankConfig.setPaySettting(settings);
		bankConfig.setUpdateTime(new Date());
		bankConfig.setStatus(status);
		bankConfig.setUpdatorId(super.getLoginUserId(request));
		service.updateById(bankConfig);
		return true;
    }
}
