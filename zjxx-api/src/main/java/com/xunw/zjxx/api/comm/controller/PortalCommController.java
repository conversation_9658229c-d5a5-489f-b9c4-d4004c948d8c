package com.xunw.zjxx.api.comm.controller;

import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.FileHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@RestController
@RequestMapping("/portal")
public class PortalCommController {

    @Autowired
    private AttConfig attConfig;

    @RequestMapping("/common/uploadFile")
    @Operation(desc = "通用文件上传")
    public Object uploadFile(HttpServletRequest request,
                             @RequestParam("file") MultipartFile file) throws Exception {
        if (file == null) {
            throw BizException.withMessage("在请求中没有检测到文件");
        }
        String ext = FileHelper.getExtension(file.getOriginalFilename());
        String path = attConfig.getRootDir() + "/upload/files/" + new SimpleDateFormat("yyyyMMddHH").format(new Date());
        String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext;
        return FileHelper.storeFile(path, file.getInputStream(), newFileName);
    }
}
