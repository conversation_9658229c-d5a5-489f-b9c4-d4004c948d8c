package com.xunw.zjxx.api.sys.controller;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.dto.reqParams.NoticeReq;
import com.xunw.zjxx.module.sys.params.NoticeQueryParams;
import com.xunw.zjxx.module.sys.service.NoticeService;

@RestController
@RequestMapping("/htgl/sys/notice")
public class NoticeController extends BaseController {

    @Autowired
    private NoticeService noticeService;

    @RequestMapping("/list")
    @Operation(desc = "公告列表")
    public Object pageQuery(HttpServletRequest request,
                            NoticeQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return noticeService.pageQuery(params);
    }

    @RequestMapping("/add")
    @Operation(desc = "新增公告")
    public Object add(HttpServletRequest request, NoticeReq req) {
        noticeService.add(req, super.getLoginUserId(request), super.getCurrentHostOrgId(request));
        return true;
    }

    @RequestMapping("/edit")
    @Operation(desc = "修改公告")
    public Object edit(HttpServletRequest request, NoticeReq req) {
        noticeService.edit(req, super.getLoginUserId(request));
        return true;
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除公告")
    public Object deleteById(HttpServletRequest request,
                             @RequestParam(required = false) String id) {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("公告id不能为空");
        }
        noticeService.deleteById(id);
        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "通过id获取公告")
    public Object getById(HttpServletRequest request,
                          @RequestParam(required = false) String id) {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("公告id不能为空");
        }
        return noticeService.selectById(id);
    }
}
