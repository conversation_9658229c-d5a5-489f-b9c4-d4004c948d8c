package com.xunw.zjxx.api.biz.controller;

import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.LearningRecordQueryParams;
import com.xunw.zjxx.module.biz.params.MonitorQueryParams;
import com.xunw.zjxx.module.biz.service.StudentBmCourseService;
import com.xunw.zjxx.module.biz.service.XmService;

@RestController
@RequestMapping("/htgl/biz/studentLearningRecord")
public class StudentLearningRecoreController extends BaseController {

    @Autowired
    private XmService xmService;
    @Autowired
    private StudentBmCourseService studentBmCourseService;

    @RequestMapping("/list")
    @Operation(desc =  "学生学习记录列表")
    public Object list(HttpServletRequest request,
                       LearningRecordQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return xmService.getStudentLearningRecordList(params, super.getAccessToken(request));
    }

    @RequestMapping("/details")
    @Operation(desc =  "学生学习记录详情")
    public Object details(HttpServletRequest request,
                          @RequestParam(required = false) String coursewareId,
                          @RequestParam(required = false) String courseId,
                          @RequestParam(required = false) String studentId) throws Exception {
        if (StringUtils.isEmpty(courseId)) {
            throw BizException.withMessage("课程ID不能为空");
        }
        if (StringUtils.isEmpty(coursewareId)) {
            throw BizException.withMessage("课件id不能为空");
        }
        if (StringUtils.isEmpty(studentId)) {
            throw BizException.withMessage("学生id不能为空");
        }
        return xmService.details(courseId,coursewareId, studentId);
    }

    @RequestMapping("/getAllPhoto")
    @Operation(desc =  "查询所有抓拍照片")
    public Object getAllPhoto(HttpServletRequest request,
                              @RequestParam(required = false) String coursewareId,
                              @RequestParam(required = false) String studentId) throws Exception {
        if (StringUtils.isEmpty(coursewareId)) {
            throw BizException.withMessage("课件id不能为空");
        }
        if (StringUtils.isEmpty(studentId)) {
            throw BizException.withMessage("学生id不能为空");
        }
        return xmService.getAllPhoto(coursewareId, studentId);
    }

    @RequestMapping("/getPhoto")
    @Operation(desc =  "查询单个课时抓拍照片")
    public Object getPhoto(HttpServletRequest request,
                           @RequestParam(required = false) String coursewareId,
                           @RequestParam(required = false) String chapterId,
                           @RequestParam(required = false) String lessonId,
                           @RequestParam(required = false) String studentId) throws Exception {
        if (StringUtils.isEmpty(coursewareId)) {
            throw BizException.withMessage("课件id不能为空");
        }
        if (StringUtils.isEmpty(chapterId)) {
            throw BizException.withMessage("章节id不能为空");
        }
        if (StringUtils.isEmpty(lessonId)) {
            throw BizException.withMessage("课时id不能为空");
        }
        if (StringUtils.isEmpty(studentId)) {
            throw BizException.withMessage("学生id不能为空");
        }
        return xmService.getPhoto(coursewareId, chapterId, lessonId, studentId);
    }

    @RequestMapping("/monitor/list")
    @Operation(desc =  "学习人脸比对列表")
    public Object monitorList(HttpServletRequest request,
                              String courseId,
                              String keyword,
                              @RequestParam(defaultValue = "0-100") String xsdfw,
                              Integer isVerify,
                              @RequestParam(defaultValue = "1") Integer current,
                              @RequestParam(defaultValue = "10") Integer size) throws Exception {
        return studentBmCourseService.monitorList(courseId, keyword, xsdfw, isVerify, current, size, super.getCurrentHostOrgId(request));
    }

    @RequestMapping("/static/progress")
    @Operation(desc =  "学习进度统计")
    public Object progress(HttpServletRequest request,
                           MonitorQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return studentBmCourseService.progress(params, super.getAccessToken(request));
    }
}
