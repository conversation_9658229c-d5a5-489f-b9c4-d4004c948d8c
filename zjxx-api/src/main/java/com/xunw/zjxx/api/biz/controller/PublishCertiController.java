package com.xunw.zjxx.api.biz.controller;

import com.baomidou.mybatisplus.plugins.Page;
import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.entity.StudentCerti;
import com.xunw.zjxx.module.biz.params.ZypxCertiApproveQueryParams;
import com.xunw.zjxx.module.biz.service.CertiApproveService;
import com.xunw.zjxx.module.params.ZypxStudentCertiMgrParams;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/htgl/biz/publishCerti")
public class PublishCertiController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PublishCertiController.class);// 记录打印日志用的
    @Autowired
    private CertiApproveService certiApproveService;

    @RequestMapping("/list")
    @Operation(desc = "合格证书列表查询")
    public Object list(HttpServletRequest request, ZypxCertiApproveQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return certiApproveService.selectPassList(params);
    }

    /**
     * 生成学员证书，返回证书PDF下载地址
     */
    @RequestMapping("/buildCerti")
    @Operation(desc = "创建证书")
    public Object buildCerti(HttpServletRequest request, @RequestParam(required = false) String xmId,
                             @RequestParam(required = false) String studentId) throws Exception {
        if (StringUtils.isEmpty(xmId)) {
            throw BizException.withMessage("项目ID不能为空");
        }
        if (StringUtils.isEmpty(studentId)) {
            throw BizException.withMessage("学员ID不能为空");
        }
        StudentCerti studentCerti = certiApproveService.buildStudentCerti(xmId, studentId, getCurrentHostOrgPortalWebUrl(request));
        return studentCerti;
    }

    @RequestMapping("/exportCertiStudent")
    @Operation(desc = "导出已经发证的学员数据")
    public void exportCertiStudent(HttpServletRequest request, HttpServletResponse response, String xmId)
            throws Exception {
        response.setContentType("text/html;charset=utf-8");
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=student_certi.xls");
        OutputStream os = null;
        List<Map<String, Object>> data = null;
        try {
            ZypxStudentCertiMgrParams params = new ZypxStudentCertiMgrParams();
            params.setXmId(xmId);
            params.setCurrent(Constants.DEFAULT_PAGE_NUMBER);
            params.setSize(Integer.MAX_VALUE);
            Page page = certiApproveService.pageQuery(params);
            data = page.getRecords();
            os = response.getOutputStream();
            certiApproveService.exportPassedStudent(data, os);
            os.flush();
        } catch (Exception e) {
            LOGGER.error("导出已经发证的学员数据出现异常：", e);
        } finally {
            if (os != null) {
                IOUtils.closeQuietly(os);
            }
        }
    }
}
