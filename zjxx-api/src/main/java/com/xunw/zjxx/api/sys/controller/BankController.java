package com.xunw.zjxx.api.sys.controller;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.entity.Bank;
import com.xunw.zjxx.module.sys.params.BankQueryParams;
import com.xunw.zjxx.module.sys.service.BankService;

@RestController
@RequestMapping("/htgl/sys/bank")
public class BankController extends BaseController {

    @Autowired
    private BankService service;

    @RequestMapping("/list")
    @Operation(desc = "收款方列表查询")
    public Object list(HttpServletRequest request, BankQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return service.pageQuery(params);
    }

    @RequestMapping("/add")
    @Operation(desc = "新增")
    public Object add(
            HttpServletRequest request,
            @RequestParam(required = false)	String hostOrgId,
            @RequestParam(required = false)	String name,
            @RequestParam(required = false)	String isDefault,
            @RequestParam(required = false) Status status
    ) {
        if (StringUtils.isEmpty(hostOrgId)){
            throw BizException.withMessage("主办单位不能为空");
        }
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("收款方名称不能为空");
        }
        if (status == null) {
            throw BizException.withMessage("状态不能为空");
        }
        if (Constants.YES.equals(isDefault) && BaseUtil.isNotEmpty(service.getDefaultBank(hostOrgId))) {
            throw BizException.withMessage("每一个主办单位只能够配置一个默认的收款方");
        }
        Bank bank = new Bank();
        bank.setId(BaseUtil.generateId2());
        bank.setHostOrgId(hostOrgId);
        bank.setName(name);
        bank.setStatus(status);
        bank.setIsDefault(isDefault);
        service.insert(bank);
        return true;
    }


    @RequestMapping("/edit")
    @Operation(desc = "编辑")
    public Object edit(
            HttpServletRequest request,
            @RequestParam(required = false)	String hostOrgId,
            @RequestParam(required = false)	String id,
            @RequestParam(required = false)	String name,
            @RequestParam(required = false)	String isDefault,
            @RequestParam(required = false)	Status status
    ) throws Exception {
        if (StringUtils.isEmpty(hostOrgId)){
            throw BizException.withMessage("主办单位不能为空");
        }
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("收款方标识不能为空");
        }
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("收款方名称不能为空");
        }
        Bank bank = service.selectById(id);
        if (bank == null) {
            throw BizException.withMessage("收款方标识错误，系统中不存在");
        }
        Bank defaultBank = service.getDefaultBank(super.getCurrentHostOrgId(request));
        if (Constants.YES.equals(isDefault) && defaultBank!= null && !defaultBank.getId().equals(id)) {
            throw BizException.withMessage("每一个主办单位只能够配置一个默认的收款方");
        }
        bank.setHostOrgId(hostOrgId);
        bank.setName(name);
        bank.setIsDefault(isDefault);
        bank.setStatus(status);
        service.updateById(bank);
        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "详情")
    public Object getById(
            HttpServletRequest request,
            @RequestParam(required = false)	String id
    ) {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("收款放标识不能为空");
        }
        return service.selectById(id);
    }

    @RequestMapping("/select")
    @Operation(desc = "下拉选项")
    public Object select(HttpServletRequest request) throws Exception {
        EntityWrapper<Bank> wrapper = new EntityWrapper<>();
        wrapper.eq("host_org_id", getCurrentHostOrgId(request));
        wrapper.eq("status", Status.OK);
        return service.selectList(wrapper);
    }

    @RequestMapping("/getBankList")
    @Operation(desc = "获取收款方信息")
    public Object getBankList(HttpServletRequest request) throws Exception {
        return service.getBankList(super.getAccessToken(request));
    }
}
