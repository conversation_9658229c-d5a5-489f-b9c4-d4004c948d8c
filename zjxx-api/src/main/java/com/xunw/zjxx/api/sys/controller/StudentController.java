package com.xunw.zjxx.api.sys.controller;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.params.StudentUserQueryParams;
import com.xunw.zjxx.module.sys.service.StudentInfoService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

@RestController
@RequestMapping("/htgl/sys/studentUser")
public class StudentController extends BaseController {

    @Autowired
    private StudentInfoService studentInfoService;

    @RequestMapping("/list")
    @Operation(desc = "学员用户列表")
    public Object pageQuery(HttpServletRequest request,
                            StudentUserQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return studentInfoService.pageQuery(params);
    }

    @RequestMapping("/getById")
    @Operation(desc = "根据id获取学员用户")
    public Object getById(HttpServletRequest request,
                          @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        return studentInfoService.getById(id);
    }

    @RequestMapping("/enable")
    @Operation(desc = "学员用户启用/禁用")
    public Object enable(HttpServletRequest request,
                         @RequestParam(required = false) String id,
                         @RequestParam(required = false) Status status) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        if (status == null) {
            throw BizException.withMessage("状态不能为空");
        }
        studentInfoService.enable(id, status);
        return true;
    }

    @RequestMapping("/resetPassword")
    @Operation(desc = "学员用户重置密码")
    public Object resetPassword(HttpServletRequest request,
                                @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("id不能为空");
        }
        studentInfoService.resetPassword(id);
        return true;
    }

    @RequestMapping("/export")
    @Operation(desc = "学员用户导出")
    public void export(HttpServletRequest request,
                       HttpServletResponse response,
                       StudentUserQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=student.xls");
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            studentInfoService.export(params, os);
            os.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                IOUtils.closeQuietly(os);
            }
        }
    }
}
