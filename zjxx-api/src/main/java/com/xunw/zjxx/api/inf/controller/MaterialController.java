package com.xunw.zjxx.api.inf.controller;


import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.inf.entity.Material;
import com.xunw.zjxx.module.inf.params.MaterialQueryParams;
import com.xunw.zjxx.module.inf.service.MaterialService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;

/**
 * 素材管理
 */
@RestController
@RequestMapping("/htgl/inf/zypx/material")
public class MaterialController extends BaseController {

    @Autowired
    private MaterialService service;

    /**
     * 列表查询
     */
    @RequestMapping("/list")
    @Operation(desc = "素材列表")
    public Object list(HttpServletRequest request, MaterialQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return service.pageQuery(params);
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @Operation(desc = "删除素材")
    public Object deleteById(@RequestParam(required = false) String ids) throws Exception {
        if (StringUtils.isEmpty(ids)) {
            throw BizException.withMessage("请传入Id");
        }
        service.deleteBatchIds(Arrays.asList(ids.split(",")));
        return true;
    }

    /**
     * 修改
     */
    @RequestMapping("/edit")
    @Operation(desc = "修改")
    public Object edit(HttpServletRequest request, Material material) {
        if (StringUtils.isEmpty(material.getId())) {
            throw BizException.withMessage("材料ID不能为空");
        }
        Material m = service.selectById(material.getId());
        if (m == null) {
            throw BizException.withMessage("材料不存在");
        }
        if (BaseUtil.isEmpty(material.getName())) {
            throw BizException.withMessage("请输入名称");
        }
        if (BaseUtil.isEmpty(material.getType())) {
            throw BizException.withMessage("请选择类型");
        }
        if (BaseUtil.isEmpty(material.getUrl())) {
            throw BizException.withMessage("请输入地址");
        }
        if (BaseUtil.isEmpty(material.getCourseId())) {
            throw BizException.withMessage("请选择课程");
        }
        BeanUtils.copyProperties(material, m);
        material.setUpdatorId(super.getLoginUserId(request));
        material.setUpdateTime(new Date());
        service.updateById(material);
        return true;
    }

    /**
     * 详情
     */
    @RequestMapping("/getById")
    @Operation(desc = "详情")
    public Object getById(@RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择一条数据");
        }
        return service.selectById(id);
    }

    /**
     * 材料导入
     */
    @RequestMapping("/batchImport")
    @Operation(desc = "材料导入")
    public Object batchImport(HttpServletRequest request, @RequestParam(value = "file") MultipartFile[] files, @RequestParam(required = false) String courseId) throws Exception {
        if (files.length == 0) {
            throw new BizException("请上传文件");
        }
        if (StringUtils.isEmpty(courseId)) {
            throw new BizException("请选择课程");
        }
        service.batchImport(files, courseId, super.getCurrentHostOrgId(request), super.getLoginUserId(request));
        return true;
    }
}
