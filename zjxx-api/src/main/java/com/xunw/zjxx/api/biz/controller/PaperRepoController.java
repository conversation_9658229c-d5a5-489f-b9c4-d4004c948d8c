package com.xunw.zjxx.api.biz.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.xunw.zjxx.module.biz.service.ExamPaperService;
import com.xunw.zjxx.module.enums.PaperCategory;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.mapper.CourseMapper;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.sys.entity.User;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.PaperRepoQueryParams;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/htgl/biz/paperRepo")
public class PaperRepoController extends BaseController {

    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private ExamPaperService examPaperService;
    @Autowired
    private CourseMapper courseMapper;

    @RequestMapping("/list")
    @Operation(desc = "卷库列表", loginRequired = false)
    public Object list(HttpServletRequest request,
                       HttpServletResponse response,
                       PaperRepoQueryParams params) throws Exception {
        int isSimulate = 0;
        if (PaperCategory.SIMULATE.name().equals(params.getCategory())) {
            params.setCategory(PaperCategory.EXAMINE.name());
            isSimulate = 1;
        }
        Object resp = examServiceApi.paperList(params.getName(), params.getCategory(),
                super.getCurrentHostOrgId(request), params.getStatus(), params.getCourseId(),
                null, isSimulate, params.getCurrent(), params.getSize()).getData();
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(resp));
        List<JSONObject> records = jsonObject.getJSONArray("records").stream().map(x -> {
            JSONObject map = JSON.parseObject(JSON.toJSONString(x));
            String courseId = map.getString("courseId");
            Course course = courseMapper.selectById(courseId);
            if(course!=null){
                map.put("courseName", course.getName());
            }
            return map;
        }).collect(Collectors.toList());
        jsonObject.put("records", records);
        JSONObject.parseObject(JSONObject.toJSONString(resp));
        return jsonObject;
    }

    @RequestMapping("/getPaperDetailById")
    @Operation(desc = "根据ID查询卷库", loginRequired = false)
    public Object getPaperDetailById(HttpServletRequest request,
                          HttpServletResponse response,
                          String id) throws Exception {
        Object resp = examServiceApi.paperGetById(id).getData();
        return JSONObject.parseObject(JSONObject.toJSONString(resp));
    }

    @RequestMapping("/editPaper")
    @Operation(desc = "修改卷库", loginRequired = false)
    public Object editPaper(HttpServletRequest request,
                       HttpServletResponse response,
                       @RequestBody JSONObject json) throws Exception {
        if(StringUtils.isEmpty(json.getString("id"))){
            throw BizException.withMessage("请选择卷库");
        }
        if(StringUtils.isEmpty(json.getString("name"))){
            throw BizException.withMessage("请填写卷库名字");
        }
        if( StringUtils.isEmpty(json.getString("status"))){
            throw BizException.withMessage("请填写卷库状态");
        }
        examPaperService.editPaperRepo(json);
        return true;
    }

    @RequestMapping("/deletePaper")
    @Operation(desc = "删除卷库", loginRequired = false)
    public Object deletePaper(HttpServletRequest request,
                         HttpServletResponse response,
                         String id) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择卷库");
        }
        examPaperService.deletePaperRepo(id);
        return true;
    }

    @RequestMapping("/configPaper")
    @Operation(desc = "试卷库配置组卷")
    public Object configPaper(HttpServletRequest request,
                              HttpServletResponse response,
                              @RequestBody JSONObject json) throws Exception {
        examPaperService.configPaper(json);
        return true;
    }

    @RequestMapping("/configByChooseQues")
    @Operation(desc = "智能组卷保存接口")
    public Object configByChooseQues(HttpServletRequest request,
                                     @RequestBody JSONObject json) throws Exception {
       examPaperService.autoBuildPaper(json, super.getCurrentHostOrgId(request), super.getAccessToken(request));
       return true;
    }

    @RequestMapping("/add")
    @Operation(desc = "新增试卷")
    public Object add(HttpServletRequest request,
                      @RequestBody JSONObject json) throws Exception {
        examPaperService.addPaper(json, super.getCurrentHostOrgId(request));
        return true;
    }
}
