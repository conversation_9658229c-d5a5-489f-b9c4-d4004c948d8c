package com.xunw.zjxx.api.portal.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.comm.entity.CommOrder;
import com.xunw.zjxx.module.comm.service.CommOrderService;
import com.xunw.zjxx.module.enums.OrderStatus;
import com.xunw.zjxx.module.enums.PayMethod;
import com.xunw.zjxx.module.enums.PayPlatform;
import com.xunw.zjxx.module.sys.entity.Bank;
import com.xunw.zjxx.module.sys.service.BankConfigService;
import com.xunw.zjxx.module.sys.service.BankService;
@RestController
@RequestMapping("/portal")
public class PortalOrderController extends BaseController {

	@Autowired
	private CommOrderService orderService;
	@Autowired
	private BankService bankService;
	@Autowired
	private BankConfigService bankConfigService;
	
	 /**
     * 获取支付平台
     * 
     */
    @RequestMapping("/getPayPlatform")
    @ResponseBody
    @Operation(desc = " 获取支付平台")
    public Object getPayPlatform(HttpServletRequest request) {
    	Bank bank = bankService.getDefaultBank(getCurrentHostOrgId(request));
    	if (bank == null) {
			throw BizException.withMessage("尚未配置可用的收款方");
		}
    	List<Map<String, Object>> list = bankConfigService.getEnabledPlatform(bank.getId());
    	return list;
    }

	@RequestMapping("/orderList")
	@Operation(desc = "订单列表")
	public Object orderList(HttpServletRequest request, String status) throws Exception {
		LoginUser loginUser = this.getLoginUser(request);
		return orderService.orderList(loginUser, status);
	}

	@RequestMapping("/orderDetail")
	@Operation(desc = "订单详情")
	public Object orderDetail(HttpServletRequest request, String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("订单id不能为空");
		}
		return orderService.orderDetail(id);
	}

	@RequestMapping("/orderPay")
	@Operation(desc = "订单支付", loginRequired = false)
	public Object orderList(HttpServletRequest request, String id, PayMethod payType, String openid)
			throws Exception {
		if (BaseUtil.isEmpty(id)) {
			throw BizException.withMessage("请传入订单编号");
		}
		if (payType == null) {
			throw BizException.withMessage("请选择订单的支付方式");
		}
		String payInfo = orderService.goPay(id, payType, openid);
		return payInfo;
	}

	@RequestMapping("/isPayed")
	@Operation(desc = "查询是否已支付")
	public Object isPayed(HttpServletRequest request, String id) throws Exception {
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("订单id不能为空");
		}
		CommOrder commOrder = orderService.selectById(id);
		return commOrder.getStatus() == OrderStatus.PAID;
	}
}
