package com.xunw.zjxx.api.biz.controller;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.ArchiveQueryParams;
import com.xunw.zjxx.module.biz.service.ArchivesService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/biz/zypx/studentfiles")
public class ArchiveController extends BaseController {

    @Autowired
    private ArchivesService archivesService;

    @RequestMapping("/list")
    @Operation(desc = "档案列表")
    public Object pageQuery(HttpServletRequest request,
                            ArchiveQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return archivesService.pageQuery(params);
    }


    @RequestMapping("/getArchives")
    @Operation(desc = "档案详情")
    public Object getArchives(HttpServletRequest request,
                              @RequestParam(required = false) String studentId) {
        if (StringUtils.isEmpty(studentId)) {
            throw BizException.withMessage("学员id不能为空");
        }
        return archivesService.getArchives(studentId, super.getCurrentHostOrgId(request));
    }
}
