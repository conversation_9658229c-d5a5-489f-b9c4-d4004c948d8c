package com.xunw.zjxx.api.biz.controller;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.module.biz.entity.Xm;
import com.xunw.zjxx.module.biz.service.XmService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.FileHelper;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.entity.CertiTpl;
import com.xunw.zjxx.module.biz.params.ZypxCertiApproveQueryParams;
import com.xunw.zjxx.module.biz.service.CertiTplService;

@RestController
@RequestMapping("/htgl/biz/certiTpl")
public class CertiController extends BaseController {

    @Autowired
    private AttConfig attConfig;

    @Autowired
    private CertiTplService service;

    @Autowired
    private XmService xmService;

    @RequestMapping("/list")
    @Operation(desc = "证书模板列表")
    public Object cx(HttpServletRequest request, ZypxCertiApproveQueryParams params) throws Exception {
        params.setHostOrgId(getCurrentHostOrgId(request));
        return service.list(params);
    }

    @RequestMapping("/add")
    @Operation(desc = "添加证书模板")
    public Object add(HttpServletRequest request, @RequestParam(required = false) String name,
                      @RequestParam(required = false) String demo, @RequestParam(required = false) MultipartFile templateFile) throws Exception {
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("证书模板名称不能为空");
        }
        if (StringUtils.isEmpty(demo)) {
            throw BizException.withMessage("证书模板预览图不能为空");
        }
        if (templateFile == null) {
            throw BizException.withMessage("证书PDF模板不能为空");
        }
        CertiTpl certiTpl = new CertiTpl();
        certiTpl.setId(BaseUtil.generateId2());
        certiTpl.setName(name);
        certiTpl.setDemo(demo);
        String ext = FileHelper.getExtension(templateFile.getOriginalFilename());
        String path = attConfig.getRootDir()+"/resource/template/"+new SimpleDateFormat("yyyyMMddHH").format(new Date());
        String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()+ext;
        String templatePath = FileHelper.storeFile(path, templateFile.getInputStream(), newFileName);
        certiTpl.setTemplatePath(templatePath);
        certiTpl.setCreateTime(new Date());
        certiTpl.setCreatorId(super.getLoginUserId(request));
        certiTpl.setHostOrgId(super.getCurrentHostOrgId(request));

        service.insert(certiTpl);

        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "证书模板详情")
    public Object getById(HttpServletRequest request,@RequestParam(required = false) String id) throws Exception {
        return service.selectById(id);
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除证书模板")
    public Object deleteById(HttpServletRequest request,@RequestParam(required = false) String id) throws Exception {
        EntityWrapper<Xm> wrapper = new EntityWrapper<>();
        wrapper.eq("certi_tpl_id", id);
        Integer count = xmService.selectCount(wrapper);
        if (count > 0) {
            throw BizException.withMessage("该证书模板有培训项目正在使用，不能删除");
        }
        service.deleteById(id);
        return true;
    }

    @RequestMapping("/select")
    @Operation(desc = "证书模板选择")
    public Object select(HttpServletRequest request) throws Exception {
        EntityWrapper<CertiTpl> wrapper = new EntityWrapper();
        wrapper.eq("host_org_id", this.getCurrentHostOrgId(request));
        wrapper.orderBy("create_time", false);
        return service.selectList(wrapper);
    }

    @RequestMapping("/edit")
    @Operation(desc = "编辑证书模板")
    public Object edit(HttpServletRequest request,@RequestParam(required = false) String id, @RequestParam(required = false) String name,
                       @RequestParam(required = false) String demo, @RequestParam(required = false) MultipartFile templateFile) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择一个证书模板");
        }
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("证书模板名称不能为空");
        }
        if (StringUtils.isEmpty(demo)) {
            throw BizException.withMessage("证书模板预览图不能为空");
        }
        CertiTpl certiTpl = service.selectById(id);
        if (certiTpl == null) {
            throw BizException.withMessage("证书模板在系统中不存在");
        }
        certiTpl.setName(name);
        certiTpl.setDemo(demo);
        if (templateFile != null) {
            String ext = FileHelper.getExtension(templateFile.getOriginalFilename());
            String path = attConfig.getRootDir()+"/resource/template/"+new SimpleDateFormat("yyyyMMddHH").format(new Date());
            String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext;
            String templatePath = FileHelper.storeFile(path, templateFile.getInputStream(), newFileName);
            certiTpl.setTemplatePath(templatePath);
        }
        service.updateById(certiTpl);
        return true;
    }


}
