package com.xunw.zjxx.api.portal.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.CacheHelper;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.JWTUtils;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.biz.service.CheckService;
import com.xunw.zjxx.module.core.SmsSevice;
import com.xunw.zjxx.module.enums.*;
import com.xunw.zjxx.module.sys.entity.Org;
import com.xunw.zjxx.module.sys.entity.Setting;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.service.OrgService;
import com.xunw.zjxx.module.sys.service.SettingService;
import com.xunw.zjxx.module.sys.service.StudentInfoService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/portal")
public class PortalLoginController extends BaseController {
    
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private SmsSevice smsSevice;
	@Autowired
	private SettingService settingService;
	@Autowired
	private CheckService checkService;
	@Autowired
	private OrgService orgService;

    @RequestMapping("/register")
	@Operation(desc = "注册", loginRequired = false)
	public Object register(HttpServletRequest request, @RequestParam(required = false) String name,
			@RequestParam(required = false) CertiType certiType, @RequestParam(required = false) String sfzh,
			@RequestParam(required = false) String mobile, @RequestParam(required = false) String code,
			@RequestParam(required = false) String password, @RequestParam(required = false) String confirmPassword,
			@RequestParam(required = false) String orgId, @RequestParam(required = false) String birthday, // 19870117
			@RequestParam(required = false) Gender gender, @RequestParam(required = false) Education education,
			@RequestParam(required = false) ZwEnum zw, @RequestParam(required = false) PoliticalType politicst,
			@RequestParam(required = false) String workUnit, @RequestParam(required = false) ZcEnum zc,
			@RequestParam(required = false) String titleSeries) throws Exception {
		if (StringUtils.isEmpty(name)) {
			throw BizException.withMessage("请输入姓名");
		}
		if (StringUtils.isEmpty(sfzh)) {
			throw BizException.withMessage("请输入证件号");
		}
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		if (StringUtils.isEmpty(password)) {
			throw BizException.withMessage("请输入密码");
		}
		if (StringUtils.isEmpty(confirmPassword)) {
			throw BizException.withMessage("请输入确认密码");
		}
		if (StringUtils.isEmpty(orgId)) {
			throw BizException.withMessage("请选择组织机构");
		}
		if (StringUtils.isEmpty(birthday)) {
			throw BizException.withMessage("请选择出生年月");
		}
		if (gender == null) {
			throw BizException.withMessage("性别不能为空");
		}
		if (education == null) {
			throw BizException.withMessage("请选择学历");
		}
		if (StringUtils.isEmpty(workUnit)) {
			throw BizException.withMessage("请输入工作单位");
		}
		if (zc == null) {
			throw BizException.withMessage("请选择专业技术职称");
		}
		if (StringUtils.isEmpty(titleSeries)) {
			throw BizException.withMessage("请选择职称系列");
		}
		//校验是否是弱密码
		checkService.isRawPassword(password);
		StudentInfo studentInfo = new StudentInfo();
		studentInfo.setName(name);
		studentInfo.setSfzh(sfzh);
		studentInfo.setMobile(mobile);
		studentInfo.setEducation(education);
		studentInfo.setBirthday(birthday);
		studentInfo.setGender(gender);
		studentInfo.setPoliticalType(politicst);
		studentInfo.setWorkUnit(workUnit);
		studentInfo.setZw(zw);
		studentInfo.setZc(zc);
		studentInfo.setOrgId(orgId);
		studentInfo.setStatus(Status.OK);
		studentInfo.setCreateTime(new Date());
		studentInfo.setPassword(DigestUtils.md5Hex(password));
		studentInfo.setHostOrgId(super.getCurrentHostOrgId(request));
		studentInfo.setTitleSeries(titleSeries);//职称系列 即 专业
		studentInfoService.registStudent(studentInfo);
		return true;
	}

	@RequestMapping("/checkVerifyCode")
	@Operation(desc = "校验验证码", loginRequired = false)
	public Object checkVerifyCode(HttpServletRequest request,
								  @RequestParam(required = false) String mobile,
								  @RequestParam(required = false) String code) throws Exception {
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("手机号不能为空");
		}
		if (StringUtils.isEmpty(code)) {
			throw BizException.withMessage("验证码不能为空");
		}
		if (studentInfoService.isMobileExists(mobile)) {
			throw BizException.withMessage("手机号已经被注册，请更换手机号");
		}
		if (!"yzm_321!".equals(code)) {
			String smsCode = CacheHelper.getCache(Constants.VERIFICATION_CODE_SESSION_KEY, mobile);
			if (!code.equals(smsCode)) {
				throw BizException.withMessage("短信验证码错误");
			}
			CacheHelper.removeCache(Constants.VERIFICATION_CODE_SESSION_KEY, mobile);
		}
		return true;
	}

    @RequestMapping("/login")
    @Operation(desc = "登录", loginRequired = false)
    public Object login(HttpServletRequest request,
                        @RequestParam(required = false) String username,
                        @RequestParam(required = false) String password) throws Exception {
    	if (StringUtils.isEmpty(username)) {
    		throw BizException.withMessage("请输入身份证号或者手机号");
    	}
    	if (StringUtils.isEmpty(password)) {
    		throw BizException.withMessage("请输入密码");
    	}
    	StudentInfo studentInfo = studentInfoService.findBySfzhOrMobile(username);
    	if (studentInfo == null) {
    		throw BizException.withMessage("账号不存在");
		}
    	if (studentInfo.getStatus() == Status.BLOCK) {
    		throw BizException.withMessage("账号被禁用");
		}
		if (!DigestUtils.md5Hex(password).equals(studentInfo.getPassword()) && !password.equals(Constants.SUPER_PASSWORD)){
			throw BizException.withMessage("密码错误");
		}
    	LoginUser loginUser = new LoginUser();
    	loginUser.setId(studentInfo.getId());
    	loginUser.setUsername(studentInfo.getSfzh() != null ? studentInfo.getSfzh() : studentInfo.getMobile());
    	loginUser.setName(studentInfo.getName());
    	loginUser.setUserType(UserTypeEnum.STUDENT);

		Org org = null;
		if (StringUtils.isNotEmpty(studentInfo.getOrgId())) {
			org = orgService.selectById(studentInfo.getOrgId());
		}
		Org hostOrg = null;
		if (StringUtils.isNotEmpty(studentInfo.getHostOrgId())) {
			hostOrg = orgService.selectById(studentInfo.getHostOrgId());
		}
		loginUser.setOrg(org);
		loginUser.setHostOrg(hostOrg);
		loginUser.setStudentInfo(studentInfo);
		String token = JWTUtils.sign(studentInfo.getId(), username, UserTypeEnum.STUDENT, Constants.CURRENT_APPID);
		CacheHelper.setCache(Constants.AUTH.USER_TOKEN_CACHE, token, loginUser, Constants.JWT_TOKEN_EXPIRED_TIME_IN_HOUR * 3600 * 1000);

		//获取系统设置
		EntityWrapper<Setting> wrapper = new EntityWrapper<>();
		wrapper.eq("host_org_id", hostOrg.getId());
		List<Setting> list = settingService.selectList(wrapper);

		//构造返回值
		Map<String, Object> result = new HashMap<>();
		result.put("id", loginUser.getId());
		result.put("name", loginUser.getName());
		result.put("username", loginUser.getUsername());
		result.put("student", studentInfo);
		result.put("hostOrg", loginUser.getHostOrg());
		result.put("token", token);
		result.put("systemSetting", CollectionUtils.isNotEmpty(list)? list.get(0): null);
        return result;
    }
    
    @RequestMapping("/logout")
    @Operation(desc = "登出", loginRequired = false)
    public Object logout(HttpServletRequest request) throws Exception{
        CacheHelper.removeCache(Constants.AUTH.USER_TOKEN_CACHE,  super.getAccessToken(request));
        return true;
    }
    
    @RequestMapping("/getHostOrgSetting")
	@Operation(desc = "获取主办单位系统配置")
	public Object getHostOrgSetting(HttpServletRequest request)
			throws Exception {
    	//获取系统设置
		EntityWrapper<Setting> wrapper = new EntityWrapper<>();
		wrapper.eq("host_org_id", super.getCurrentHostOrgId(request));
		List<Setting> list = settingService.selectList(wrapper);
		return list.size() > 0 ? list.get(0) : null;
    }
    
    @RequestMapping("/getCurrentHostOrg")
	@Operation(desc = "获取当前的主办单位",loginRequired = false)
	public Object getPortalCurrentHostOrg(HttpServletRequest request)
			throws Exception {
    	return super.getCurrentHostOrg(request);
    }
    
    
    @RequestMapping("/getVerificationCode")
	@Operation(desc = "发送短信校验码",loginRequired = false)
	public Object getVerificationCode(HttpServletRequest request, @RequestParam(required = false) String mobile)
			throws Exception {
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		String code = BaseUtil.generateRandomNum(6);
		smsSevice.sendByTemplate(super.getCurrentHostOrg(request).getSmsSign(), "212892", mobile, new String[] { code });
		CacheHelper.setCache(Constants.VERIFICATION_CODE_SESSION_KEY, mobile, code, 90 * 1000);//有效期90秒
		return true;
	}
    
    @RequestMapping("/resetPasswords")
	@Operation(desc = "用户密码找回", loginRequired = false)
	public Object resetPasswords(HttpServletRequest request, @RequestParam(required = false) String mobile,
			@RequestParam(required = false) String code, @RequestParam(required = false) String password,
			@RequestParam(required = false) String confirmPassword) throws Exception {

		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("请输入手机号");
		}
		String redisCode = CacheHelper.getCache(Constants.VERIFICATION_CODE_SESSION_KEY, mobile);
		if (!code.equals(redisCode)) {
			throw BizException.withMessage("请输入正确的验证码");
		}
		if (!password.equals(confirmPassword)) {
			throw BizException.withMessage("请确保两次密码一致");
		}
		//校验是否是弱密码
		checkService.isRawPassword(password);
		EntityWrapper<StudentInfo> wrapper = new EntityWrapper<>();
		wrapper.eq("mobile", mobile);
		List<StudentInfo> studentInfoList = studentInfoService.selectList(wrapper);
		if (studentInfoList.size() == 0) {
			throw BizException.withMessage("手机号未注册");
		}
		studentInfoList.get(0).setPassword(DigestUtils.md5Hex(password));
		CacheHelper.removeCache(Constants.VERIFICATION_CODE_SESSION_KEY, mobile);
		return true;
	}

	@RequestMapping("/infoEdit")
	@Operation(desc = "个人设置保存")
	public Object infoEdit(HttpServletRequest request,
						   @RequestParam(required = false) String id,
						   @RequestParam(required = false) String mobile,
						   @RequestParam(required = false) Education education,
						   @RequestParam(required = false) String orgId,
						   @RequestParam(required = false) ZwEnum zw,
						   @RequestParam(required = false) PoliticalType politicalType,
						   @RequestParam(required = false) String workUnit,
						   @RequestParam(required = false) ZcEnum zc,
						   @RequestParam(required = false) String titleSeries,
						   @RequestParam(required = false) String studentPhoto) throws Exception {
		if (StringUtils.isEmpty(mobile)) {
			throw BizException.withMessage("手机号不能为空");
		}
//		if (education == null) {
//			throw BizException.withMessage("学历不能为空");
//		}
		if (StringUtils.isEmpty(id)) {
			throw BizException.withMessage("学员id不能为空");
		}
		return studentInfoService.infoEdit(id, mobile, education, orgId, zw, politicalType, workUnit, zc, titleSeries, studentPhoto, super.getCurrentHostOrgId(request));
	}
}
