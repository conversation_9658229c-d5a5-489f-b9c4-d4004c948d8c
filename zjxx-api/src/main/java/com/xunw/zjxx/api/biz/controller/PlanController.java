package com.xunw.zjxx.api.biz.controller;

import java.util.Date;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import com.xunw.zjxx.module.biz.service.CheckService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.entity.StudentCerti;
import com.xunw.zjxx.module.biz.entity.Xm;
import com.xunw.zjxx.module.biz.params.XmQueryParams;
import com.xunw.zjxx.module.biz.service.XmCourseScopeService;
import com.xunw.zjxx.module.biz.service.XmService;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.enums.StudyWay;

@RestController
@RequestMapping("/htgl/biz")
public class PlanController extends BaseController {

    @Autowired
    private XmService xmService;
    @Autowired
    private XmCourseScopeService xmCourseScopeService;
    @Autowired
    private CheckService checkService;

    @RequestMapping("/plan/list")
    @Operation(desc = "学习计划列表查询")
    public Object pageQuery(HttpServletRequest request,
                            XmQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return xmService.pageQuery(params);
    }

    @RequestMapping("/planDetail/add")
    @Operation(desc = "学习计划基本信息新增")
    public Object add(HttpServletRequest request,
                      @RequestParam(required = false) String years,
                      @RequestParam(required = false) String name,
                      @RequestParam(required = false) Integer hours,
                      @RequestParam(required = false) StudyWay studyWay,
                      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                      @RequestParam(required = false) Integer isOpenVerify,
                      @RequestParam(required = false) Integer verifyThreshold,
                      @RequestParam(required = false) Integer isOpenCamera,
                      @RequestParam(required = false) Integer photoCatchInterval,
                      @RequestParam(required = false) String logo,
                      @RequestParam(required = false) String certiTplId,
                      @RequestParam(required = false) String certiXmName,
                      @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date certiDate,
                      @RequestParam(required = false) String certiNoPrefix,
                      @RequestParam(required = false) String certiStartNo) throws Exception {
        if (StringUtils.isEmpty(name)) {
            throw BizException.withMessage("学习计划名称不能为空");
        }
        if (StringUtils.isEmpty(years)) {
            throw BizException.withMessage("年度不能为空");
        }
        if (startTime == null) {
            throw BizException.withMessage("学习计划开始时间不能为空");
        }
        if (endTime == null) {
            throw BizException.withMessage("学习计划结束时间不能为空");
        }
        if (startTime.after(endTime)) {
            throw BizException.withMessage("结束时间不能早于开始时间");
        }
        Xm xm = new Xm();
        xm.setId(BaseUtil.generateId());
        xm.setName(name);
        xm.setYears(years);
        xm.setStudyWay(studyWay);
        xm.setHours(hours);
        xm.setStartTime(startTime);
        xm.setEndTime(endTime);
        xm.setIsOpenVerify(isOpenVerify);
        xm.setVerifyThreshold(verifyThreshold);
        xm.setIsOpenCamera(isOpenCamera);
        xm.setPhotoCatchInterval(photoCatchInterval);
        xm.setLogo(logo);
        xm.setStatus(Status.OK);
        xm.setCertiTplId(certiTplId);
        xm.setCertiXmName(certiXmName);
        xm.setCertiDate(certiDate);
        xm.setCertiNoPrefix(certiNoPrefix);
        xm.setCertiStartNo(certiStartNo);
        xm.setCreateTime(new Date());
        xm.setCreatorId(super.getLoginUserId(request));
        xm.setHostOrgId(super.getCurrentHostOrgId(request));
        xmService.insert(xm);
        return xm.getId();
    }

    @RequestMapping("/planDetail/edit")
    @Operation(desc = "学习计划基本信息修改")
    public Object edit(HttpServletRequest request,
                       @RequestParam(required = false) String id,
                       @RequestParam(required = false) String years,
                       @RequestParam(required = false) String name,
                       @RequestParam(required = false) Integer hours,
                       @RequestParam(required = false) StudyWay studyWay,
                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                       @RequestParam(required = false) Integer isOpenVerify,
                       @RequestParam(required = false) Integer verifyThreshold,
                       @RequestParam(required = false) Integer isOpenCamera,
                       @RequestParam(required = false) Integer photoCatchInterval,
                       @RequestParam(required = false) String logo,
                       @RequestParam(required = false) String certiTplId,
                       @RequestParam(required = false) String certiXmName,
                       @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date certiDate,
                       @RequestParam(required = false) String certiNoPrefix,
                       @RequestParam(required = false) String certiStartNo) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("学习计划id不能为空");
        }
        if (startTime != null && endTime != null) {
            if (startTime.after(endTime)) {
                throw BizException.withMessage("结束时间不能早于开始时间");
            }
        }
        Xm xm = xmService.selectById(id);
        if (xm == null) {
            throw BizException.withMessage("学习计划不存在");
        }
        if (startTime != null && startTime.after(xm.getEndTime())) {
            throw BizException.withMessage("开始时间不能晚于结束时间");
        }
        if (endTime != null && xm.getStartTime().after(endTime)) {
            throw BizException.withMessage("结束时间不能早于开始时间");
        }
        xm.setName(name);
        xm.setYears(years);
        xm.setStudyWay(studyWay);
        xm.setHours(hours);
        xm.setStartTime(startTime);
        xm.setEndTime(endTime);
        xm.setIsOpenVerify(isOpenVerify);
        xm.setVerifyThreshold(verifyThreshold);
        xm.setIsOpenCamera(isOpenCamera);
        xm.setPhotoCatchInterval(photoCatchInterval);
        xm.setLogo(logo);
        xm.setCertiTplId(certiTplId);
        xm.setCertiXmName(certiXmName);
        xm.setCertiDate(certiDate);
        xm.setCertiNoPrefix(certiNoPrefix);
        xm.setCertiStartNo(certiStartNo);
        xm.setUpdateTime(new Date());
        xm.setUpdatorId(super.getLoginUserId(request));
        xmService.updateById(xm);
        return true;
    }

    @RequestMapping("/plan/deleteById")
    @Operation(desc = "学习计划删除")
    public Object deleteById(HttpServletRequest request,
                             @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("学习计划id不能为空");
        }
        //校验是否有学员申请发证
        checkService.isDeleteXm(id);
        xmService.deleteById(id);
        return true;
    }

    @RequestMapping("/planDetail/getById")
    @Operation(desc = "学习计划详情")
    public Object get(HttpServletRequest request,
                      @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("学习计划id不能为空");
        }
        return xmService.selectById(id);
    }

    @RequestMapping("/plan/editStatus")
    @Operation(desc = "修改学习计划状态")
    public Object editStatus(HttpServletRequest request,
                             @RequestParam(required = false) String id,
                             @RequestParam(required = false) Status status) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("学习计划id不能为空");
        }
        Xm xm = xmService.selectById(id);
        if (xm == null) {
            throw BizException.withMessage("学习计划不存在");
        }
        xm.setStatus(status);
        xm.setUpdateTime(new Date());
        xm.setUpdatorId(super.getLoginUserId(request));
        xmService.updateById(xm);
        return true;
    }

    @RequestMapping("/plan/getCourseSetting")
    @Operation(desc = "获取学习计划课程设置")
    public Object getCourseSetting(HttpServletRequest request,
                                   @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("学习计划id不能为空");
        }
        Xm xm = xmService.selectById(id);
        if (xm == null) {
            throw BizException.withMessage("学习计划不存在");
        }
        return xmCourseScopeService.getCourseSetting(id);
    }

    @RequestMapping("/plan/tree")
    @Operation(desc = "获取学习计划树")
    public Object tree(HttpServletRequest request) throws Exception {
        return xmService.getPlanTree(super.getCurrentHostOrgId(request));
    }
    
    @RequestMapping("/plan/years/select")
    @Operation(desc = "获取学习计划年份")
    public Object yearsSelect(HttpServletRequest request) throws Exception {
    	  EntityWrapper<Xm> wrapper = new EntityWrapper<Xm>();
          wrapper.eq("host_org_id", super.getCurrentHostOrgId(request));
          wrapper.orderBy("years", true);
          return xmService.selectList(wrapper).stream().map(Xm::getYears).distinct().collect(Collectors.toList());
    }
}
