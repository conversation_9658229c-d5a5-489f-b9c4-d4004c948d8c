package com.xunw.zjxx.api.biz.controller;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.params.StudentBmCourseQueryParams;
import com.xunw.zjxx.module.biz.service.ScoreService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 成绩管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/htgl/biz/zypx/")
public class ScoreController extends BaseController {
	
	@Autowired
	private ScoreService service;

    @RequestMapping("learningScore/list")
    @Operation(desc = "单科成绩管理查询列表")
    public Object learningScoreList(HttpServletRequest request,
                                    StudentBmCourseQueryParams params) throws Exception  {
    	params.setHostOrgId(super.getCurrentHostOrgId(request));
        return service.learningScoreList(params);
    }

    @RequestMapping("learningScore/getDetails")
    @Operation(desc = "单科成绩管理详情")
    public Object getDetails(HttpServletRequest request,
    						@RequestParam(required = false) String courseId,
    						@RequestParam(required = false) String studentId)  {
    	if (StringUtils.isEmpty(courseId)) {
			throw BizException.withMessage("课程ID不能为空");
		}
    	if (StringUtils.isEmpty(studentId)) {
    		throw BizException.withMessage("学员ID不能为空");
    	}
    	return service.learningScoreDetails(studentId, courseId, super.getCurrentHostOrgId(request), super.getAccessToken(request));
    }

    @RequestMapping("trainingScore/list")
    @Operation(desc = "综合成绩管理查询列表")
    public Object trainingScoreList(HttpServletRequest request,
                                    StudentBmCourseQueryParams params)  {
    	params.setHostOrgId(super.getCurrentHostOrgId(request));
        return service.trainingScoreList(params);
    }

    @RequestMapping("trainingScore/getDetails")
    @Operation(desc = "综合成绩管理详情")
    public Object trainingScoreGetDetails(HttpServletRequest request,
                                    String studentId, String xmId)  {
    	if (StringUtils.isEmpty(studentId)) {
			throw BizException.withMessage("学员ID不能为空");
		}
    	if (StringUtils.isEmpty(xmId)) {
    		throw BizException.withMessage("学习计划ID不能为空");
    	}
        return service.trainingScoreDetails(studentId, xmId);
    }
}
