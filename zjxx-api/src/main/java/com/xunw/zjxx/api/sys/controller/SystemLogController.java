package com.xunw.zjxx.api.sys.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.sys.entity.SystemLog;
import com.xunw.zjxx.module.sys.params.SystemLogQueryParams;
import com.xunw.zjxx.module.sys.service.SystemLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/htgl/sys/log")
public class SystemLogController extends BaseController {

    @Autowired
    private SystemLogService systemLogService;

    @RequestMapping("/list")
    @Operation(desc = "日志列表")
    public Object pageQuery(HttpServletRequest request,
                            SystemLogQueryParams params) throws Exception {
        EntityWrapper<SystemLog> wrapper = new EntityWrapper<>();
        if(StringUtils.isNotEmpty(params.getUsername())){
            wrapper.eq("username",params.getUsername());
        }
        if(params.getLogType()!=null){
            wrapper.eq("log_type",params.getLogType().name());
        }
        if(super.getLoginUser(request).getHostOrg()!=null) {
            wrapper.eq("host_org_id", super.getLoginUser(request).getHostOrg().getId());
        }
        wrapper.orderBy("create_time",false);
        return systemLogService.selectPage(params, wrapper);
    }
}
