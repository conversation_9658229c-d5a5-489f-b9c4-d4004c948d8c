package com.xunw.zjxx.api.openapi.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.CacheHelper;
import com.xunw.zjxx.common.utils.Constants;
import com.xunw.zjxx.common.utils.JWTUtils;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.base.LoginUser;
import com.xunw.zjxx.module.enums.Gender;
import com.xunw.zjxx.module.enums.Status;
import com.xunw.zjxx.module.sys.dto.UserAddReqParams;
import com.xunw.zjxx.module.sys.entity.*;
import com.xunw.zjxx.module.sys.service.OrgService;
import com.xunw.zjxx.module.sys.service.SettingService;
import com.xunw.zjxx.module.sys.service.StudentInfoService;
import com.xunw.zjxx.module.sys.service.UserInfoService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 教育考试系统接口对接
 */
@RestController
public class OpenApiController extends BaseController {

    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private SettingService settingService;

    @RequestMapping("/htgl/openapi/sysUser/sso")
    @Operation(desc = "管理用户单点登录", loginRequired = false)
    public Object resetSysUserPassword(HttpServletRequest request,
                                       UserAddReqParams params) throws Exception {
        if(StringUtils.isEmpty(params.getId())){
            throw BizException.withMessage("ID不能够为空");
        }
        if (StringUtils.isEmpty(params.getRoleId())) {
            throw BizException.withMessage("用户角色不能为空");
        }
        User user = userInfoService.getUserByOpenId(params.getId());
        if (user == null) {
            String password = Constants.DEFAULT_PASSWORD;
            params.setPassword(password);
            params.setUsername(params.getId());
            params.setMobile(params.getPhone());
            params.setSfzh(params.getId());
            params.setOpenId(params.getId());
            user = userInfoService.add(params, super.getCurrentHostOrgId(request), null);
        } else {
            user.setMobile(params.getPhone());
            user.setEmail(params.getEmail());
            userInfoService.updateUser(user);
        }
        return this.cacheUser(user, super.getCurrentHostOrgId(request));
    }

    /**
     *
     * @param request
     * @param name
     * @param id  身份证号/openId
     * @param phone
     * @param email
     * @return
     * @throws Exception
     */
    @RequestMapping("/portal/openapi/student/sso")
    @Operation(desc = "学员用户单点登录", loginRequired = false)
    public Object resetSysUserPassword(HttpServletRequest request,
                                       @RequestParam(required = false) String name,
                                       @RequestParam(required = false) String id,
                                       @RequestParam(required = false) String phone,
                                       @RequestParam(required = false) String email) throws Exception {
        if(StringUtils.isEmpty(name)){
            throw BizException.withMessage("请输入姓名");
        }
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("ID不能够为空");
        }
        // 先用openid查询
        StudentInfo studentInfo = studentInfoService.getByOpenId(id);
        if (studentInfo == null) {
            String password = Constants.DEFAULT_PASSWORD;
            studentInfo = new StudentInfo();
            studentInfo.setName(name);
            studentInfo.setSfzh(id);
            studentInfo.setGender(Gender.OTHER);
            studentInfo.setMobile(phone);
            studentInfo.setPhone(phone);
            studentInfo.setStatus(Status.OK);
            studentInfo.setEmail(email);
            studentInfo.setOpenId(id);
            studentInfo.setCreateTime(new Date());
            studentInfo.setPassword(DigestUtils.md5Hex(password));
            studentInfo.setHostOrgId(super.getCurrentHostOrgId(request));
            studentInfoService.registStudent(studentInfo);
        } else {
            studentInfo.setName(name);
            studentInfo.setEmail(email);
            studentInfo.setPhone(phone);
            studentInfo.setMobile(phone);
            studentInfoService.updateAllColumnById(studentInfo);
        }
        return this.cacheStudent(studentInfo);
    }

    private Map<String, Object> cacheUser(User user, String hostOrgId) {
        Map<String, Object> userMap = userInfoService.getUserByUserName(user.getUsername(), hostOrgId);
        if (Status.valueOf(BaseUtil.getStringValueFromMap(userMap, "status")) != Status.OK) {
            throw BizException.withMessage("账号状态异常(账号被禁用或者被注销)，无法登录");
        }
        //查询用户的角色
        String userId = BaseUtil.getStringValueFromMap(userMap, "id");
        String orgId = BaseUtil.getStringValueFromMap(userMap, "orgId");
        String userType = BaseUtil.getStringValueFromMap(userMap, "userType");
        List<Role> roles = userInfoService.getRolesByUserId(userId);
        Org org = null;
        if (StringUtils.isNotEmpty(orgId)) {
            org = orgService.selectById(orgId);
        }
        Org hostOrg = null;
        if (StringUtils.isNotEmpty(hostOrgId)) {
            hostOrg = orgService.selectById(hostOrgId);
        }
        //生成用户的token
        String token = JWTUtils.sign(userId, user.getUsername(), UserTypeEnum.valueOf(userType), Constants.CURRENT_APPID);
        LoginUser loginUser = new LoginUser();
        loginUser.setId(userId);
        loginUser.setUsername((String)userMap.get("username"));
        loginUser.setName((String)userMap.get("name"));
        loginUser.setUserType(UserTypeEnum.ADMIN);
        loginUser.setOrg(org);
        loginUser.setHostOrg(hostOrg);
        loginUser.setRoles(roles);
        if (loginUser.getRoles()!=null && !loginUser.getRoles().isEmpty()){
            loginUser.setRole(loginUser.getRoles().get(0));
        }
        CacheHelper.setCache(Constants.AUTH.USER_TOKEN_CACHE, token, loginUser, Constants.JWT_TOKEN_EXPIRED_TIME_IN_HOUR * 3600 * 1000);
        //构造返回值
        Map<String, Object> result = new HashMap<>();
        result.put("id", loginUser.getId());
        result.put("name", loginUser.getName());
        result.put("username", loginUser.getUsername());
        result.put("roles", loginUser.getRoles());
        result.put("hostOrg", loginUser.getHostOrg());
        result.put("token", token);
        return result;
    }

    private Map<String, Object> cacheStudent(StudentInfo studentInfo) {
        if (studentInfo.getStatus() == Status.BLOCK) {
            throw BizException.withMessage("账号被禁用");
        }
        String username = studentInfo.getSfzh() != null ? studentInfo.getSfzh() : studentInfo.getMobile();
        LoginUser loginUser = new LoginUser();
        loginUser.setId(studentInfo.getId());
        loginUser.setUsername(username);
        loginUser.setName(studentInfo.getName());
        loginUser.setUserType(UserTypeEnum.STUDENT);

        Org org = null;
        if (StringUtils.isNotEmpty(studentInfo.getOrgId())) {
            org = orgService.selectById(studentInfo.getOrgId());
        }
        Org hostOrg = null;
        if (StringUtils.isNotEmpty(studentInfo.getHostOrgId())) {
            hostOrg = orgService.selectById(studentInfo.getHostOrgId());
        }
        loginUser.setOrg(org);
        loginUser.setHostOrg(hostOrg);
        loginUser.setStudentInfo(studentInfo);
        String token = JWTUtils.sign(studentInfo.getId(), username, UserTypeEnum.STUDENT, Constants.CURRENT_APPID);
        CacheHelper.setCache(Constants.AUTH.USER_TOKEN_CACHE, token, loginUser, Constants.JWT_TOKEN_EXPIRED_TIME_IN_HOUR * 3600 * 1000);

        //获取系统设置
        EntityWrapper<Setting> wrapper = new EntityWrapper<>();
        wrapper.eq("host_org_id", hostOrg.getId());
        List<Setting> list = settingService.selectList(wrapper);

        //构造返回值
        Map<String, Object> result = new HashMap<>();
        result.put("id", loginUser.getId());
        result.put("name", loginUser.getName());
        result.put("username", loginUser.getUsername());
        result.put("student", studentInfo);
        result.put("hostOrg", loginUser.getHostOrg());
        result.put("token", token);
        result.put("systemSetting", CollectionUtils.isNotEmpty(list)? list.get(0): null);
        return result;
    }

    /**
     * 学员用户删除
     */
    @RequestMapping("/htgl/openapi/student/delete")
    @Operation(desc = "学员用户删除", loginRequired = false)
    public Object studentDelete(HttpServletRequest request,
                                @RequestParam(required = false) String openId) {
        if (BaseUtil.isEmpty(openId)) {
            throw BizException.withMessage("openId不能为空");
        }
        studentInfoService.deleteList((EntityWrapper<StudentInfo>) new EntityWrapper<StudentInfo>().eq("open_id", openId));
        return true;
    }
}
