package com.xunw.zjxx.api.sys.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.sys.entity.Role;
import com.xunw.zjxx.module.sys.service.RoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/htgl/sys/resource")
public class SysResourceController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SysResourceController.class);

    @Autowired
    private RoleService roleService;

    @RequestMapping("/list")
    @Operation(desc = "角色权限管理列表")
    public Object list(HttpServletRequest request) throws Exception {
        EntityWrapper<Role> wrapper = new EntityWrapper();
        return roleService.selectList(wrapper);
    }

    @RequestMapping("/resourceTree")
    @Operation(desc = "角色菜单权限提交")
    public Object resourceTree(HttpServletRequest request,
                               String roleId,
                               String[] codes) throws Exception {
        roleService.setPrivilegeByCode(roleId,String.join(",", codes));
        return true;
    }
}
