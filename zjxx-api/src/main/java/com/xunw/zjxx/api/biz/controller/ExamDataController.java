package com.xunw.zjxx.api.biz.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.xunw.zjxx.common.config.ExamServiceConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.entity.ExamData;
import com.xunw.zjxx.module.biz.params.ExamDataQueryParams;
import com.xunw.zjxx.module.biz.params.ExamMonitorQueryParams;
import com.xunw.zjxx.module.biz.service.ExamDataService;
import com.xunw.zjxx.module.enums.ExamStatus;
import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.service.CourseService;
import com.xunw.zjxx.module.rpc.ExamService.api.ExamServiceApi;
import com.xunw.zjxx.module.sys.entity.StudentInfo;
import com.xunw.zjxx.module.sys.entity.User;
import com.xunw.zjxx.module.sys.service.StudentInfoService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/htgl/biz/zypx/examdata/exam")
public class ExamDataController extends BaseController {

    @Autowired
    private ExamServiceConfig examServiceConfig;
    @Autowired
    private ExamServiceApi examServiceApi;
    @Autowired
    private ExamDataService examDataService;
    @Autowired
    private StudentInfoService studentInfoService;
    @Autowired
    private CourseService courseService;

    @RequestMapping("/done/list")
    @Operation(desc = "已做试卷列表")
    public Object doneList(HttpServletRequest request,
                           ExamDataQueryParams params) {
        Object data = examServiceApi.examList(super.getCurrentHostOrgId(request), null, params.getCourseId(),
                params.getCourseName(), params.getKeyword(), null, "EXAMINE", null, null,
                params.getStatus(), null, null, null, params.getCurrent(), params.getSize()).getData();
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
        List<com.alibaba.fastjson.JSONObject> records = jsonObject.getJSONArray("records").stream().map(x -> {
            JSONObject map = JSON.parseObject(JSON.toJSONString(x));
            String studentId = map.getString("studentId");
            String courseId = map.getString("courseId");
            StudentInfo studentInfo = studentInfoService.selectById(studentId);
            if(studentInfo != null){
                map.put("studentName", studentInfo.getName());
                map.put("sfzh", studentInfo.getSfzh());
                map.put("mobile", studentInfo.getMobile());
            }
            Course course = courseService.selectById(courseId);
            if(course!=null){
                map.put("courseName", course.getName());
            }
            return map;
        }).collect(Collectors.toList());
        jsonObject.put("records", records);
        return jsonObject;
    }

    @RequestMapping("/undo/list")
    @Operation(desc = "未做试卷列表")
    public Object undoList(HttpServletRequest request,
                           ExamDataQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        params.setToken(super.getAccessToken(request));
        return examDataService.undoList(params);
    }

    @RequestMapping("/undo/export")
    @Operation(desc = "导出未做记录")
    public void undoExport(HttpServletRequest request,
                           HttpServletResponse response,
                           ExamDataQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        params.setToken(super.getAccessToken(request));
        params.setSize(Integer.MAX_VALUE);
        params.setCurrent(1);
        response.setContentType("application/octet-stream");
        response.setHeader("content-disposition", "attachment;filename=undoExamData.xls");
        OutputStream os = null;
        try {
            os = response.getOutputStream();
            examDataService.undoExport(params, os);
            os.flush();
        } catch (Exception e) {

        } finally {
            if (os != null) {
                IOUtils.closeQuietly(os);
            }
        }
    }

    @RequestMapping("/done/getDetails")
    @Operation(desc = "获取答卷详情")
    public Object getDetails(HttpServletRequest request,
                             String dataId) throws Exception {
        if (StringUtils.isEmpty(dataId)) {
            throw BizException.withMessage("答卷id不能为空");
        }
        com.alibaba.fastjson.JSONObject data = JSON.parseObject(JSON.toJSONString(examServiceApi.examGetById(dataId).getData()));
        //包装学员信息
        String examDataId = data.getString("id");
        ExamData examData = examDataService.selectById(examDataId);
        List<StudentInfo> studentInfos = studentInfoService.selectList((EntityWrapper<StudentInfo>) new EntityWrapper<StudentInfo>()
                .eq("id", examData.getStudentId()));
        data.put("studentName", CollectionUtils.isNotEmpty(studentInfos) ? studentInfos.get(0).getName() : null);
        data.put("sfzh", CollectionUtils.isNotEmpty(studentInfos) ? studentInfos.get(0).getSfzh() : null);
        return data;
    }

    @RequestMapping("/done/reCheck")
    @Operation(desc = "重新批改试卷")
    public Object reCheck(HttpServletRequest request,
                          @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("答卷ID不能为空");
        }
        examDataService.reCheck(id, super.getAccessToken(request));
        return true;
    }

    @RequestMapping("/done/deleteById")
    @Operation(desc = "删除已做记录")
    public Object deleteById(HttpServletRequest request,
                             @RequestParam(required = false) String id) throws Exception {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("答卷ID不能为空");
        }
        examDataService.delete(id);
        return true;
    }

    @RequestMapping("/done/nextPaper")
    @Operation(desc = "下一张答卷")
    public Object nextPaper(HttpServletRequest request,
                            String paperId,
                            String dataId) throws Exception {
        if (StringUtils.isEmpty(paperId)) {
            throw BizException.withMessage("试卷id不能为空");
        }
        if (StringUtils.isEmpty(dataId)) {
            throw BizException.withMessage("答卷id不能为空");
        }
        return examServiceApi.examNext(dataId, paperId, ExamStatus.AUTOMATIC_REVISED).getData();
    }

    @RequestMapping("/monitor/list")
    @Operation(desc = "考试人脸比对记录")
    public Object monitorList(HttpServletRequest request,
                              ExamMonitorQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        params.setToken(super.getAccessToken(request));
        return examDataService.monitorList(params);
    }

    @RequestMapping("/revise")
    @Operation(desc = "批改试卷")
    public Object revise(HttpServletRequest request,
                         @RequestParam(required = false) String examId,
                         @RequestParam(required = false) String questionId,
                         @RequestParam(required = false) String score) throws Exception {
        if (StringUtils.isEmpty(examId)) {
            throw BizException.withMessage("答卷ID不能为空");
        }
        if (StringUtils.isEmpty(questionId)) {
            throw BizException.withMessage("试题id不能为空");
        }
        if (StringUtils.isEmpty(examId)) {
            throw BizException.withMessage("分数不能为空");
        }
        return examDataService.revise(examId, questionId, score, super.getAccessToken(request));
    }
}
