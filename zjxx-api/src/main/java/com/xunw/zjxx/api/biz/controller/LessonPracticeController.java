package com.xunw.zjxx.api.biz.controller;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.biz.service.LessonPracticeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 课件课时作业关系
 * <AUTHOR>
@RestController
@RequestMapping("/htgl/biz/lessonPractice")
public class LessonPracticeController extends BaseController {

    @Autowired
    private LessonPracticeService service;

    @RequestMapping("/save")
    @Operation(desc = "课件课时设置练习")
    public Object save(HttpServletRequest request,
                      @RequestParam(required = false) String id,
                      @RequestParam(required = false) String coursewareId,
                      @RequestParam(required = false) String lessonId,
                      @RequestParam(required = false) String paperIds) {
        if (StringUtils.isEmpty(coursewareId)) {
            throw BizException.withMessage("课件id不能为空");
        }
        if (StringUtils.isEmpty(lessonId)) {
            throw BizException.withMessage("课时id不能为空");
        }
        if (StringUtils.isEmpty(paperIds)) {
            throw BizException.withMessage("练习id不能为空");
        }
        service.save(id,coursewareId,lessonId,paperIds,super.getLoginUserId(request));
        return true;
    }

    @RequestMapping("/delete")
    @Operation(desc = "删除课件课时练习")
    public Object delete(HttpServletRequest request,
                       @RequestParam(required = false) String id) {
        if (StringUtils.isEmpty(id)) {
            throw BizException.withMessage("请选择要删除的练习");
        }
        service.deleteById(id);
        return true;
    }

    @RequestMapping("/getPracticesByLesson")
    @Operation(desc = "获取课时所有练习")
    public Object getPracticesByLesson(HttpServletRequest request,
                       @RequestParam(required = false) String lessonId) {
        if (StringUtils.isEmpty(lessonId)) {
            throw BizException.withMessage("课时id不能为空");
        }
        return service.getPracticesByLesson(lessonId, null, null);
    }

}
