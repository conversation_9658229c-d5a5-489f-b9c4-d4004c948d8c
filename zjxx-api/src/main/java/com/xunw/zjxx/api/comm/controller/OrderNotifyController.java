package com.xunw.zjxx.api.comm.controller;


import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.comm.pay.WeiXinPayUtils;
import com.xunw.zjxx.module.comm.service.CommOrderService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

/**
 * 支付的异步通知接口
 */
@RestController
@RequestMapping("/portal/order")
public class OrderNotifyController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderNotifyController.class);

    @Autowired
    private CommOrderService service;

    /**
     * 支付宝异步 通知接口
     */
    @RequestMapping(value = "/aliNotify")
    @Operation(desc = "支付异步通知接口", loginRequired = false)
    public Object aliNotify(HttpServletRequest request, HttpServletRequest response) throws Exception {
        // 获取支付宝POST过来反馈信息
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }
        // 商户订单号
        String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"), "UTF-8");
        // 支付宝交易号
        String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"), "UTF-8");
        // 交易状态
        String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"), "UTF-8");
        // 付款金额
        String total_amount = new String(request.getParameter("total_amount").getBytes("ISO-8859-1"), "UTF-8");
        params.put("out_trade_no", out_trade_no);
        params.put("trade_no", trade_no);
        params.put("trade_status", trade_status);
        params.put("total_amount", total_amount);

        LOGGER.info("* params: {}", params);
        service.processAliNotify(params);
        LOGGER.info("********************** 支付成功(支付宝异步通知) **********************");
        LOGGER.info("* 订单号: {}", out_trade_no);
        LOGGER.info("* 支付宝交易号: {}", trade_no);
        LOGGER.info("* 实付金额: {}", total_amount);
        LOGGER.info("***************************************************************");
        return "success";
    }

    /**
	 * 微信支付异步通知接口
	 */
	@RequestMapping("/wxNotify")
	@Operation(loginRequired=false, desc = "微信异步通知接口")
	public void wxNotify(HttpServletRequest request, HttpServletResponse response) {
		try {
			InputStream inputStream = request.getInputStream();// 从request中取得输入流
			Map<String, String>  notifyParamsMap = WeiXinPayUtils.parseXml(inputStream);
			LOGGER.info("微信支付异步回调信息,message:" + JSONObject.fromObject(notifyParamsMap).toString());
			service.processWxNotify(notifyParamsMap);
			response.setContentType("text/xml");
			StringBuffer messageBuffer = new StringBuffer();
			messageBuffer.append("<xml>");
			messageBuffer.append("<return_code><![CDATA[SUCCESS]]></return_code>");
			messageBuffer.append("<return_msg><![CDATA[OK]]></return_msg>");
			messageBuffer.append("</xml>");
			response.getWriter().print(messageBuffer.toString());
		} catch (Exception e) {
			LOGGER.error("处理微信支付异步通知异常:",e);
			StringBuffer messageBuffer = new StringBuffer();
			response.setContentType("text/xml");
			messageBuffer.append("<xml>");
			messageBuffer.append("<return_code><![CDATA[FAIL]]></return_code>");
			messageBuffer.append("<return_msg><![CDATA["+"专技学习管理平台服务端处理微信异步通知失败"+"]]></return_msg>");
			messageBuffer.append("</xml>");
			try {
				response.getWriter().print(messageBuffer.toString());
			} catch (IOException exp) {
				LOGGER.error("error:",exp);
			}
		}
	}

    /**
     * 建设银行异步 通知接口
     */
    @RequestMapping(value = "/ccbNotify")
    @Operation(desc = "建行异步通知接口", loginRequired = false)
    public Object ccbNotify(HttpServletRequest request, HttpServletRequest response) {
        try {
            Map<String, String> params = new HashMap<String, String>();
            Map<String, String[]> requestParams = request.getParameterMap();
            for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
                String name = (String) iter.next();
                String[] values = (String[]) requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, valueStr);
            }
            service.processCCBNotify(params);
        } catch (Exception e) {
            LOGGER.error("error:",e);
        }
        return "success";
    }

    /**
     * 工行聚合支付回调接口
     */
    @RequestMapping(value = "/icbcNotify")
    @Operation(desc = "工行聚合支付回调接口", loginRequired = false)
    public void icbcNotify(HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, String> params = new TreeMap<String, String>();
            Map<String, String[]> requestParams = request.getParameterMap();
            for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
                String name = (String) iter.next();
                String[] values = (String[]) requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, valueStr);
            }
            String requestUri = request.getRequestURI();
            service.processICBCNotify(params, requestUri, response);
        } catch (Throwable e) {
            LOGGER.error("error:",e);
        }
    }
}
