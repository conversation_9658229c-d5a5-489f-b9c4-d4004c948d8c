package com.xunw.zjxx.api.comm.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.module.base.BaseController;
import com.xunw.zjxx.module.comm.params.OrderQueryParams;
import com.xunw.zjxx.module.comm.service.CommOrderService;

@RestController
@RequestMapping("/htgl/comm/zypx/order")
public class OrderController extends BaseController {

    @Autowired
    private CommOrderService service;

    @RequestMapping("/list")
    @Operation(desc = "订单列表")
    public Object list(
            HttpServletRequest request,
            OrderQueryParams params) throws Exception {
        params.setHostOrgId(super.getCurrentHostOrgId(request));
        return service.pageQuery(params);
    }

    @RequestMapping("/deleteById")
    @Operation(desc = "删除订单", loginRequired = false)
    public Object deletePaper(HttpServletRequest request,
                              HttpServletResponse response,
                              String id) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择要删除的订单");
        }
        service.deleteById(id);
        return true;
    }

    @RequestMapping("/getById")
    @Operation(desc = "订单详情", loginRequired = false)
    public Object getById(HttpServletRequest request,
                          String id) throws Exception {
        if(StringUtils.isEmpty(id)){
            throw BizException.withMessage("请选择订单");
        }
        return service.orderDetail(id);
    }
}
