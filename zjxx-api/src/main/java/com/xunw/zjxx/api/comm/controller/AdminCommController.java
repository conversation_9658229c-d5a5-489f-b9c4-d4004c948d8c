package com.xunw.zjxx.api.comm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xunw.zjxx.common.config.AttConfig;
import com.xunw.zjxx.common.core.annotation.Operation;
import com.xunw.zjxx.common.exception.BizException;
import com.xunw.zjxx.common.utils.BaseUtil;
import com.xunw.zjxx.common.utils.CheckImageCode;
import com.xunw.zjxx.common.utils.FileHelper;
import com.xunw.zjxx.common.utils.QiniuZbConstants;
import com.xunw.zjxx.module.core.SmsSevice;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/htgl/")
public class AdminCommController {

    @Autowired
    private AttConfig attConfig;
    @Autowired
    private SmsSevice smsSevice;

    private static Set<String> IMAGE_FILE_ALLOWED_TYPES = null;
    private static Set<String> KJ_FILE_ALLOWED_TYPES = null;

    static {
        KJ_FILE_ALLOWED_TYPES = new HashSet<String>();
        KJ_FILE_ALLOWED_TYPES.add(".mp3");
        KJ_FILE_ALLOWED_TYPES.add(".mp4");
        KJ_FILE_ALLOWED_TYPES.add(".wmv");
        KJ_FILE_ALLOWED_TYPES.add(".rmvb");
        KJ_FILE_ALLOWED_TYPES.add(".flv");
        KJ_FILE_ALLOWED_TYPES.add(".mpeg");
        KJ_FILE_ALLOWED_TYPES.add(".wav");

        IMAGE_FILE_ALLOWED_TYPES = new HashSet<>();
        IMAGE_FILE_ALLOWED_TYPES.add(".jpg");
        IMAGE_FILE_ALLOWED_TYPES.add(".png");
        IMAGE_FILE_ALLOWED_TYPES.add(".jpeg");
        IMAGE_FILE_ALLOWED_TYPES.add(".gif");
        IMAGE_FILE_ALLOWED_TYPES.add(".bmp");
    }

    @RequestMapping("/imgCode/yzm")
    @Operation(desc = "获取图形验证码", loginRequired = false)
    public void getImgVerifyCode(HttpServletRequest request,
                                 HttpServletResponse response) throws Exception {
        response.setContentType("image/jpeg");//设置相应类型,告诉浏览器输出的内容为图片
        response.setHeader("Pragma", "No-cache");//设置响应头信息，告诉浏览器不要缓存此内容
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expire", 0);
        CheckImageCode checkImageCode = new CheckImageCode();
        checkImageCode.getRandcode(request, response);//输出验证码图片方法
    }
    
    @RequestMapping("/common/uploadImage")
    @Operation(desc = "图片上传不支持base64", loginRequired = false)
    public Object uploadImage(HttpServletRequest request,
                              @RequestParam("file") MultipartFile file) throws Exception {
        if(file== null){
            throw BizException.withMessage("在请求中没有检测到图片");
        }
        String ext = FileHelper.getExtension(file.getOriginalFilename());
        //检查文件类型是否被允许
        if(!isImageTypeAllowed(ext)) {
            throw BizException.withMessage("不允许上传:"+ext+"格式的图片文件");
        }
        String path = attConfig.getRootDir()+"/upload/images/"+new SimpleDateFormat("yyyyMMddHH").format(new Date());
        String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()+ext;
        return FileHelper.storeFile(path, file.getInputStream(), newFileName);
    }

    @RequestMapping("/common/uploadFile")
    @Operation(desc = "通用文件上传")
    public Object uploadFile(HttpServletRequest request,
                             @RequestParam("file") MultipartFile file) throws Exception {
        if (file == null) {
            throw BizException.withMessage("在请求中没有检测到文件");
        }
        String ext = FileHelper.getExtension(file.getOriginalFilename());
        String path = attConfig.getRootDir() + "/upload/files/" + new SimpleDateFormat("yyyyMMddHH").format(new Date());
        String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase() + ext;
        return FileHelper.storeFile(path, file.getInputStream(), newFileName);
    }

    @RequestMapping("/uploadBae64Image")
    @Operation(desc = "通用图片文件上传接口(仅支持64)")
    public Object uploadBae64Image(
            HttpServletRequest request, @RequestParam(required = false, value = "base64Image") String base64Image) throws Exception {
        if(StringUtils.isEmpty(base64Image)){
            throw BizException.withMessage("在请求中没有检测到Base64图片");
        }
        String url = FileHelper.convertImgBase64SrcStrToUrl(base64Image);
        return url;
    }

    /**
     * 图片文件类型是否合法
     * @param extname 文件后缀名: 如：.jpg
     * @return
     */
    public static boolean isImageTypeAllowed(String extname){
        for(String type : IMAGE_FILE_ALLOWED_TYPES){
            if(type.trim().equalsIgnoreCase(extname))
                return true;
        }
        return false;
    }

    @RequestMapping("/common/sendSms")
    @Operation(desc = "短信通知", loginRequired = false)
    public Object sendSms(HttpServletRequest request,
                          @RequestParam(required = false) String mobiles,
                          @RequestParam(required = false) String message) throws Exception {
        if(StringUtils.isEmpty(mobiles)){
            throw BizException.withMessage("请输入手机号");
        }
        if(StringUtils.isEmpty(message)){
            throw BizException.withMessage("请输入短信内容");
        }
        smsSevice.send("292818",null, mobiles.split(","), new String[]{message});
        return true;
    }

    @RequestMapping("/uploadKj")
    @Operation(desc = "课件上传接口(不支持Base64)")
    public Object uploadKj(
            HttpServletRequest request, @RequestParam(required = false,value = "file") MultipartFile file) throws Exception {
        if(file== null){
            throw BizException.withMessage("在请求中没有检测到文件");
        }
        if(file.getBytes().length == 0) {
            throw BizException.withMessage("不能够上传空文件");
        }
        String ext = FileHelper.getExtension(file.getOriginalFilename());
        //检查文件类型是否被允许
        if(!isKjFileTypeAllowed(ext)) {
            throw BizException.withMessage("不允许上传:"+ext+"格式的课件文件");
        }
        Map<String,Object> rst = new HashMap<>();
        String path = attConfig.getRootDir()+"/upload/courseware/"+new SimpleDateFormat("yyyyMMddHH").format(new Date());
        String newFileName = UUID.randomUUID().toString().replace("-", "").toUpperCase()+ext;
        String url = FileHelper.storeFile(path, file.getBytes(), newFileName);
        if("CLOUD".equals(attConfig.getStoreType())) {
            int seconds = getKjVideoDuration(url);//获取课件的时长
            int minutes = seconds / 60;
            if (minutes < 1) {
                throw BizException.withMessage("视频时长过短");
            }
            rst.put("minutes", minutes);
        }
        rst.put("name", file.getOriginalFilename());
        rst.put("url", url);
        return rst;
    }

    /**
     * 课件文件类型是否合法
     * @param extname 文件后缀名: 如：.mp4
     * @return
     */
    public static boolean isKjFileTypeAllowed(String extname){
        for(String type : KJ_FILE_ALLOWED_TYPES){
            if(type.trim().equalsIgnoreCase(extname))
                return true;
        }
        return false;
    }

    private int getKjVideoDuration(String url) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        // 创建Get请求
        HttpGet httpGet = new HttpGet(url + QiniuZbConstants.QINIU_REVIEW_URLSUFFIX);
        // 响应模型
        CloseableHttpResponse response = null;
        try {
            // 由客户端执行(发送)Get请求
            response = httpClient.execute(httpGet);
            // 配置信息
            RequestConfig requestConfig = RequestConfig.custom()
                    // 设置连接超时时间(单位毫秒)
                    .setConnectTimeout(5000)
                    // 设置请求超时时间(单位毫秒)
                    .setConnectionRequestTimeout(5000)
                    // socket读写超时时间(单位毫秒)
                    .setSocketTimeout(5000)
                    // 设置是否允许重定向(默认为true)
                    .setRedirectsEnabled(true).build();

            // 将上面的配置信息 运用到这个Get请求里
            httpGet.setConfig(requestConfig);

            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();
            System.out.println("响应状态为:" + response.getStatusLine());
            if (responseEntity != null) {
                String str = EntityUtils.toString(responseEntity);
                long len = responseEntity.getContentLength();
                System.out.println("响应内容长度为:" + responseEntity.getContentLength());
                if (len <= 30 && len != -1) {
                    System.out.println("响应内容为:" + str);
                }
                JSONObject parseObject = JSON.parseObject(str);
                JSONObject format = (JSONObject) parseObject.get("format");
                if (BaseUtil.isNotEmpty(format)) {
                    return BaseUtil.getInt(format.get("duration"));
                } else {
                    return 0;
                }
            }
        } catch (ClientProtocolException e) {
            e.printStackTrace();
        } catch (ParseException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return 0;
    }
}
