gateway.url=http://172.26.179.193:59010
login-service.login=/loginService/api/user/login

user-auth-service.findPrivilegeByRoleId=/userAuthService/api/role/findPrivilegeByRoleId
user-auth-service.roleList=/userAuthService/api/role/list
user-auth-service.orgList=/userAuthService/api/org/list
user-auth-service.orgAdd=/userAuthService/api/org/add
user-auth-service.orgEdit=/userAuthService/api/org/edit
user-auth-service.orgDelete=/userAuthService/api/org/delete
user-auth-service.orgTree=/userAuthService/api/org/tree
user-auth-service.orgSelect=/userAuthService/api/org/select
user-auth-service.orgGetById=/userAuthService/api/org/getById
user-auth-service.orgGetHostOrgByDomain=/userAuthService/api/org/getHostOrgByDomain
user-auth-service.relatedOrgSelect=/userAuthService/api/relatedOrg/select/{hostOrgId}
user-auth-service.relatedOrgList=/userAuthService/api/relatedOrg/list/{hostOrgId}
user-auth-service.relatedOrgTee=/userAuthService/api/relatedOrg/tree/{hostOrgId}
user-auth-service.addOrgRelation=/userAuthService/api/relatedOrg/add
user-auth-service.getTopOrg=/userAuthService/api/org/getTopOrg
user-auth-service.userAdd=/userAuthService/api/user/add
user-auth-service.userSetRole=/userAuthService/api/user/setRole
user-auth-service.userGetById=/userAuthService/api/user/getById
user-auth-service.userUpdate=/userAuthService/api/user/update
user-auth-service.userList=/userAuthService/api/user/list
user-auth-service.userDelete=/userAuthService/api/user/delete
#\u6e05\u9664\u7528\u6237\u89d2\u8272
user-auth-service.deleteUserRole=/userAuthService/api/user/deleteUserRole
user-auth-service.resetPassword=/userAuthService/api/user/resetPassword
user-auth-service.findRoleByUserId=/userAuthService/api/user/findRolesByUserId
user-auth-service.setPrivilege=/userAuthService/api/role/setPrivilege
user-auth-service.setPrivilegeByCode=/userAuthService/api/role/setPrivilegeByCode

learn-service.coursewareList=/learningCoursewareService/api/courseware/list
learn-service.studyGetLearn=/learningCoursewareService/api/study/getLearn
learn-service.getById=/learningCoursewareService/api/courseware/getById
learn-service.getEnableCoursewareByCourseId=/learningCoursewareService/api/courseware/getEnableCoursewareByCourseId
learn-service.add=/learningCoursewareService/api/courseware/add
learn-service.addStudyInfo=/learningCoursewareService/api/courseware/addStudyInfo
learn-service.edit=/learningCoursewareService/api/courseware/edit
learn-service.remove=/learningCoursewareService/api/courseware/remove
learn-service.addLearn=/learningCoursewareService/api/study/addLearn
learn-service.addNotes=/learningCoursewareService/api/study/addLearnNote
learn-service.getLearnNotes=/learningCoursewareService/api/study/getLearnNotes
learn-service.deleteLearnNote=/learningCoursewareService/api/study/deleteLearnNote
learn-service.addLearnComment=/learningCoursewareService/api/study/addLearnComment
learn-service.getLearnComments=/learningCoursewareService/api/study/getLearnComments
learn-service.getStudyMaterials=/learningCoursewareService/api/courseware/getStudyMaterials
learn-service.removeStudyMaterial=/learningCoursewareService/api/courseware/removeStudyMaterial
learn-service.addExtConfig=/learningCoursewareService/api/courseware/addExtConfig
learn-service.removeExtConfig=/learningCoursewareService/api/courseware/removeExtConfig
learn-service.addPlayQues=/learningCoursewareService/api/study/addPlayQues
learn-service.CoursewareProgress=/learningCoursewareService/api/study/getCoursewareProgress
learn-service.CoursewareImport=/learningCoursewareService/api/courseware/import

exam-service.questionDbSelect=/examService/api/questionDb/select
exam-service.questionDbList=/examService/api/questionDb/list
exam-service.questionDbAdd=/examService/api/questionDb/add
exam-service.questionDbDelete=/examService/api/questionDb/delete
exam-service.questionDbEdit=/examService/api/questionDb/update
exam-service.questionDbViewQues=/examService/api/questionDb/preview
exam-service.questionDbQuestionPreview=/examService/api/questionDb/questionPreview
exam-service.questionCount=/examService/api/question/count
exam-service.questionList=/examService/api/question/list
exam-service.questionAdd=/examService/api/question/add
exam-service.questionEdit=/examService/api/question/edit
exam-service.questionGetChildByPid=/examService/api/question/getChildByPid
exam-service.questionSelectRealType=/examService/api/question/selectRealType
exam-service.questionGetQuesDetailsById=/examService/api/question/preview
exam-service.questionDeleteById=/examService/api/question/delete
exam-service.questionEditStatus=/examService/api/question/updateStatus
exam-service.questionImport=/examService/api/question/import
exam-service.questionDbGetRandomObjectiveQuestion=/examService/api/questionDb/getRandomObjectiveQuestion
exam-service.paperList=/examService/api/paper/list
exam-service.paperUpdate=/examService/api/paper/update
exam-service.paperDelete=/examService/api/paper/delete
exam-service.paperPackage=/examService/api/paper/package
exam-service.paperGetById=/examService/api/paper/getById
exam-service.paperAdd=/examService/api/paper/add
exam-service.paperAddAndAuto=/examService/api/paper/addAndAuto
exam-service.paperRepoList=/examService/api/paperRepo/list
exam-service.paperRepoGetPaperDetailById=/examService/api/paperRepo/getById
exam-service.paperRepoEditPaper=/examService/api/paperRepo/update
exam-service.paperRepoDeletePaper=/examService/api/paperRepo/delete
exam-service.paperRepoAutoPackage=/examService/api/paperRepo/autoPackage
exam-service.paperRepoPackage=/examService/api/paperRepo/package
exam-service.examList=/examService/api/exam/list
exam-service.studentExamList=/examService/api/student/paper/list
exam-service.revisereset=/examService/api/exam/revisereset
exam-service.revise=/examService/api/exam/revise
#\u8003\u8bd5\u5f00\u59cb\u7ee7\u7eed
exam-service.examStart=/examService/api/student/exam/start
#\u8003\u8bd5\u8be6\u60c5
exam-service.examDetail=/examService/api/student/exam/detail
#\u8003\u8bd5\u4e0a\u4f20\u7b54\u9898\u5361
exam-service.examUploadCard=/examService/api/student/exam/uploadCard
#\u8003\u8bd5\u4fdd\u5b58\u4f5c\u7b54\u8bb0\u5f55
exam-service.examSave=/examService/api/student/examSave
#\u7b54\u5377\u8be6\u60c5
exam-service.examGetById=/examService/api/exam/getById
#\u5220\u9664\u7b54\u5377
exam-service.examDelete=/examService/api/exam/delete
#\u4e0b\u4e00\u5f20\u7b54\u5377
exam-service.examNext=/examService/api/exam/next
exam-service.examSubmit=/examService/api/student/examSubmit
exam-service.autoSubmit=/examService/api/student/autoSubmit
exam-service.redoExam=/examService/api/student/redoExam
#\u5b66\u4e60\u6293\u62cd\u8bb0\u5f55

face-service.captureLog=/faceService/api/learn/capture/log
#\u8003\u8bd5\u6bd4\u5bf9\u5217\u8868
face-service.examVerifyLog=/faceService/api/exam/verify/log
#\u8bfe\u7a0b\u5b66\u4e60\u6bd4\u5bf9\u8bb0\u5f55
face-service.learnVerifyLog=/faceService/api/learn/verify/log
face-service.examCapture=/faceService/api/exam/capture
face-service.learnCapture=/faceService/api/learn/capture
face-service.examVerify=/faceService/api/exam/verify
face-service.learnVerify=/faceService/api/learn/verify

basic-service.courseAdd=/basicService/api/course/add
basic-service.courseList=/basicService/api/course/list
basic-service.courseUpdate=/basicService/api/course/update
basic-service.courseDelete=/basicService/api/course/delete
basic-service.courseGetById=/basicService/api/course/getById
