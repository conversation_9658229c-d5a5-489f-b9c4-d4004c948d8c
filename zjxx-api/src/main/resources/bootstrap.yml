spring:
  application:
    name: xxtx-jxks-service # 应用名称也是构成 Nacos 配置管理 dataId 字段的一部分 (当 config.prefix 为空时)
  cloud:
    # Nacos客户端
    nacos:
      discovery:
        # Nacos
        server-addr: **************:8848
        namespace: micro-dm8 #命名空间为nacos配置的，自动生成或手动配置的
        group: DEFAULT_GROUP
        username: nacos
        password: nacos
      config:
        enabled: false
management:
  health:
    redis:
      enabled: false #关闭redis健康检查
    sentinel:
      enabled: false #关闭sentinel健康检查
    ldap:
      enabled: false #关闭ldap健康检查
  #暴露端点
  endpoints:
    web:
      exposure:
        include: '*'
    #关闭过滤敏感信息
    health:
      sensitive: false
  endpoint:
    health:
      show-details: always  #显示详细信息
feign:
  client:
    config:
      default:
        connectTimeout: 180000 # 建立连接超时时间
        readTimeout: 180000   # 读取资源超时时间
#seata配置
seata:
  enabled: true
  enable-auto-data-source-proxy: true
  tx-service-group: zjxx_tx_group
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: **************:8848
      group: DEFAULT_GROUP
      username: nacos
      password: nacos
      namespace: micro-dm8
  config:
    type: file
  #    nacos:
  #      server-addr: localhost:8848
  #      group: SEATA_GROUP
  #      username: nacos
  #      password: nacos
  #      namespace: e5a9f3fc-cfd5-48d9-9f80-a5fb9ede58d3
  service:
    vgroup-mapping:
      zjxx_tx_group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
