<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
	<contextName>logback</contextName>
	<!--输出到控制台 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}| %thread | %level | %X{TRACE_ID} - %X{KEY} | %m | [%class:%line]%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<!--按天生成日志 -->
	<appender name="logFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>logs/zjxx-server.log</file>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}| %thread | %level | %X{TRACE_ID}
				- %X{KEY} | %m | [%class:%line]%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<rollingPolicy
			class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>logs/zjxx-server.log.%d{yyyy-MM-dd}.%i.log
			</fileNamePattern>
			<maxHistory>100</maxHistory>
			<maxFileSize>100MB</maxFileSize>
		</rollingPolicy>
	</appender>
	
	<logger name="org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainer" level="INFO" additivity="false">
		<appender-ref ref="console" />
		<appender-ref ref="logFile" />
	</logger>

	<root level="INFO">
		<appender-ref ref="console" />
		<appender-ref ref="logFile" />
	</root>
	
</configuration>