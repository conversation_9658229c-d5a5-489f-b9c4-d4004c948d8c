server:
  port: 59033
file:
  encoding: UTF-8
sun:
  jnu:
    encoding: UTF-8
spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 1024MB
      max-request-size: 1024MB
  jackson: # 格式化返回时间 yyyy-MM-dd HH:mm:ss
    date-format: yyyy-MM-dd HH:mm:ss 
    time-zone: GMT+8
  aop:
    proxy-target-class: true
  ####文件上传
  http:
    encoding:
      charset: UTF-8
  redis:
    database: 0
    host: **************
    port: 6379
    password: sebms_study
    timeout: 180000
    pool:
      max-active: 500
      max-wait: -1
      max-idle: 100
      min-idle: 20
#mysql数据库配置
  datasource:
#    driverClassName: com.mysql.jdbc.Driver
#    url: **********************************************************************************************************************************************
#    username: root
#    password: xunw2020
    driverClassName: dm.jdbc.driver.DmDriver
    url: jdbc:dm://**************:5236?databaseName=zjxx_db&autoReconnect=true&useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
    username: zjxx_db
    password: Zjxx!@2025
# mybatis-plus配置文件
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    jdbc-type-for-null: null
    call-setters-on-nulls: true
    # 如果不需要打印SQL请注释此行代码
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    id-type: 1
  typeEnumsPackage: com.xunw.zjxx.module.enums
  typeAliasesPackage: com.xunw.zjxx.module.inf.entity,com.xunw.zjxx.module.sys.entity,com.xunw.zjxx.module.comm.entity,com.xunw.zjxx.module.biz.entity
  mapper-locations: classpath:/mapper/*.xml

logging:
  config: classpath:logback-spring.xml

att:
  root-dir: zjxx_att
  store-dir: d:/Documents/D_SOFT/STORE
  store-type: CLOUD
  tempdir: d:/Documents/D_SOFT/TEMP
  url-prefix: http://jxjy-att.whxunw.com/
qiniu:
  access-Key: eO32A7yxJWpO6BAu65L4sykwUTYRfshEZ2uWhDod
  bucket: jxjy-study
  imageMogr2-rotate: ?imageMogr2/rotate/
  secret-key: yyJWIpNz6kgbYLrtnoQhUb2g4mXcMh6Nzsx77cab
  urlprefix: http://jxjy-att.whxunw.com/
  zbhfprefix: JXJY
sms:
  key: KMaYrOIiFV6PgCSf
  secret: xiFKt2cUzVJapyPFquaLuFzDH8AI5EmE
system:
  app-id: zjxx
  openapi-secret-key: sjk8AukLp
