package com.xunw.zjxx;

import java.util.List;

import com.xunw.zjxx.module.biz.service.CertiApproveService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.xunw.zjxx.module.inf.entity.Course;
import com.xunw.zjxx.module.inf.entity.Type;
import com.xunw.zjxx.module.inf.service.CourseService;
import com.xunw.zjxx.module.inf.service.TypeService;

@SpringBootTest
@RunWith(SpringRunner.class)
public class TypeAndCourseTest {

    @Autowired
    TypeService typeService;
    @Autowired
    CourseService courseService;
    @Autowired
    CertiApproveService certiApproveService;

    @Test
    public void typeTest() {
        List<Type> allTypes = typeService.getAllTypes("1", "1");
        System.out.println(allTypes);
    }

    @Test
    public void courseTest() {
        List<Course> allCourse = courseService.getAllCourse("1", "1");
        System.out.println(allCourse);
    }

    @Test
    public void getTopType() {
//        Type topType = typeService.getTopType("12");
//        System.out.println(topType.toString());
    }

    @Test
    public void certiDetail() {
        Object o = certiApproveService.certiDetail("5B6C3FC3F3AE4B0FB8614139F1894846");
        System.out.println(o);
    }
}
