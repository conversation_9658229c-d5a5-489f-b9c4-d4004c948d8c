package com.xunw.zjxx;

import com.xunw.zjxx.common.config.SystemConfig;
import com.xunw.zjxx.common.enums.UserTypeEnum;
import com.xunw.zjxx.common.utils.JWTUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class createtokentest {

    @Test
    public void signAdmin() {
        System.out.println(JWTUtils.sign("1", "test", UserTypeEnum.ADMIN, SystemConfig.getAppId()));
    }

    @Test
    public void signStudent() {
        System.out.println(JWTUtils.sign("1", "test", UserTypeEnum.STUDENT, SystemConfig.getAppId()));
    }
}
